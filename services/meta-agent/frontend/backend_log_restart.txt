Starting backend server...
Requirement already satisfied: aiosqlite in ./venv/lib/python3.12/site-packages (0.21.0)
Requirement already satisfied: structlog in ./venv/lib/python3.12/site-packages (25.4.0)
Requirement already satisfied: python-dotenv in ./venv/lib/python3.12/site-packages (1.1.1)
Requirement already satisfied: aiofiles in ./venv/lib/python3.12/site-packages (24.1.0)
Collecting jsonschema
  Using cached jsonschema-4.25.0-py3-none-any.whl.metadata (7.7 kB)
Requirement already satisfied: typing_extensions>=4.0 in ./venv/lib/python3.12/site-packages (from aiosqlite) (4.14.1)
Requirement already satisfied: attrs>=22.2.0 in ./venv/lib/python3.12/site-packages (from jsonschema) (25.3.0)
Collecting jsonschema-specifications>=2023.03.6 (from jsonschema)
  Using cached jsonschema_specifications-2025.4.1-py3-none-any.whl.metadata (2.9 kB)
Collecting referencing>=0.28.4 (from jsonschema)
  Using cached referencing-0.36.2-py3-none-any.whl.metadata (2.8 kB)
Collecting rpds-py>=0.7.1 (from jsonschema)
  Using cached rpds_py-0.26.0-cp312-cp312-macosx_11_0_arm64.whl.metadata (4.2 kB)
Using cached jsonschema-4.25.0-py3-none-any.whl (89 kB)
Using cached jsonschema_specifications-2025.4.1-py3-none-any.whl (18 kB)
Using cached referencing-0.36.2-py3-none-any.whl (26 kB)
Using cached rpds_py-0.26.0-cp312-cp312-macosx_11_0_arm64.whl (350 kB)
Installing collected packages: rpds-py, referencing, jsonschema-specifications, jsonschema

Successfully installed jsonschema-4.25.0 jsonschema-specifications-2025.4.1 referencing-0.36.2 rpds-py-0.26.0
INFO:     Will watch for changes in these directories: ['/Users/<USER>/metaspaces/mono/services/meta-agent/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [11310] using StatReload
2025-07-27 15:25:36 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:25:36 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:25:36 [info     ] Docker client initialized successfully
2025-07-27 15:25:36 [info     ] RBAC service initialized
2025-07-27 15:25:36 [info     ] Initialized OAuth provider: google
2025-07-27 15:25:36 [info     ] Initialized OAuth provider: github
2025-07-27 15:25:36 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:25:36 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:25:36 [info     ] MFA service initialized
2025-07-27 15:25:36 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:25:36 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 20, in <module>
    from api.ai_assistant import router as ai_assistant_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/ai_assistant.py", line 11, in <module>
    from services.ai_service import ai_service, AIRequest, AICapability, AIProvider
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/ai_service.py", line 14, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google.generativeai'
WARNING:  StatReload detected changes in 'src/services/ai_service.py'. Reloading...
2025-07-27 15:26:07 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:26:07 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:26:07 [info     ] Docker client initialized successfully
2025-07-27 15:26:07 [info     ] RBAC service initialized
2025-07-27 15:26:08 [info     ] Initialized OAuth provider: google
2025-07-27 15:26:08 [info     ] Initialized OAuth provider: github
2025-07-27 15:26:08 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:26:08 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:26:08 [info     ] MFA service initialized
2025-07-27 15:26:08 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:26:08 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 23, in <module>
    from langflow.api import router as langflow_router, health_check_router, log_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/langflow/api/__init__.py", line 1, in <module>
    from langflow.api.health_check_router import health_check_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/langflow/api/health_check_router.py", line 4, in <module>
    from loguru import logger
ModuleNotFoundError: No module named 'loguru'
WARNING:  StatReload detected changes in 'src/services/ai_service.py'. Reloading...
2025-07-27 15:26:41 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:26:41 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:26:41 [info     ] Docker client initialized successfully
2025-07-27 15:26:41 [info     ] RBAC service initialized
2025-07-27 15:26:41 [info     ] Initialized OAuth provider: google
2025-07-27 15:26:41 [info     ] Initialized OAuth provider: github
2025-07-27 15:26:41 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:26:41 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:26:41 [info     ] MFA service initialized
2025-07-27 15:26:41 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:26:41 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 23, in <module>
    from langflow.api import router as langflow_router, health_check_router, log_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/langflow/api/__init__.py", line 1, in <module>
    from langflow.api.health_check_router import health_check_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/langflow/api/health_check_router.py", line 4, in <module>
    from loguru import logger
ModuleNotFoundError: No module named 'loguru'
INFO:     Stopping reloader process [11310]
