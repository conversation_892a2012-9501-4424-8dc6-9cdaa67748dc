#!/usr/bin/env python3
import subprocess
import sys
import os

print("=== Backend Debug Script ===\n")

# Change to backend directory
backend_dir = os.path.abspath('../backend')
os.chdir(backend_dir)
print(f"Changed to directory: {os.getcwd()}")

# Activate venv and run a simple import test
print("\n1. Testing Python environment and imports...")
test_code = """
import sys
print(f"Python: {sys.version}")
print(f"Python path: {sys.path[0]}")

try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✓ dotenv loaded")
except Exception as e:
    print(f"✗ dotenv error: {e}")

try:
    from src.config.settings import settings
    print(f"✓ Settings loaded - Port: {settings.port}")
except Exception as e:
    print(f"✗ Settings error: {e}")

try:
    from src.database.connection import engine
    print("✓ Database engine created")
except Exception as e:
    print(f"✗ Database error: {e}")

try:
    from src.main import app
    print("✓ FastAPI app imported")
except Exception as e:
    print(f"✗ Main app error: {e}")
    import traceback
    traceback.print_exc()
"""

# Run the test in venv
result = subprocess.run(
    ["bash", "-c", "source venv/bin/activate && python3 -c '{}'".format(test_code)],
    capture_output=True,
    text=True
)

print(result.stdout)
if result.stderr:
    print("STDERR:", result.stderr)

print("\n2. Checking installed packages...")
result = subprocess.run(
    ["bash", "-c", "source venv/bin/activate && pip list | grep -E 'fastapi|uvicorn|pydantic|sqlalchemy|aiosqlite'"],
    capture_output=True,
    text=True
)
print(result.stdout)

print("\n3. Checking for missing imports...")
# Check if all required packages are importable
packages = ['fastapi', 'uvicorn', 'pydantic', 'sqlalchemy', 'aiosqlite', 'structlog', 'dotenv']
for pkg in packages:
    result = subprocess.run(
        ["bash", "-c", f"source venv/bin/activate && python3 -c 'import {pkg}' 2>&1"],
        capture_output=True,
        text=True
    )
    if result.returncode == 0:
        print(f"✓ {pkg}")
    else:
        print(f"✗ {pkg}: {result.stdout.strip()}")