import { Routes, Route, Navigate } from 'react-router-dom'
import { Providers } from '@/components/providers'

// Import pages
import HomePage from '@/pages/HomePage'
import DashboardPage from '@/pages/DashboardPage'
import AgentsPage from '@/pages/AgentsPage'
import AgentDetailPage from '@/pages/AgentDetailPage'
import CreateAgentPage from '@/pages/CreateAgentPage'
import TasksPage from '@/pages/TasksPage'
import TaskDetailPage from '@/pages/TaskDetailPage'
import CreateTaskPage from '@/pages/CreateTaskPage'
import WorkflowsPage from '@/pages/WorkflowsPage'
import CreateWorkflowPage from '@/pages/CreateWorkflowPage'
import WorkflowEditorPage from '@/pages/WorkflowEditorPage'
import AutomationsPage from '@/pages/AutomationsPage'
import OrchestrationsPage from '@/pages/OrchestrationsPage'
import ConnectionsPage from '@/pages/ConnectionsPage'
import IntelligencePage from '@/pages/IntelligencePage'
import SettingsPage from '@/pages/SettingsPage'
import GeneratorPage from '@/pages/GeneratorPage'
import MigrationPage from '@/pages/MigrationPage'
import TestPage from '@/pages/TestPage'
import TestEmbeddedPage from '@/pages/TestEmbeddedPage'
import DeployedAgentPage from '@/pages/DeployedAgentPage'
import WorkflowSystem from '@/workflow'

// Guest access - no authentication required
function GuestRoute({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}

function App() {
  return (
    <Providers>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route
          path="/dashboard"
          element={<DashboardPage />}
        />
        <Route path="/agents" element={<AgentsPage />} />
        <Route path="/agents/create" element={<CreateAgentPage />} />
        <Route path="/agents/:id" element={<AgentDetailPage />} />
        <Route path="/tasks" element={<TasksPage />} />
        <Route path="/tasks/create" element={<CreateTaskPage />} />
        <Route path="/tasks/:id" element={<TaskDetailPage />} />
        <Route path="/workflows" element={<WorkflowsPage />} />
        <Route path="/workflows/create" element={<CreateWorkflowPage />} />
        <Route path="/workflows/editor" element={<WorkflowEditorPage />} />
        <Route path="/workflows/editor/:id" element={<WorkflowEditorPage />} />
        <Route path="/automations" element={<AutomationsPage />} />
        <Route path="/orchestrations" element={<OrchestrationsPage />} />
        <Route path="/connections" element={<ConnectionsPage />} />
        <Route path="/intelligence" element={<IntelligencePage />} />
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/generator" element={<GeneratorPage />} />
        <Route path="/migration" element={<MigrationPage />} />
        <Route path="/test" element={<TestPage />} />
        <Route path="/test-embedded" element={<TestEmbeddedPage />} />
        <Route path="/deployed/:agentId" element={<DeployedAgentPage />} />
        <Route path="/langflow" element={<WorkflowSystem />} />
        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Providers>
  )
}

export default App