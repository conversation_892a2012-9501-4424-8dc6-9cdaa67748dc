/**
 * LangFlow API Adapter
 * 
 * This adapter provides the necessary API functions that LangFlow components expect,
 * routing them to our integrated LangFlow backend at /api/v1/langflow/
 */

// Base API configuration for LangFlow endpoints
const LANGFLOW_BASE_URL = '/api/v1/langflow';

class LangFlowAPIAdapter {
  private async fetchAPI(endpoint: string, options: RequestInit = {}) {
    const url = `${LANGFLOW_BASE_URL}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers: defaultHeaders,
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Flows API
  async getFlows(params?: any) {
    const queryParams = new URLSearchParams(params).toString();
    const endpoint = `/v1/flows/${queryParams ? `?${queryParams}` : ''}`;
    return this.fetchAPI(endpoint);
  }

  async getFlow(id: string) {
    return this.fetchAPI(`/v1/flows/${id}`);
  }

  async createFlow(data: any) {
    return this.fetchAPI('/v1/flows/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateFlow(id: string, data: any) {
    return this.fetchAPI(`/v1/flows/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteFlow(id: string) {
    return this.fetchAPI(`/v1/flows/${id}`, {
      method: 'DELETE',
    });
  }

  // Component Types API
  async getAllComponents() {
    return this.fetchAPI('/v1/all');
  }

  // User Authentication API
  async getCurrentUser() {
    return this.fetchAPI('/v1/users/whoami');
  }

  // Global Variables API
  async getGlobalVariables() {
    return this.fetchAPI('/v1/variables/');
  }

  async createGlobalVariable(data: any) {
    return this.fetchAPI('/v1/variables/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Messages/Chat API
  async getMessages(params?: any) {
    const queryParams = new URLSearchParams(params).toString();
    const endpoint = `/v1/monitor/messages${queryParams ? `?${queryParams}` : ''}`;
    return this.fetchAPI(endpoint);
  }

  // Build System API
  async getBuildStatus(flowId: string) {
    return this.fetchAPI(`/v1/monitor/builds?flow_id=${flowId}`);
  }

  async getVertexExecutionOrder(flowId: string, data: any) {
    return this.fetchAPI(`/v1/build/${flowId}/vertices`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // File Upload API
  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    
    return fetch(`${LANGFLOW_BASE_URL}/v1/files/upload`, {
      method: 'POST',
      headers: {
        // Don't set Content-Type for FormData, let browser set it
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
      },
      body: formData,
    }).then(response => {
      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }
      return response.json();
    });
  }

  // Code Validation API
  async validateCode(code: string) {
    return this.fetchAPI('/v1/validate/code', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
  }

  // Flow Execution API
  async runFlow(flowId: string, data?: any) {
    return this.fetchAPI(`/v1/run/${flowId}`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    });
  }

  // System Configuration API
  async getConfig() {
    return this.fetchAPI('/v1/config');
  }

  async getVersion() {
    return this.fetchAPI('/v1/version');
  }

  // Health Check API
  async healthCheck() {
    return this.fetchAPI('/health');
  }

  // Projects/Folders API
  async getProjects() {
    return this.fetchAPI('/v1/projects/');
  }

  async createProject(data: any) {
    return this.fetchAPI('/v1/projects/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Custom Component API
  async uploadCustomComponent(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    
    return fetch(`${LANGFLOW_BASE_URL}/v1/custom_component`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
      },
      body: formData,
    }).then(response => {
      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }
      return response.json();
    });
  }
}

// Create and export singleton instance
export const langflowAPI = new LangFlowAPIAdapter();

// Make API available globally for LangFlow components
if (typeof window !== 'undefined') {
  (window as any).langflowAPI = langflowAPI;
}

export default langflowAPI;