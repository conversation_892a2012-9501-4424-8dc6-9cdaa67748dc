/**
 * WorkflowsPage - Visual AI Workflow Management
 * Powered by the complete LangFlow implementation, rebranded as "Workflows"
 */

import React from 'react';
import MainPage from '@/workflow/pages/MainPage';

export const WorkflowsPage: React.FC = () => {
  // Use the complete LangFlow MainPage component
  // This provides the full workflow management experience with:
  // - Workflow listing and filtering
  // - Template gallery
  // - Folder organization
  // - Import/export functionality
  // - Component library
  return (
    <div className="h-screen">
      <MainPage />
    </div>
  );
};

export default WorkflowsPage;