/**
 * TextShimmer component for LangFlow integration
 */

import React from 'react';
import { cn } from '@/lib/utils';

interface TextShimmerProps {
  className?: string;
  children?: React.ReactNode;
}

const TextShimmer: React.FC<TextShimmerProps> = ({ className, children }) => {
  return (
    <div
      className={cn(
        'inline-block animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] text-transparent bg-clip-text',
        className
      )}
    >
      {children}
    </div>
  );
};

export { TextShimmer };
export default TextShimmer;