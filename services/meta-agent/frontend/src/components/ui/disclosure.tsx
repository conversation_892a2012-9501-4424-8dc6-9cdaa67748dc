/**
 * Disclosure component for LangFlow integration
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

interface DisclosureProps {
  children: React.ReactNode;
  className?: string;
  defaultOpen?: boolean;
}

interface DisclosureButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

interface DisclosurePanelProps {
  children: React.ReactNode;
  className?: string;
  open?: boolean;
}

const DisclosureContext = React.createContext<{
  open: boolean;
  setOpen: (open: boolean) => void;
}>({
  open: false,
  setOpen: () => {},
});

const Disclosure: React.FC<DisclosureProps> = ({ children, className, defaultOpen = false }) => {
  const [open, setOpen] = useState(defaultOpen);

  return (
    <DisclosureContext.Provider value={{ open, setOpen }}>
      <div className={cn('', className)}>
        {children}
      </div>
    </DisclosureContext.Provider>
  );
};

const DisclosureButton: React.FC<DisclosureButtonProps> = ({ children, className, onClick }) => {
  const { open, setOpen } = React.useContext(DisclosureContext);

  const handleClick = () => {
    setOpen(!open);
    onClick?.();
  };

  return (
    <button
      onClick={handleClick}
      className={cn(
        'flex w-full items-center justify-between text-left',
        className
      )}
    >
      {children}
      <ChevronDown 
        className={cn(
          'h-4 w-4 transition-transform duration-200',
          open ? 'rotate-180' : ''
        )}
      />
    </button>
  );
};

const DisclosurePanel: React.FC<DisclosurePanelProps> = ({ children, className }) => {
  const { open } = React.useContext(DisclosureContext);

  if (!open) return null;

  return (
    <div className={cn('mt-2', className)}>
      {children}
    </div>
  );
};

// Additional aliases for LangFlow compatibility
const DisclosureTrigger = DisclosureButton;
const DisclosureContent = DisclosurePanel;

export { 
  Disclosure, 
  DisclosureButton, 
  DisclosurePanel, 
  DisclosureTrigger, 
  DisclosureContent 
};