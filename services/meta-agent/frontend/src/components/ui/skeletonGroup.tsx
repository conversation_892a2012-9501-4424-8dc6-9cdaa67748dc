/**
 * SkeletonGroup component for LangFlow integration
 */

import React from 'react';
import { cn } from '@/lib/utils';

interface SkeletonGroupProps {
  className?: string;
  count?: number;
}

const SkeletonGroup: React.FC<SkeletonGroupProps> = ({ className, count = 3 }) => {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="h-4 bg-gray-200 rounded animate-pulse"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        />
      ))}
    </div>
  );
};

export default SkeletonGroup;