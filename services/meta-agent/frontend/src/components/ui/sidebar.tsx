/**
 * Sidebar component - Provides context for sidebar width and state
 */

import React, { createContext, useContext, useState } from 'react';
import { cn } from '@/lib/utils';

interface SidebarContextType {
  isOpen: boolean;
  toggle: () => void;
  width: string;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

interface SidebarProviderProps {
  children: React.ReactNode;
  width?: string;
  defaultOpen?: boolean;
}

export function SidebarProvider({ children, width = "280px", defaultOpen = true }: SidebarProviderProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggle = () => setIsOpen(!isOpen);

  const value = {
    isOpen,
    toggle,
    width,
  };

  return (
    <SidebarContext.Provider value={value}>
      <div className="flex h-full w-full">
        {children}
      </div>
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}

interface SidebarProps {
  children: React.ReactNode;
  className?: string;
}

export function Sidebar({ children, className }: SidebarProps) {
  const { isOpen, width } = useSidebar();

  return (
    <aside
      className={cn(
        "transition-all duration-300 ease-in-out border-r bg-background",
        isOpen ? "translate-x-0" : "-translate-x-full",
        className
      )}
      style={{ width: isOpen ? width : '0px' }}
    >
      {children}
    </aside>
  );
}

interface SidebarContentProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarContent({ children, className }: SidebarContentProps) {
  return (
    <div className={cn("flex h-full flex-col", className)}>
      {children}
    </div>
  );
}

interface SidebarMainProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarMain({ children, className }: SidebarMainProps) {
  return (
    <main className={cn("flex-1 overflow-auto", className)}>
      {children}
    </main>
  );
}

interface SidebarTriggerProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarTrigger({ children, className }: SidebarTriggerProps) {
  const { toggle } = useSidebar();

  return (
    <button
      onClick={toggle}
      className={cn("p-2 hover:bg-accent rounded-md", className)}
    >
      {children}
    </button>
  );
}

// Additional sidebar components for LangFlow compatibility
interface SidebarHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarHeader({ children, className }: SidebarHeaderProps) {
  return (
    <div className={cn("px-3 py-2 text-sm font-semibold", className)}>
      {children}
    </div>
  );
}

interface SidebarFooterProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarFooter({ children, className }: SidebarFooterProps) {
  return (
    <div className={cn("mt-auto px-3 py-2", className)}>
      {children}
    </div>
  );
}

interface SidebarGroupProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarGroup({ children, className }: SidebarGroupProps) {
  return (
    <div className={cn("px-3 py-2", className)}>
      {children}
    </div>
  );
}

interface SidebarGroupLabelProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarGroupLabel({ children, className }: SidebarGroupLabelProps) {
  return (
    <div className={cn("px-2 py-1 text-xs font-medium text-muted-foreground", className)}>
      {children}
    </div>
  );
}

interface SidebarGroupContentProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarGroupContent({ children, className }: SidebarGroupContentProps) {
  return (
    <div className={cn("space-y-1", className)}>
      {children}
    </div>
  );
}

interface SidebarMenuProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarMenu({ children, className }: SidebarMenuProps) {
  return (
    <nav className={cn("space-y-1", className)}>
      {children}
    </nav>
  );
}

interface SidebarMenuItemProps {
  children: React.ReactNode;
  className?: string;
}

export function SidebarMenuItem({ children, className }: SidebarMenuItemProps) {
  return (
    <div className={cn("", className)}>
      {children}
    </div>
  );
}

interface SidebarMenuButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export function SidebarMenuButton({ children, className, onClick }: SidebarMenuButtonProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex w-full items-center rounded-md px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground",
        className
      )}
    >
      {children}
    </button>
  );
}