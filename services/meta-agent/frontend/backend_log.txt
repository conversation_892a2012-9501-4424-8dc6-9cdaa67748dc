Starting backend server...
Requirement already satisfied: aiosqlite in ./venv/lib/python3.12/site-packages (0.21.0)
Requirement already satisfied: structlog in ./venv/lib/python3.12/site-packages (25.4.0)
Requirement already satisfied: python-dotenv in ./venv/lib/python3.12/site-packages (1.1.1)
Requirement already satisfied: typing_extensions>=4.0 in ./venv/lib/python3.12/site-packages (from aiosqlite) (4.14.1)
INFO:     Will watch for changes in these directories: ['/Users/<USER>/metaspaces/mono/services/meta-agent/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [8626] using StatReload
2025-07-27 15:08:07 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:08:08 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:08:08 [info     ] Docker client initialized successfully
2025-07-27 15:08:08 [info     ] RBAC service initialized
2025-07-27 15:08:08 [info     ] Initialized OAuth provider: google
2025-07-27 15:08:08 [info     ] Initialized OAuth provider: github
2025-07-27 15:08:08 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:08:08 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:08:08 [info     ] MFA service initialized
2025-07-27 15:08:08 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:08:08 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 15, in <module>
    from api.google_adk import router as google_adk_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/google_adk.py", line 16, in <module>
    from services.google_adk import google_adk_service, get_google_adk_service, GoogleADKService
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/google_adk.py", line 13, in <module>
    from google.cloud import aiplatform
ModuleNotFoundError: No module named 'google.cloud'
INFO:     Stopping reloader process [8626]
