import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // LangFlow integration aliases - redirect LangFlow imports to workflow directory  
      // These must come BEFORE the general @ alias to take precedence
      '@/controllers': path.resolve(__dirname, './src/workflow/controllers'),
      '@/customization': path.resolve(__dirname, './src/workflow/customization'),
      '@/stores': path.resolve(__dirname, './src/workflow/stores'),
      '@/hooks': path.resolve(__dirname, './src/workflow/hooks'),
      '@/modals': path.resolve(__dirname, './src/workflow/modals'),
      '@/constants': path.resolve(__dirname, './src/workflow/constants'),
      '@/utils': path.resolve(__dirname, './src/workflow/utils'),
      '@/types': path.resolve(__dirname, './src/workflow/types'),
      '@/components/common': path.resolve(__dirname, './src/workflow/components/common'),
      '@/components/core': path.resolve(__dirname, './src/workflow/components/core'),
      '@/CustomNodes': path.resolve(__dirname, './src/workflow/CustomNodes'),
      '@/CustomEdges': path.resolve(__dirname, './src/workflow/CustomEdges'),
      '@/context': path.resolve(__dirname, './src/workflow/context'),
      '@/shared': path.resolve(__dirname, './src/workflow/shared'),
      '@/alerts': path.resolve(__dirname, './src/workflow/alerts'),
      '@/icons': path.resolve(__dirname, './src/workflow/icons'),
      '@/helpers': path.resolve(__dirname, './src/workflow/helpers'),
      '@/contexts': path.resolve(__dirname, './src/workflow/contexts'),
      // General alias for meta-agent components - more specific first
      '@/contexts/AuthContext': path.resolve(__dirname, './src/contexts/AuthContext'),
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    include: ['@xyflow/react', 'reactflow', 'framer-motion'],
    exclude: ['elkjs']
  },
  server: {
    port: 3000,
    proxy: {
      '/api/v1': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/v1/, '/api/v1'),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
  define: {
    'process.env': process.env,
    global: 'globalThis',
  },
  worker: {
    format: 'es'
  },
})