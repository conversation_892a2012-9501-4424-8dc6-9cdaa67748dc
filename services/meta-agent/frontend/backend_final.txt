Starting backend server...
Requirement already satisfied: aiosqlite in ./venv/lib/python3.12/site-packages (0.21.0)
Requirement already satisfied: structlog in ./venv/lib/python3.12/site-packages (25.4.0)
Requirement already satisfied: python-dotenv in ./venv/lib/python3.12/site-packages (1.1.1)
Requirement already satisfied: google-cloud-aiplatform in ./venv/lib/python3.12/site-packages (1.105.0)
Requirement already satisfied: typing_extensions>=4.0 in ./venv/lib/python3.12/site-packages (from aiosqlite) (4.14.1)
Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1 in ./venv/lib/python3.12/site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (2.25.1)
Requirement already satisfied: google-auth<3.0.0,>=2.14.1 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (2.40.3)
Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (1.26.1)
Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.20.2 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (6.31.1)
Requirement already satisfied: packaging>=14.3 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (25.0)
Requirement already satisfied: google-cloud-storage<3.0.0,>=1.32.0 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (2.19.0)
Requirement already satisfied: google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (3.35.1)
Requirement already satisfied: google-cloud-resource-manager<3.0.0,>=1.3.3 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (1.14.2)
Requirement already satisfied: shapely<3.0.0 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (2.1.1)
Requirement already satisfied: google-genai<2.0.0,>=1.0.0 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (1.27.0)
Requirement already satisfied: pydantic<3 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (2.11.7)
Requirement already satisfied: docstring_parser<1 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (0.17.0)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in ./venv/lib/python3.12/site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (1.70.0)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in ./venv/lib/python3.12/site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (2.32.4)
Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in ./venv/lib/python3.12/site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (1.74.0)
Requirement already satisfied: grpcio-status<2.0.0,>=1.33.2 in ./venv/lib/python3.12/site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (1.74.0)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in ./venv/lib/python3.12/site-packages (from google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in ./venv/lib/python3.12/site-packages (from google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in ./venv/lib/python3.12/site-packages (from google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform) (4.9.1)
Requirement already satisfied: google-cloud-core<3.0.0,>=2.4.1 in ./venv/lib/python3.12/site-packages (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform) (2.4.3)
Requirement already satisfied: google-resumable-media<3.0.0,>=2.0.0 in ./venv/lib/python3.12/site-packages (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform) (2.7.2)
Requirement already satisfied: python-dateutil<3.0.0,>=2.8.2 in ./venv/lib/python3.12/site-packages (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform) (2.9.0.post0)
Requirement already satisfied: grpc-google-iam-v1<1.0.0,>=0.14.0 in ./venv/lib/python3.12/site-packages (from google-cloud-resource-manager<3.0.0,>=1.3.3->google-cloud-aiplatform) (0.14.2)
Requirement already satisfied: google-crc32c<2.0dev,>=1.0 in ./venv/lib/python3.12/site-packages (from google-cloud-storage<3.0.0,>=1.32.0->google-cloud-aiplatform) (1.7.1)
Requirement already satisfied: anyio<5.0.0,>=4.8.0 in ./venv/lib/python3.12/site-packages (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (4.9.0)
Requirement already satisfied: httpx<1.0.0,>=0.28.1 in ./venv/lib/python3.12/site-packages (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (0.28.1)
Requirement already satisfied: tenacity<9.0.0,>=8.2.3 in ./venv/lib/python3.12/site-packages (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (8.5.0)
Requirement already satisfied: websockets<15.1.0,>=13.0.0 in ./venv/lib/python3.12/site-packages (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (15.0.1)
Requirement already satisfied: idna>=2.8 in ./venv/lib/python3.12/site-packages (from anyio<5.0.0,>=4.8.0->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (3.10)
Requirement already satisfied: sniffio>=1.1 in ./venv/lib/python3.12/site-packages (from anyio<5.0.0,>=4.8.0->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (1.3.1)
Requirement already satisfied: certifi in ./venv/lib/python3.12/site-packages (from httpx<1.0.0,>=0.28.1->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (2025.7.14)
Requirement already satisfied: httpcore==1.* in ./venv/lib/python3.12/site-packages (from httpx<1.0.0,>=0.28.1->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (1.0.9)
Requirement already satisfied: h11>=0.16 in ./venv/lib/python3.12/site-packages (from httpcore==1.*->httpx<1.0.0,>=0.28.1->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in ./venv/lib/python3.12/site-packages (from pydantic<3->google-cloud-aiplatform) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./venv/lib/python3.12/site-packages (from pydantic<3->google-cloud-aiplatform) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./venv/lib/python3.12/site-packages (from pydantic<3->google-cloud-aiplatform) (0.4.1)
Requirement already satisfied: six>=1.5 in ./venv/lib/python3.12/site-packages (from python-dateutil<3.0.0,>=2.8.2->google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform) (1.17.0)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (2.5.0)
Requirement already satisfied: pyasn1>=0.1.3 in ./venv/lib/python3.12/site-packages (from rsa<5,>=3.1.4->google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform) (0.6.1)
Requirement already satisfied: numpy>=1.21 in ./venv/lib/python3.12/site-packages (from shapely<3.0.0->google-cloud-aiplatform) (2.3.2)
INFO:     Will watch for changes in these directories: ['/Users/<USER>/metaspaces/mono/services/meta-agent/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [8950] using StatReload
2025-07-27 15:09:20 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:09:20 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:09:20 [info     ] Docker client initialized successfully
2025-07-27 15:09:20 [info     ] RBAC service initialized
2025-07-27 15:09:20 [info     ] Initialized OAuth provider: google
2025-07-27 15:09:20 [info     ] Initialized OAuth provider: github
2025-07-27 15:09:20 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:09:20 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:09:20 [info     ] MFA service initialized
2025-07-27 15:09:20 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:09:20 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 15, in <module>
    from api.google_adk import router as google_adk_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/google_adk.py", line 16, in <module>
    from services.google_adk import google_adk_service, get_google_adk_service, GoogleADKService
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/google_adk.py", line 28, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google.generativeai'
WARNING:  StatReload detected changes in 'src/api/__init__.py'. Reloading...
2025-07-27 15:12:20 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:12:20 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:12:20 [info     ] Docker client initialized successfully
2025-07-27 15:12:20 [info     ] RBAC service initialized
2025-07-27 15:12:20 [info     ] Initialized OAuth provider: google
2025-07-27 15:12:20 [info     ] Initialized OAuth provider: github
2025-07-27 15:12:20 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:12:20 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:12:20 [info     ] MFA service initialized
2025-07-27 15:12:20 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:12:20 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 17, in <module>
    from api.deployment import router as deployment_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/deployment.py", line 12, in <module>
    from services.auto_deployment import AutoDeploymentSystem, DeploymentConfig, DeploymentStrategy, DeploymentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/auto_deployment.py", line 19, in <module>
    from ..utils.logging import get_logger, LoggerMixin
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/api/__init__.py'. Reloading...
2025-07-27 15:12:24 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:12:24 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:12:24 [info     ] Docker client initialized successfully
2025-07-27 15:12:24 [info     ] RBAC service initialized
2025-07-27 15:12:24 [info     ] Initialized OAuth provider: google
2025-07-27 15:12:24 [info     ] Initialized OAuth provider: github
2025-07-27 15:12:24 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:12:24 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:12:24 [info     ] MFA service initialized
2025-07-27 15:12:24 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:12:24 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 17, in <module>
    from api.deployment import router as deployment_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/deployment.py", line 12, in <module>
    from services.auto_deployment import AutoDeploymentSystem, DeploymentConfig, DeploymentStrategy, DeploymentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/auto_deployment.py", line 19, in <module>
    from ..utils.logging import get_logger, LoggerMixin
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/services/auto_deployment.py'. Reloading...
2025-07-27 15:12:43 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:12:43 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:12:43 [info     ] Docker client initialized successfully
2025-07-27 15:12:43 [info     ] RBAC service initialized
2025-07-27 15:12:43 [info     ] Initialized OAuth provider: google
2025-07-27 15:12:43 [info     ] Initialized OAuth provider: github
2025-07-27 15:12:43 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:12:43 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:12:43 [info     ] MFA service initialized
2025-07-27 15:12:43 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:12:43 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-4:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 17, in <module>
    from api.deployment import router as deployment_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/deployment.py", line 12, in <module>
    from services.auto_deployment import AutoDeploymentSystem, DeploymentConfig, DeploymentStrategy, DeploymentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/auto_deployment.py", line 22, in <module>
    import aiofiles
ModuleNotFoundError: No module named 'aiofiles'
INFO:     Stopping reloader process [8950]
