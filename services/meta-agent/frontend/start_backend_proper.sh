#!/bin/bash
# Start backend server with proper Python path

# Change to backend directory
cd ../backend

# Activate virtual environment
source venv/bin/activate

# Install essential missing dependencies quietly
pip install passlib[bcrypt] httpx pyotp 2>/dev/null

# Add src directory to PYTHONPATH and run server
cd src
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload