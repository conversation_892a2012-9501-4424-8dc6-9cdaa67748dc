#!/usr/bin/env python3
import os
import sys

# Add backend src to path
backend_src = os.path.abspath('../backend/src')
sys.path.insert(0, backend_src)
os.chdir('../backend')

print("Testing full app import step by step...")

steps = [
    ("dotenv", "from dotenv import load_dotenv; load_dotenv()"),
    ("settings", "from config.settings import settings"),
    ("database models", "from database.models import Agent, User"),
    ("database connection", "from database.connection import engine"),
    ("ai integration", "from services.ai_integration import ai_integration_service, AIRequest"),
    ("agents service", "from services.agents import AgentService"),
    ("api agents", "from api.agents import router"),
    ("api router", "from api import api_router"),
    ("main app", "from main import app"),
]

for i, (step_name, import_code) in enumerate(steps):
    try:
        print(f"{i+1}. Importing {step_name}...")
        exec(import_code)
        print(f"   ✓ {step_name} imported successfully")
    except Exception as e:
        print(f"   ✗ Error in {step_name}: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        break

print("\nIf all steps passed, the app should be importable!")