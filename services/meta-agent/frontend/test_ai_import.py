#!/usr/bin/env python3
import os
import sys

# Add backend src to path
backend_src = os.path.abspath('../backend/src')
sys.path.insert(0, backend_src)
os.chdir('../backend')

print("Testing AI integration import...")

try:
    print("1. Importing config...")
    from config.settings import settings
    print("   ✓ Config imported")
    
    print("2. Importing ai_integration module...")
    import services.ai_integration
    print("   ✓ Module imported")
    
    print("3. Importing specific classes...")
    from services.ai_integration import AIRequest, AIResponse, AIProvider, TaskType
    print("   ✓ Classes imported")
    
    print("4. Importing service instance...")
    from services.ai_integration import ai_integration_service
    print("   ✓ Service instance imported")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"   ✗ Error: {type(e).__name__}: {e}")
    import traceback
    traceback.print_exc()