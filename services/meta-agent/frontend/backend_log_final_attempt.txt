Starting backend server...
Requirement already satisfied: aiosqlite in ./venv/lib/python3.12/site-packages (0.21.0)
Requirement already satisfied: structlog in ./venv/lib/python3.12/site-packages (25.4.0)
Requirement already satisfied: python-dotenv in ./venv/lib/python3.12/site-packages (1.1.1)
Requirement already satisfied: aiofiles in ./venv/lib/python3.12/site-packages (24.1.0)
Requirement already satisfied: jsonschema in ./venv/lib/python3.12/site-packages (4.25.0)
Collecting loguru
  Using cached loguru-0.7.3-py3-none-any.whl.metadata (22 kB)
Requirement already satisfied: typing_extensions>=4.0 in ./venv/lib/python3.12/site-packages (from aiosqlite) (4.14.1)
Requirement already satisfied: attrs>=22.2.0 in ./venv/lib/python3.12/site-packages (from jsonschema) (25.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in ./venv/lib/python3.12/site-packages (from jsonschema) (2025.4.1)
Requirement already satisfied: referencing>=0.28.4 in ./venv/lib/python3.12/site-packages (from jsonschema) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in ./venv/lib/python3.12/site-packages (from jsonschema) (0.26.0)
Using cached loguru-0.7.3-py3-none-any.whl (61 kB)
Installing collected packages: loguru
Successfully installed loguru-0.7.3
INFO:     Will watch for changes in these directories: ['/Users/<USER>/metaspaces/mono/services/meta-agent/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [11691] using StatReload
2025-07-27 15:27:22 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:27:22 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:27:22 [info     ] Docker client initialized successfully
2025-07-27 15:27:22 [info     ] RBAC service initialized
2025-07-27 15:27:22 [info     ] Initialized OAuth provider: google
2025-07-27 15:27:22 [info     ] Initialized OAuth provider: github
2025-07-27 15:27:22 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:27:22 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:27:22 [info     ] MFA service initialized
2025-07-27 15:27:22 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:27:22 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 23, in <module>
    from langflow.api import router as langflow_router, health_check_router, log_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/langflow/api/__init__.py", line 1, in <module>
    from langflow.api.health_check_router import health_check_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/langflow/api/health_check_router.py", line 6, in <module>
    from sqlmodel import select
ModuleNotFoundError: No module named 'sqlmodel'
WARNING:  StatReload detected changes in 'src/api/__init__.py'. Reloading...
2025-07-27 15:27:55 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:27:55 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:27:55 [info     ] Docker client initialized successfully
2025-07-27 15:27:55 [info     ] RBAC service initialized
2025-07-27 15:27:55 [info     ] Initialized OAuth provider: google
2025-07-27 15:27:55 [info     ] Initialized OAuth provider: github
2025-07-27 15:27:55 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:27:55 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:27:55 [info     ] MFA service initialized
2025-07-27 15:27:55 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:27:55 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 45, in <module>
    api_router.include_router(langflow_router, prefix="/langflow")
                              ^^^^^^^^^^^^^^^
NameError: name 'langflow_router' is not defined. Did you mean: 'workflows_router'?
WARNING:  StatReload detected changes in 'src/api/__init__.py'. Reloading...
2025-07-27 15:28:01 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:28:01 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:28:01 [info     ] Docker client initialized successfully
2025-07-27 15:28:01 [info     ] RBAC service initialized
2025-07-27 15:28:01 [info     ] Initialized OAuth provider: google
2025-07-27 15:28:01 [info     ] Initialized OAuth provider: github
2025-07-27 15:28:01 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:28:01 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:28:01 [info     ] MFA service initialized
2025-07-27 15:28:01 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:28:01 [info     ] Orchestration manager initialized max_orchestrations=100
INFO:     Started server process [11811]
INFO:     Waiting for application startup.
{"version": "1.0.0", "event": "Starting AI Agent Platform", "timestamp": "2025-07-27T09:58:04.251019Z", "level": "info"}
2025-07-27 15:28:04,274 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-07-27 15:28:04,274 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-27 15:28:04,276 INFO sqlalchemy.engine.Engine select current_schema()
2025-07-27 15:28:04,276 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-27 15:28:04,278 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-07-27 15:28:04,278 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-27 15:28:04,279 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 15:28:04,280 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,280 INFO sqlalchemy.engine.Engine [generated in 0.00007s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,284 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,284 INFO sqlalchemy.engine.Engine [cached since 0.003781s ago] ('agents', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,284 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,284 INFO sqlalchemy.engine.Engine [cached since 0.004271s ago] ('tasks', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,285 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,285 INFO sqlalchemy.engine.Engine [cached since 0.004694s ago] ('orchestrations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,285 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,285 INFO sqlalchemy.engine.Engine [cached since 0.005093s ago] ('orchestration_members', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,285 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,285 INFO sqlalchemy.engine.Engine [cached since 0.005435s ago] ('ai_models', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,286 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,286 INFO sqlalchemy.engine.Engine [cached since 0.005782s ago] ('agent_generations', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,286 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,286 INFO sqlalchemy.engine.Engine [cached since 0.006052s ago] ('communications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,286 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,286 INFO sqlalchemy.engine.Engine [cached since 0.006375s ago] ('oauth_tokens', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,287 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,287 INFO sqlalchemy.engine.Engine [cached since 0.006691s ago] ('roles', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,287 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,287 INFO sqlalchemy.engine.Engine [cached since 0.006975s ago] ('permissions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,287 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,287 INFO sqlalchemy.engine.Engine [cached since 0.007347s ago] ('user_roles', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,288 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,288 INFO sqlalchemy.engine.Engine [cached since 0.007675s ago] ('role_permissions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,288 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,288 INFO sqlalchemy.engine.Engine [cached since 0.007961s ago] ('mfa_devices', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,288 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-27 15:28:04,288 INFO sqlalchemy.engine.Engine [cached since 0.00827s ago] ('login_attempts', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-27 15:28:04,289 INFO sqlalchemy.engine.Engine COMMIT
{"event": "Database tables created successfully", "timestamp": "2025-07-27T09:58:04.289450Z", "level": "info"}
{"event": "Database tables created successfully", "timestamp": "2025-07-27T09:58:04.289478Z", "level": "info"}
{"error": "NoBrokersAvailable", "event": "Failed to connect to Kafka", "timestamp": "2025-07-27T09:58:06.293418Z", "level": "error"}
{"event": "Message queue service connected", "timestamp": "2025-07-27T09:58:06.293536Z", "level": "info"}
{"topic": "agent.events", "error": "NoBrokersAvailable", "event": "Failed to subscribe to topic", "timestamp": "2025-07-27T09:58:08.299041Z", "level": "error"}
{"topic": "orchestration.commands", "error": "NoBrokersAvailable", "event": "Failed to subscribe to topic", "timestamp": "2025-07-27T09:58:10.304659Z", "level": "error"}
{"topic": "intelligence.requests", "error": "NoBrokersAvailable", "event": "Failed to subscribe to topic", "timestamp": "2025-07-27T09:58:12.307867Z", "level": "error"}
{"event": "Message queue subscriptions initialized", "timestamp": "2025-07-27T09:58:12.307970Z", "level": "info"}
{"event": "AI integration service initialized", "timestamp": "2025-07-27T09:58:12.308011Z", "level": "info"}
INFO:     Application startup complete.
INFO:     127.0.0.1:62466 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:62496 - "GET /api/agents HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:62534 - "GET /agents HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:62558 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:62588 - "GET /openapi.json HTTP/1.1" 200 OK
2025-07-27 15:28:59,956 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 15:28:59,957 INFO sqlalchemy.engine.Engine SELECT agents.name, agents.description, agents.type, agents.status, agents.config, agents.capabilities, agents.constraints, agents.version, agents.runtime_info, agents.last_heartbeat, agents.cpu_usage, agents.memory_usage, agents.owner_id, agents.id, agents.created_at, agents.updated_at, agents.created_by, agents.updated_by 
FROM agents 
 LIMIT $1::INTEGER OFFSET $2::INTEGER
2025-07-27 15:28:59,957 INFO sqlalchemy.engine.Engine [generated in 0.00008s] (100, 0)
2025-07-27 15:28:59,964 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.last_login AS users_last_login, users.first_name AS users_first_name, users.last_name AS users_last_name, users.avatar_url AS users_avatar_url, users.is_verified AS users_is_verified, users.verified_at AS users_verified_at, users.last_login_at AS users_last_login_at, users.oauth_provider AS users_oauth_provider, users.oauth_provider_id AS users_oauth_provider_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.created_by AS users_created_by, users.updated_by AS users_updated_by 
FROM users 
WHERE users.id IN ($1::UUID)
2025-07-27 15:28:59,964 INFO sqlalchemy.engine.Engine [generated in 0.00010s] (UUID('45758cd8-e46d-4984-88dc-7bd5d376a08a'),)
2025-07-27 15:28:59,967 INFO sqlalchemy.engine.Engine SELECT count(agents.id) AS count_1 
FROM agents
2025-07-27 15:28:59,967 INFO sqlalchemy.engine.Engine [generated in 0.00004s] ()
2025-07-27 15:28:59,969 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:62626 - "GET /api/v1/agents/ HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
{"event": "Shutting down AI Agent Platform", "timestamp": "2025-07-27T10:03:42.115040Z", "level": "info"}
{"event": "Disconnected from Kafka", "timestamp": "2025-07-27T10:03:42.115635Z", "level": "info"}
{"event": "Message queue service disconnected", "timestamp": "2025-07-27T10:03:42.115781Z", "level": "info"}
INFO:     Application shutdown complete.
INFO:     Finished server process [11811]
INFO:     Stopping reloader process [11691]
