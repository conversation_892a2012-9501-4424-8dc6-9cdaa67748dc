Starting backend server...
Requirement already satisfied: aiosqlite in ./venv/lib/python3.12/site-packages (0.21.0)
Requirement already satisfied: structlog in ./venv/lib/python3.12/site-packages (25.4.0)
Requirement already satisfied: python-dotenv in ./venv/lib/python3.12/site-packages (1.1.1)
Requirement already satisfied: aiofiles in ./venv/lib/python3.12/site-packages (24.1.0)
Requirement already satisfied: typing_extensions>=4.0 in ./venv/lib/python3.12/site-packages (from aiosqlite) (4.14.1)
INFO:     Will watch for changes in these directories: ['/Users/<USER>/metaspaces/mono/services/meta-agent/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [9895] using StatReload
2025-07-27 15:15:47 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:15:47 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:15:47 [info     ] Docker client initialized successfully
2025-07-27 15:15:47 [info     ] RBAC service initialized
2025-07-27 15:15:47 [info     ] Initialized OAuth provider: google
2025-07-27 15:15:47 [info     ] Initialized OAuth provider: github
2025-07-27 15:15:47 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:15:47 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:15:47 [info     ] MFA service initialized
2025-07-27 15:15:47 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:15:47 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from ..intelligence.gateway import AIGateway
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/services/self_evolution.py'. Reloading...
2025-07-27 15:16:10 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:16:10 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:16:10 [info     ] Docker client initialized successfully
2025-07-27 15:16:10 [info     ] RBAC service initialized
2025-07-27 15:16:10 [info     ] Initialized OAuth provider: google
2025-07-27 15:16:10 [info     ] Initialized OAuth provider: github
2025-07-27 15:16:10 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:16:10 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:16:10 [info     ] MFA service initialized
2025-07-27 15:16:10 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:16:10 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 8, in <module>
    from .gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/gateway.py", line 5, in <module>
    from .providers.base import BaseAIProvider, AIMessage, AIResponse, ModelInfo, ModelCapability
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/providers/__init__.py", line 6, in <module>
    from .google import GoogleAIProvider
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/providers/google.py", line 3, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google.generativeai'
WARNING:  StatReload detected changes in 'src/services/self_evolution.py'. Reloading...
2025-07-27 15:16:15 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:16:15 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:16:15 [info     ] Docker client initialized successfully
2025-07-27 15:16:15 [info     ] RBAC service initialized
2025-07-27 15:16:15 [info     ] Initialized OAuth provider: google
2025-07-27 15:16:15 [info     ] Initialized OAuth provider: github
2025-07-27 15:16:15 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:16:15 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:16:15 [info     ] MFA service initialized
2025-07-27 15:16:15 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:16:15 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 8, in <module>
    from .gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/gateway.py", line 5, in <module>
    from .providers.base import BaseAIProvider, AIMessage, AIResponse, ModelInfo, ModelCapability
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/providers/__init__.py", line 6, in <module>
    from .google import GoogleAIProvider
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/providers/google.py", line 3, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google.generativeai'
WARNING:  StatReload detected changes in 'src/intelligence/providers/__init__.py'. Reloading...
2025-07-27 15:16:28 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:16:28 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:16:28 [info     ] Docker client initialized successfully
2025-07-27 15:16:28 [info     ] RBAC service initialized
2025-07-27 15:16:28 [info     ] Initialized OAuth provider: google
2025-07-27 15:16:28 [info     ] Initialized OAuth provider: github
2025-07-27 15:16:28 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:16:28 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:16:28 [info     ] MFA service initialized
2025-07-27 15:16:28 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:16:28 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-4:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 8, in <module>
    from .gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/gateway.py", line 8, in <module>
    from .providers.google import GoogleAIProvider
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/providers/google.py", line 3, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google.generativeai'
WARNING:  StatReload detected changes in 'src/intelligence/gateway.py'. Reloading...
2025-07-27 15:16:45 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:16:45 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:16:45 [info     ] Docker client initialized successfully
2025-07-27 15:16:45 [info     ] RBAC service initialized
2025-07-27 15:16:45 [info     ] Initialized OAuth provider: google
2025-07-27 15:16:45 [info     ] Initialized OAuth provider: github
2025-07-27 15:16:45 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:16:45 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:16:45 [info     ] MFA service initialized
2025-07-27 15:16:45 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:16:45 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-5:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 10, in <module>
    from .reasoning.context_manager import ContextManager
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/reasoning/__init__.py", line 4, in <module>
    from .chain_of_thought import ChainOfThoughtReasoner
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/reasoning/chain_of_thought.py", line 15, in <module>
    from ...utils.logging import get_logger
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/intelligence/reasoning/chain_of_thought.py'. Reloading...
2025-07-27 15:17:01 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:17:01 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:17:01 [info     ] Docker client initialized successfully
2025-07-27 15:17:01 [info     ] RBAC service initialized
2025-07-27 15:17:01 [info     ] Initialized OAuth provider: google
2025-07-27 15:17:01 [info     ] Initialized OAuth provider: github
2025-07-27 15:17:01 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:17:01 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:17:01 [info     ] MFA service initialized
2025-07-27 15:17:01 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:17:01 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-6:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 10, in <module>
    from .reasoning.context_manager import ContextManager
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/reasoning/__init__.py", line 5, in <module>
    from .multi_modal import MultiModalProcessor
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/reasoning/multi_modal.py", line 18, in <module>
    from ...utils.logging import get_logger
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/intelligence/gateway.py'. Reloading...
2025-07-27 15:17:12 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:17:12 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:17:12 [info     ] Docker client initialized successfully
2025-07-27 15:17:12 [info     ] RBAC service initialized
2025-07-27 15:17:12 [info     ] Initialized OAuth provider: google
2025-07-27 15:17:12 [info     ] Initialized OAuth provider: github
2025-07-27 15:17:12 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:17:12 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:17:12 [info     ] MFA service initialized
2025-07-27 15:17:12 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:17:12 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-7:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 10, in <module>
    from .reasoning.context_manager import ContextManager
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/reasoning/__init__.py", line 5, in <module>
    from .multi_modal import MultiModalProcessor
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/reasoning/multi_modal.py", line 18, in <module>
    from ...utils.logging import get_logger
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/intelligence/reasoning/multi_modal.py'. Reloading...
2025-07-27 15:17:20 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:17:20 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:17:20 [info     ] Docker client initialized successfully
2025-07-27 15:17:20 [info     ] RBAC service initialized
2025-07-27 15:17:21 [info     ] Initialized OAuth provider: google
2025-07-27 15:17:21 [info     ] Initialized OAuth provider: github
2025-07-27 15:17:21 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:17:21 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:17:21 [info     ] MFA service initialized
2025-07-27 15:17:21 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:17:21 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-8:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 11, in <module>
    from .embeddings.generator import EmbeddingGenerator
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/embeddings/__init__.py", line 3, in <module>
    from .generator import EmbeddingGenerator
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/embeddings/generator.py", line 19, in <module>
    from ...utils.logging import get_logger
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/generation/prompt_based_operations.py'. Reloading...
2025-07-27 15:18:30 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:18:31 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:18:31 [info     ] Docker client initialized successfully
2025-07-27 15:18:31 [info     ] RBAC service initialized
2025-07-27 15:18:31 [info     ] Initialized OAuth provider: google
2025-07-27 15:18:31 [info     ] Initialized OAuth provider: github
2025-07-27 15:18:31 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:18:31 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:18:31 [info     ] MFA service initialized
2025-07-27 15:18:31 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:18:31 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-9:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 11, in <module>
    from .embeddings.generator import EmbeddingGenerator
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/embeddings/__init__.py", line 3, in <module>
    from .generator import EmbeddingGenerator
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/embeddings/generator.py", line 19, in <module>
    from ...utils.logging import get_logger
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/generation/templates.py'. Reloading...
2025-07-27 15:19:34 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:19:34 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:19:34 [info     ] Docker client initialized successfully
2025-07-27 15:19:34 [info     ] RBAC service initialized
2025-07-27 15:19:34 [info     ] Initialized OAuth provider: google
2025-07-27 15:19:34 [info     ] Initialized OAuth provider: github
2025-07-27 15:19:34 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:19:34 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:19:34 [info     ] MFA service initialized
2025-07-27 15:19:34 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:19:34 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-10:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 16, in <module>
    from intelligence.gateway import AIGateway
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/__init__.py", line 11, in <module>
    from .embeddings.generator import EmbeddingGenerator
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/embeddings/__init__.py", line 3, in <module>
    from .generator import EmbeddingGenerator
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/intelligence/embeddings/generator.py", line 19, in <module>
    from ...utils.logging import get_logger
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/intelligence/embeddings/generator.py'. Reloading...
2025-07-27 15:20:03 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:20:03 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:20:03 [info     ] Docker client initialized successfully
2025-07-27 15:20:03 [info     ] RBAC service initialized
2025-07-27 15:20:03 [info     ] Initialized OAuth provider: google
2025-07-27 15:20:03 [info     ] Initialized OAuth provider: github
2025-07-27 15:20:03 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:20:03 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:20:03 [info     ] MFA service initialized
2025-07-27 15:20:03 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:20:03 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-11:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 18, in <module>
    from api.evolution import router as evolution_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/evolution.py", line 11, in <module>
    from services.self_evolution import PlatformEvolutionSystem
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/self_evolution.py", line 20, in <module>
    from generation.ai_workflow_generator import AgentWorkflowGenerator
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/generation/ai_workflow_generator.py", line 15, in <module>
    from ..intelligence.gateway import AIGateway
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/generation/ai_workflow_generator.py'. Reloading...
2025-07-27 15:20:28 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:20:28 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:20:28 [info     ] Docker client initialized successfully
2025-07-27 15:20:28 [info     ] RBAC service initialized
2025-07-27 15:20:28 [info     ] Initialized OAuth provider: google
2025-07-27 15:20:28 [info     ] Initialized OAuth provider: github
2025-07-27 15:20:28 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:20:28 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:20:28 [info     ] MFA service initialized
2025-07-27 15:20:28 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:20:28 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-12:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 15, in <module>
    from ..intelligence.gateway import AIGateway
ImportError: attempted relative import beyond top-level package
WARNING:  StatReload detected changes in 'src/services/workflow_engine.py'. Reloading...
2025-07-27 15:20:56 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:20:56 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:20:56 [info     ] Docker client initialized successfully
2025-07-27 15:20:56 [info     ] RBAC service initialized
2025-07-27 15:20:56 [info     ] Initialized OAuth provider: google
2025-07-27 15:20:56 [info     ] Initialized OAuth provider: github
2025-07-27 15:20:56 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:20:56 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:20:56 [info     ] MFA service initialized
2025-07-27 15:20:56 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:20:56 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-13:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/generation/templates.py'. Reloading...
2025-07-27 15:21:01 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:21:01 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:21:01 [info     ] Docker client initialized successfully
2025-07-27 15:21:01 [info     ] RBAC service initialized
2025-07-27 15:21:01 [info     ] Initialized OAuth provider: google
2025-07-27 15:21:01 [info     ] Initialized OAuth provider: github
2025-07-27 15:21:01 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:21:01 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:21:01 [info     ] MFA service initialized
2025-07-27 15:21:01 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:21:01 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-14:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/orchestration/compound_agents.py'. Reloading...
2025-07-27 15:21:27 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:21:27 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:21:27 [info     ] Docker client initialized successfully
2025-07-27 15:21:27 [info     ] RBAC service initialized
2025-07-27 15:21:27 [info     ] Initialized OAuth provider: google
2025-07-27 15:21:27 [info     ] Initialized OAuth provider: github
2025-07-27 15:21:27 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:21:27 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:21:27 [info     ] MFA service initialized
2025-07-27 15:21:27 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:21:27 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-15:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/services/mcp_integration.py'. Reloading...
2025-07-27 15:21:35 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:21:35 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:21:35 [info     ] Docker client initialized successfully
2025-07-27 15:21:36 [info     ] RBAC service initialized
2025-07-27 15:21:36 [info     ] Initialized OAuth provider: google
2025-07-27 15:21:36 [info     ] Initialized OAuth provider: github
2025-07-27 15:21:36 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:21:36 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:21:36 [info     ] MFA service initialized
2025-07-27 15:21:36 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:21:36 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-16:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/services/google_adk.py'. Reloading...
2025-07-27 15:22:02 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:22:02 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:22:02 [info     ] Docker client initialized successfully
2025-07-27 15:22:02 [info     ] RBAC service initialized
2025-07-27 15:22:02 [info     ] Initialized OAuth provider: google
2025-07-27 15:22:02 [info     ] Initialized OAuth provider: github
2025-07-27 15:22:02 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:22:02 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:22:02 [info     ] MFA service initialized
2025-07-27 15:22:02 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:22:02 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-17:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/services/migration_service.py'. Reloading...
2025-07-27 15:22:12 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:22:12 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:22:12 [info     ] Docker client initialized successfully
2025-07-27 15:22:12 [info     ] RBAC service initialized
2025-07-27 15:22:13 [info     ] Initialized OAuth provider: google
2025-07-27 15:22:13 [info     ] Initialized OAuth provider: github
2025-07-27 15:22:13 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:22:13 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:22:13 [info     ] MFA service initialized
2025-07-27 15:22:13 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:22:13 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-18:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/services/migration_service.py'. Reloading...
2025-07-27 15:22:23 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:22:23 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:22:23 [info     ] Docker client initialized successfully
2025-07-27 15:22:23 [info     ] RBAC service initialized
2025-07-27 15:22:23 [info     ] Initialized OAuth provider: google
2025-07-27 15:22:23 [info     ] Initialized OAuth provider: github
2025-07-27 15:22:23 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:22:23 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:22:23 [info     ] MFA service initialized
2025-07-27 15:22:23 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:22:23 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-19:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/services/migration_service.py'. Reloading...
2025-07-27 15:22:39 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:22:40 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:22:40 [info     ] Docker client initialized successfully
2025-07-27 15:22:40 [info     ] RBAC service initialized
2025-07-27 15:22:40 [info     ] Initialized OAuth provider: google
2025-07-27 15:22:40 [info     ] Initialized OAuth provider: github
2025-07-27 15:22:40 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:22:40 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:22:40 [info     ] MFA service initialized
2025-07-27 15:22:40 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:22:40 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-20:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/services/a2a_protocol.py'. Reloading...
2025-07-27 15:23:02 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:23:02 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:23:02 [info     ] Docker client initialized successfully
2025-07-27 15:23:02 [info     ] RBAC service initialized
2025-07-27 15:23:02 [info     ] Initialized OAuth provider: google
2025-07-27 15:23:02 [info     ] Initialized OAuth provider: github
2025-07-27 15:23:02 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:23:02 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:23:02 [info     ] MFA service initialized
2025-07-27 15:23:02 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:23:02 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-21:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/protocols/enhanced_a2a.py'. Reloading...
2025-07-27 15:23:12 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:23:12 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:23:12 [info     ] Docker client initialized successfully
2025-07-27 15:23:12 [info     ] RBAC service initialized
2025-07-27 15:23:12 [info     ] Initialized OAuth provider: google
2025-07-27 15:23:12 [info     ] Initialized OAuth provider: github
2025-07-27 15:23:12 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:23:12 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:23:12 [info     ] MFA service initialized
2025-07-27 15:23:12 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:23:12 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-22:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/migration/app_to_agent.py'. Reloading...
2025-07-27 15:23:21 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:23:21 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:23:21 [info     ] Docker client initialized successfully
2025-07-27 15:23:21 [info     ] RBAC service initialized
2025-07-27 15:23:22 [info     ] Initialized OAuth provider: google
2025-07-27 15:23:22 [info     ] Initialized OAuth provider: github
2025-07-27 15:23:22 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:23:22 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:23:22 [info     ] MFA service initialized
2025-07-27 15:23:22 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:23:22 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-23:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/ml/model_serving.py'. Reloading...
2025-07-27 15:23:30 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:23:30 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:23:30 [info     ] Docker client initialized successfully
2025-07-27 15:23:30 [info     ] RBAC service initialized
2025-07-27 15:23:30 [info     ] Initialized OAuth provider: google
2025-07-27 15:23:30 [info     ] Initialized OAuth provider: github
2025-07-27 15:23:30 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:23:30 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:23:30 [info     ] MFA service initialized
2025-07-27 15:23:31 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:23:31 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-24:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/integrations/google_adk.py'. Reloading...
2025-07-27 15:23:40 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:23:40 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:23:40 [info     ] Docker client initialized successfully
2025-07-27 15:23:40 [info     ] RBAC service initialized
2025-07-27 15:23:40 [info     ] Initialized OAuth provider: google
2025-07-27 15:23:40 [info     ] Initialized OAuth provider: github
2025-07-27 15:23:40 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:23:40 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:23:40 [info     ] MFA service initialized
2025-07-27 15:23:40 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:23:40 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-25:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/agents/self_healing.py'. Reloading...
2025-07-27 15:23:51 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:23:51 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:23:51 [info     ] Docker client initialized successfully
2025-07-27 15:23:51 [info     ] RBAC service initialized
2025-07-27 15:23:51 [info     ] Initialized OAuth provider: google
2025-07-27 15:23:51 [info     ] Initialized OAuth provider: github
2025-07-27 15:23:51 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:23:51 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:23:51 [info     ] MFA service initialized
2025-07-27 15:23:51 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:23:51 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-26:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
WARNING:  StatReload detected changes in 'src/intelligence/reasoning/context_manager.py'. Reloading...
2025-07-27 15:24:00 [info     ] Agent manager initialized      max_agents=1000
2025-07-27 15:24:00 [info     ] Agent storage initialized      base_path=/tmp/ai_agent_storage/generated_agents
2025-07-27 15:24:00 [info     ] Docker client initialized successfully
2025-07-27 15:24:00 [info     ] RBAC service initialized
2025-07-27 15:24:00 [info     ] Initialized OAuth provider: google
2025-07-27 15:24:00 [info     ] Initialized OAuth provider: github
2025-07-27 15:24:00 [info     ] Initialized OAuth provider: microsoft
2025-07-27 15:24:00 [info     ] OAuth service initialized with providers: providers=['google', 'github', 'microsoft']
2025-07-27 15:24:00 [info     ] MFA service initialized
2025-07-27 15:24:00 [info     ] Message queue service initialized topics={'agent_events': 'agent.events', 'orchestration': 'orchestration.commands', 'intelligence': 'intelligence.requests'}
2025-07-27 15:24:00 [info     ] Orchestration manager initialized max_orchestrations=100
Process SpawnProcess-27:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.11/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/main.py", line 18, in <module>
    from api import api_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/__init__.py", line 19, in <module>
    from api.workflows import router as workflows_router
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/api/workflows.py", line 14, in <module>
    from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/services/workflow_engine.py", line 19, in <module>
    from orchestration.compound_agents import CompoundAgentSystem, ExecutionContext, CompoundAgentStatus
  File "/Users/<USER>/metaspaces/mono/services/meta-agent/backend/src/orchestration/compound_agents.py", line 15, in <module>
    import jsonschema
ModuleNotFoundError: No module named 'jsonschema'
INFO:     Stopping reloader process [9895]
