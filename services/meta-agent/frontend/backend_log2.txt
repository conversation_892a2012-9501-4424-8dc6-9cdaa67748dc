Starting backend server...
Requirement already satisfied: aiosqlite in ./venv/lib/python3.12/site-packages (0.21.0)
Requirement already satisfied: structlog in ./venv/lib/python3.12/site-packages (25.4.0)
Requirement already satisfied: python-dotenv in ./venv/lib/python3.12/site-packages (1.1.1)
Collecting google-cloud-aiplatform
  Using cached google_cloud_aiplatform-1.105.0-py2.py3-none-any.whl.metadata (38 kB)
Requirement already satisfied: typing_extensions>=4.0 in ./venv/lib/python3.12/site-packages (from aiosqlite) (4.14.1)
Collecting google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform)
  Using cached google_api_core-2.25.1-py3-none-any.whl.metadata (3.0 kB)
Collecting google-auth<3.0.0,>=2.14.1 (from google-cloud-aiplatform)
  Using cached google_auth-2.40.3-py2.py3-none-any.whl.metadata (6.2 kB)
Collecting proto-plus<2.0.0,>=1.22.3 (from google-cloud-aiplatform)
  Using cached proto_plus-1.26.1-py3-none-any.whl.metadata (2.2 kB)
Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.20.2 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (6.31.1)
Requirement already satisfied: packaging>=14.3 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (25.0)
Collecting google-cloud-storage<3.0.0,>=1.32.0 (from google-cloud-aiplatform)
  Using cached google_cloud_storage-2.19.0-py2.py3-none-any.whl.metadata (9.1 kB)
Collecting google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0 (from google-cloud-aiplatform)
  Using cached google_cloud_bigquery-3.35.1-py3-none-any.whl.metadata (8.0 kB)
Collecting google-cloud-resource-manager<3.0.0,>=1.3.3 (from google-cloud-aiplatform)
  Using cached google_cloud_resource_manager-1.14.2-py3-none-any.whl.metadata (9.6 kB)
Collecting shapely<3.0.0 (from google-cloud-aiplatform)
  Using cached shapely-2.1.1-cp312-cp312-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting google-genai<2.0.0,>=1.0.0 (from google-cloud-aiplatform)
  Using cached google_genai-1.27.0-py3-none-any.whl.metadata (43 kB)
Requirement already satisfied: pydantic<3 in ./venv/lib/python3.12/site-packages (from google-cloud-aiplatform) (2.11.7)
Collecting docstring_parser<1 (from google-cloud-aiplatform)
  Using cached docstring_parser-0.17.0-py3-none-any.whl.metadata (3.5 kB)
Collecting googleapis-common-protos<2.0.0,>=1.56.2 (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform)
  Using cached googleapis_common_protos-1.70.0-py3-none-any.whl.metadata (9.3 kB)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in ./venv/lib/python3.12/site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (2.32.4)
Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in ./venv/lib/python3.12/site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (1.74.0)
Collecting grpcio-status<2.0.0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform)
  Using cached grpcio_status-1.74.0-py3-none-any.whl.metadata (1.1 kB)
Collecting cachetools<6.0,>=2.0.0 (from google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform)
  Using cached cachetools-5.5.2-py3-none-any.whl.metadata (5.4 kB)
Collecting pyasn1-modules>=0.2.1 (from google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform)
  Using cached pyasn1_modules-0.4.2-py3-none-any.whl.metadata (3.5 kB)
Requirement already satisfied: rsa<5,>=3.1.4 in ./venv/lib/python3.12/site-packages (from google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform) (4.9.1)
Collecting google-cloud-core<3.0.0,>=2.4.1 (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform)
  Using cached google_cloud_core-2.4.3-py2.py3-none-any.whl.metadata (2.7 kB)
Collecting google-resumable-media<3.0.0,>=2.0.0 (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform)
  Using cached google_resumable_media-2.7.2-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting python-dateutil<3.0.0,>=2.8.2 (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform)
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting grpc-google-iam-v1<1.0.0,>=0.14.0 (from google-cloud-resource-manager<3.0.0,>=1.3.3->google-cloud-aiplatform)
  Using cached grpc_google_iam_v1-0.14.2-py3-none-any.whl.metadata (9.1 kB)
Collecting google-crc32c<2.0dev,>=1.0 (from google-cloud-storage<3.0.0,>=1.32.0->google-cloud-aiplatform)
  Using cached google_crc32c-1.7.1-cp312-cp312-macosx_12_0_arm64.whl.metadata (2.3 kB)
Requirement already satisfied: anyio<5.0.0,>=4.8.0 in ./venv/lib/python3.12/site-packages (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (4.9.0)
Requirement already satisfied: httpx<1.0.0,>=0.28.1 in ./venv/lib/python3.12/site-packages (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (0.28.1)
Collecting tenacity<9.0.0,>=8.2.3 (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform)
  Using cached tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)
Collecting websockets<15.1.0,>=13.0.0 (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform)
  Using cached websockets-15.0.1-cp312-cp312-macosx_11_0_arm64.whl.metadata (6.8 kB)
Requirement already satisfied: idna>=2.8 in ./venv/lib/python3.12/site-packages (from anyio<5.0.0,>=4.8.0->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (3.10)
Requirement already satisfied: sniffio>=1.1 in ./venv/lib/python3.12/site-packages (from anyio<5.0.0,>=4.8.0->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (1.3.1)
Requirement already satisfied: certifi in ./venv/lib/python3.12/site-packages (from httpx<1.0.0,>=0.28.1->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (2025.7.14)
Requirement already satisfied: httpcore==1.* in ./venv/lib/python3.12/site-packages (from httpx<1.0.0,>=0.28.1->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (1.0.9)
Requirement already satisfied: h11>=0.16 in ./venv/lib/python3.12/site-packages (from httpcore==1.*->httpx<1.0.0,>=0.28.1->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in ./venv/lib/python3.12/site-packages (from pydantic<3->google-cloud-aiplatform) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./venv/lib/python3.12/site-packages (from pydantic<3->google-cloud-aiplatform) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./venv/lib/python3.12/site-packages (from pydantic<3->google-cloud-aiplatform) (0.4.1)
Requirement already satisfied: six>=1.5 in ./venv/lib/python3.12/site-packages (from python-dateutil<3.0.0,>=2.8.2->google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform) (1.17.0)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform) (2.5.0)
Requirement already satisfied: pyasn1>=0.1.3 in ./venv/lib/python3.12/site-packages (from rsa<5,>=3.1.4->google-auth<3.0.0,>=2.14.1->google-cloud-aiplatform) (0.6.1)
Requirement already satisfied: numpy>=1.21 in ./venv/lib/python3.12/site-packages (from shapely<3.0.0->google-cloud-aiplatform) (2.3.2)
Downloading google_cloud_aiplatform-1.105.0-py2.py3-none-any.whl (7.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 7.9/7.9 MB 10.4 MB/s eta 0:00:00
Using cached docstring_parser-0.17.0-py3-none-any.whl (36 kB)
Using cached google_api_core-2.25.1-py3-none-any.whl (160 kB)
Using cached google_auth-2.40.3-py2.py3-none-any.whl (216 kB)
Using cached cachetools-5.5.2-py3-none-any.whl (10 kB)
Using cached google_cloud_bigquery-3.35.1-py3-none-any.whl (256 kB)
Using cached google_cloud_core-2.4.3-py2.py3-none-any.whl (29 kB)
Using cached google_cloud_resource_manager-1.14.2-py3-none-any.whl (394 kB)
Using cached google_cloud_storage-2.19.0-py2.py3-none-any.whl (131 kB)
Using cached google_crc32c-1.7.1-cp312-cp312-macosx_12_0_arm64.whl (30 kB)
Downloading google_genai-1.27.0-py3-none-any.whl (218 kB)
Using cached google_resumable_media-2.7.2-py2.py3-none-any.whl (81 kB)
Using cached googleapis_common_protos-1.70.0-py3-none-any.whl (294 kB)
Using cached grpc_google_iam_v1-0.14.2-py3-none-any.whl (19 kB)
Downloading grpcio_status-1.74.0-py3-none-any.whl (14 kB)
Using cached proto_plus-1.26.1-py3-none-any.whl (50 kB)
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Using cached shapely-2.1.1-cp312-cp312-macosx_11_0_arm64.whl (1.6 MB)
Using cached tenacity-8.5.0-py3-none-any.whl (28 kB)
Downloading websockets-15.0.1-cp312-cp312-macosx_11_0_arm64.whl (173 kB)
Using cached pyasn1_modules-0.4.2-py3-none-any.whl (181 kB)
Installing collected packages: websockets, tenacity, shapely, python-dateutil, pyasn1-modules, proto-plus, googleapis-common-protos, google-crc32c, docstring_parser, cachetools, grpcio-status, google-resumable-media, google-auth, grpc-google-iam-v1, google-genai, google-api-core, google-cloud-core, google-cloud-storage, google-cloud-resource-manager, google-cloud-bigquery, google-cloud-aiplatform

Successfully installed cachetools-5.5.2 docstring_parser-0.17.0 google-api-core-2.25.1 google-auth-2.40.3 google-cloud-aiplatform-1.105.0 google-cloud-bigquery-3.35.1 google-cloud-core-2.4.3 google-cloud-resource-manager-1.14.2 google-cloud-storage-2.19.0 google-crc32c-1.7.1 google-genai-1.27.0 google-resumable-media-2.7.2 googleapis-common-protos-1.70.0 grpc-google-iam-v1-0.14.2 grpcio-status-1.74.0 proto-plus-1.26.1 pyasn1-modules-0.4.2 python-dateutil-2.9.0.post0 shapely-2.1.1 tenacity-8.5.0 websockets-15.0.1
INFO:     Will watch for changes in these directories: ['/Users/<USER>/metaspaces/mono/services/meta-agent/backend']
ERROR:    [Errno 48] Address already in use
