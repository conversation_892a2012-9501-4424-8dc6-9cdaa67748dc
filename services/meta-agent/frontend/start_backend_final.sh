#!/bin/bash
# Start backend server with all dependencies

# Change to backend directory
cd ../backend

# Activate virtual environment
source venv/bin/activate

# Install all requirements
echo "Installing backend dependencies..."
pip install -r requirements.txt 2>/dev/null

# Run server as specified by user
echo "Starting backend server..."
python3 -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload