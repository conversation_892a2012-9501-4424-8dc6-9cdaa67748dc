{"name": "ai-agent-platform-frontend", "version": "2.0.0", "private": true, "description": "AI Agent Platform - Frontend Application with Tailwind & ShadCN", "scripts": {"dev": "vite", "build": "vite build", "start": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "add": "npx shadcn-ui@latest add", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config=jest.config.integration.js", "test:integration:watch": "jest --config=jest.config.integration.js --watch", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "test:e2e:critical": "playwright test agent-generation.critical.spec.ts", "test:browser": "playwright test browser-test-suite.spec.ts", "test:all": "npm run test && npm run test:e2e", "playwright:install": "playwright install", "playwright:report": "playwright show-report", "dev:all": "concurrently \"npm run dev\" \"cd ../backend && make dev\""}, "dependencies": {"@headlessui/react": "^2.0.4", "@hookform/resolvers": "^3.3.2", "@playwright/test": "^1.54.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@tabler/icons-react": "^3.6.0", "@tanstack/react-query": "^5.8.4", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@xyflow/react": "^12.3.6", "ace-builds": "^1.41.0", "ag-grid-community": "^32.0.2", "ag-grid-react": "^32.0.2", "axios": "^1.6.2", "base64-js": "^1.5.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "dompurify": "^3.2.4", "elkjs": "^0.9.3", "fetch-intercept": "^2.4.0", "file-saver": "^2.0.5", "framer-motion": "^11.2.10", "fuse.js": "^7.0.0", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "pako": "^2.1.0", "react": "^18.2.0", "react-ace": "^11.0.1", "react-cookie": "^8.0.1", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hotkeys-hook": "^5.1.0", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^0.0.55", "react-router-dom": "^6.26.2", "reactflow": "^11.11.3", "recharts": "^2.8.0", "remark-gfm": "^4.0.0", "short-unique-id": "^5.2.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.2", "uuid": "^10.0.0", "vanilla-jsoneditor": "^2.3.3", "vaul": "^0.7.9", "whatwg-fetch": "^3.6.20", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.7", "@types/jest": "^30.0.0", "@types/react-resizable": "^3.0.7", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.16", "concurrently": "^9.2.0", "eslint": "^8.54.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "nodemon": "^3.1.10", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "ts-jest": "^29.4.0", "vite": "^5.4.1"}}