#!/bin/bash
# Start backend server with debug output

echo "Starting backend server with debug output..."

# Change to backend directory
cd ../backend

# Activate virtual environment
source venv/bin/activate

# Show Python and uvicorn versions
echo "Python version: $(python3 --version)"
echo "Uvicorn version: $(pip show uvicorn | grep Version)"

# Run server with output
echo "Starting server..."
python3 -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload