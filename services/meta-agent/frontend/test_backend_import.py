#!/usr/bin/env python3
import sys
import os

# Add backend src to path
backend_src = os.path.abspath('../backend/src')
sys.path.insert(0, backend_src)

print(f"Python path includes: {backend_src}")
print("Testing backend imports...")

try:
    print("1. Importing config.settings...")
    from config.settings import settings
    print("   ✓ Settings imported successfully")
    print(f"   App name: {settings.app_name}")
    print(f"   Port: {settings.port}")
except Exception as e:
    print(f"   ✗ Failed: {type(e).__name__}: {e}")

try:
    print("\n2. Importing main module...")
    import main
    print("   ✓ Main module imported successfully")
    print(f"   FastAPI app exists: {hasattr(main, 'app')}")
except Exception as e:
    print(f"   ✗ Failed: {type(e).__name__}: {e}")

print("\nDone.")