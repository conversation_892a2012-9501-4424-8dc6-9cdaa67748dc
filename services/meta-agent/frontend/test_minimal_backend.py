#!/usr/bin/env python3
import os
import sys

# Change to backend and add to path
backend_dir = os.path.abspath('../backend')
src_dir = os.path.join(backend_dir, 'src')
sys.path.insert(0, src_dir)
os.chdir(backend_dir)

print("Testing minimal backend startup...")

# Load environment
from dotenv import load_dotenv
load_dotenv(os.path.join(src_dir, '.env'))

# Try to import and check what's happening
try:
    print("1. Importing settings...")
    from config.settings import settings
    print(f"   ✓ Settings loaded - Debug: {settings.debug}")
    
    print("2. Checking database URL...")
    db_url = os.getenv("DATABASE_URL", "not set")
    print(f"   Database URL: {db_url}")
    
    print("3. Testing main app import...")
    # Temporarily mock some services to see where it fails
    import sys
    import types
    
    # Mock the message queue service
    mock_mq = types.SimpleNamespace()
    mock_mq.connect = lambda: None
    mock_mq.disconnect = lambda: None
    mock_mq.subscribe_to_topic = lambda *args: None
    sys.modules['services.messaging'] = types.SimpleNamespace(
        message_queue_service=mock_mq,
        MessageHandlers=types.SimpleNamespace()
    )
    
    # Mock AI integration service  
    mock_ai = types.SimpleNamespace()
    mock_ai.initialize = lambda: None
    sys.modules['services.ai_integration'] = types.SimpleNamespace(
        ai_integration_service=mock_ai
    )
    
    from main import app
    print("   ✓ App imported successfully")
    print(f"   App type: {type(app)}")
    print(f"   App title: {app.title}")
    
except Exception as e:
    print(f"   ✗ Error: {type(e).__name__}: {e}")
    import traceback
    traceback.print_exc()