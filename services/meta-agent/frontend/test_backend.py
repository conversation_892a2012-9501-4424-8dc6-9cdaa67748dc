#!/usr/bin/env python3
import requests
import time

print("Testing backend connectivity...")

# Give backend time to fully start
time.sleep(2)

endpoints = [
    ("http://localhost:8000/", "Root endpoint"),
    ("http://localhost:8000/health", "Health check"),
    ("http://localhost:8000/api/v1", "API v1"),
    ("http://localhost:8000/docs", "API documentation"),
]

for url, desc in endpoints:
    try:
        response = requests.get(url, timeout=5)
        print(f"\n✓ {desc} ({url}):")
        print(f"  Status: {response.status_code}")
        if response.headers.get('content-type', '').startswith('application/json'):
            print(f"  Response: {response.json()}")
        else:
            print(f"  Response: {response.text[:100]}...")
    except requests.exceptions.ConnectionError:
        print(f"\n✗ {desc} ({url}): Connection refused")
    except requests.exceptions.Timeout:
        print(f"\n✗ {desc} ({url}): Request timed out")
    except Exception as e:
        print(f"\n✗ {desc} ({url}): {type(e).__name__}: {e}")