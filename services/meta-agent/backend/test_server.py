#!/usr/bin/env python3
"""
Minimal test server to verify basic FastAPI functionality
"""
import sys
import os

# Add the src directory to Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(backend_dir, 'src')
sys.path.insert(0, src_dir)
os.chdir(src_dir)
os.environ['PYTHONPATH'] = src_dir

try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    
    # Create minimal FastAPI app
    app = FastAPI(
        title="Meta-Agent Backend Test",
        description="Test server for meta-agent backend",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        return {
            "message": "Meta-Agent Backend Test Server",
            "status": "running",
            "version": "1.0.0"
        }
    
    @app.get("/health")
    async def health():
        return {"status": "healthy", "service": "meta-agent-backend"}
    
    @app.get("/test")
    async def test():
        return {"test": "success", "message": "Backend is working correctly"}
    
    if __name__ == "__main__":
        print("Starting minimal test server...")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
        
except Exception as e:
    print(f"Error starting test server: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)