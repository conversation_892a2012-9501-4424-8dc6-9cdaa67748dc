#!/usr/bin/env python3
"""
Simple script to start the meta-agent backend server
"""
import sys
import os

# Add the src directory to Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(backend_dir, 'src')
sys.path.insert(0, src_dir)

# Change to src directory
os.chdir(src_dir)

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = src_dir

try:
    import uvicorn
    print("Starting meta-agent backend server...")
    print(f"Source directory: {src_dir}")
    print(f"PYTHONPATH: {os.environ.get('PYTHONPATH')}")
    
    # Run the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
except Exception as e:
    print(f"Error starting server: {e}")
    sys.exit(1)