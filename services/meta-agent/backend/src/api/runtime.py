"""
AI Agent Platform - Runtime Management API Routes
"""

from typing import List, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status
import structlog

from agents.manager import agent_manager
from services.messaging import message_queue_service
from api.schemas import RuntimeInfoResponse, SystemStatsResponse, SystemHealthResponse, TaskExecutionRequest, TaskExecutionResponse, ResourceUsageResponse, TaskStatisticsResponse

logger = structlog.get_logger()

router = APIRouter(prefix="/runtime", tags=["runtime"])


@router.get("/agents", response_model=List[RuntimeInfoResponse])
async def list_active_agents():
    """List all active agent runtimes"""
    try:
        active_agents = await agent_manager.list_active_agents()
        return [RuntimeInfoResponse(**agent) for agent in active_agents]
        
    except Exception as e:
        logger.error("Failed to list active agents", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list active agents"
        )


@router.get("/agents/{agent_id}/info", response_model=RuntimeInfoResponse)
async def get_agent_runtime_info(agent_id: UUID):
    """Get runtime information for a specific agent"""
    try:
        runtime_info = await agent_manager.get_runtime_info(agent_id)
        
        if not runtime_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent runtime not found"
            )
        
        return RuntimeInfoResponse(**runtime_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent runtime info", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent runtime info"
        )


@router.post("/agents/{agent_id}/pause")
async def pause_agent_runtime(agent_id: UUID):
    """Pause an agent runtime"""
    try:
        success = await agent_manager.pause_agent(agent_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent runtime not found or cannot be paused"
            )
        
        return {"message": "Agent paused successfully", "agent_id": str(agent_id)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to pause agent", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to pause agent"
        )


@router.post("/agents/{agent_id}/resume")
async def resume_agent_runtime(agent_id: UUID):
    """Resume an agent runtime"""
    try:
        success = await agent_manager.resume_agent(agent_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent runtime not found or cannot be resumed"
            )
        
        return {"message": "Agent resumed successfully", "agent_id": str(agent_id)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to resume agent", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resume agent"
        )


@router.post("/agents/{agent_id}/execute-task", response_model=TaskExecutionResponse)
async def execute_task_on_agent(agent_id: UUID, task_request: TaskExecutionRequest):
    """Execute a task on a specific agent"""
    try:
        task_data = {
            'id': str(task_request.task_id) if task_request.task_id else None,
            'type': task_request.task_type,
            'input_data': task_request.input_data,
            'metadata': task_request.metadata or {}
        }
        
        result = await agent_manager.execute_task(agent_id, task_data)
        
        return TaskExecutionResponse(
            task_id=task_data.get('id'),
            agent_id=agent_id,
            status="completed",
            result=result,
            message="Task executed successfully"
        )
        
    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to execute task", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute task"
        )


@router.post("/agents/{agent_id}/queue-task")
async def queue_task_for_agent(agent_id: UUID, task_request: TaskExecutionRequest):
    """Add a task to an agent's execution queue"""
    try:
        task_data = {
            'id': str(task_request.task_id) if task_request.task_id else None,
            'type': task_request.task_type,
            'input_data': task_request.input_data,
            'metadata': task_request.metadata or {}
        }
        
        success = await agent_manager.add_task_to_queue(agent_id, task_data)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent runtime not found"
            )
        
        return {
            "message": "Task added to queue successfully",
            "agent_id": str(agent_id),
            "task_id": task_data.get('id')
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to queue task", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to queue task"
        )


@router.get("/system/stats", response_model=SystemStatsResponse)
async def get_system_stats():
    """Get system statistics"""
    try:
        stats = await agent_manager.get_system_stats()

        # Transform the structure to match frontend expectations
        resource_usage = stats.get("resource_usage", {})
        task_statistics = stats.get("task_statistics", {})

        transformed_stats = {
            "total_agents": stats.get("total_agents", 0),
            "active_agents": stats.get("status_breakdown", {}).get("running", 0),
            "cpu_usage": resource_usage.get("total_cpu_usage", 0),
            "memory_usage": resource_usage.get("total_memory_mb", 0),
            "disk_usage": 0,  # TODO: Add disk usage tracking
            "uptime": "0h 0m",  # TODO: Add uptime tracking
            "success_rate": 95.0,  # TODO: Calculate actual success rate
            "resource_usage": ResourceUsageResponse(
                total_cpu_usage=resource_usage.get("total_cpu_usage", 0),
                total_memory_mb=resource_usage.get("total_memory_mb", 0),
                average_cpu_per_agent=resource_usage.get("average_cpu_per_agent", 0),
                average_memory_per_agent=resource_usage.get("average_memory_per_agent", 0)
            ),
            "task_statistics": TaskStatisticsResponse(
                queued_tasks=task_statistics.get("queued_tasks", 0),
                completed_tasks=task_statistics.get("completed_tasks", 0)
            )
        }

        return SystemStatsResponse(**transformed_stats)

    except Exception as e:
        logger.error("Failed to get system stats", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system stats"
        )


@router.get("/system/health", response_model=SystemHealthResponse)
async def get_system_health():
    """Get system health status"""
    try:
        health_info = await agent_manager.health_check()
        
        # Determine overall health status
        healthy_agents = health_info.get('healthy_agents', 0)
        unhealthy_agents = health_info.get('unhealthy_agents', 0)
        total_agents = health_info.get('total_agents', 0)
        
        if total_agents == 0:
            overall_status = "no_agents"
        elif unhealthy_agents == 0:
            overall_status = "healthy"
        elif healthy_agents > 0:
            overall_status = "degraded"
        else:
            overall_status = "unhealthy"
        
        # Map overall_status to SystemHealthResponse status
        if overall_status == "healthy":
            status_mapped = "healthy"
        elif overall_status in ["degraded", "no_agents"]:
            status_mapped = "degraded"
        else:
            status_mapped = "unhealthy"

        return SystemHealthResponse(
            status=status_mapped,
            version="1.0.0",  # TODO: Get from settings
            uptime="0h 0m",   # TODO: Calculate actual uptime
            cpu_usage=0.0,    # TODO: Get actual CPU usage
            memory_usage=0.0, # TODO: Get actual memory usage
            memory_mb=0,      # TODO: Get actual memory usage in MB
            components={
                "agents": {
                    "status": overall_status,
                    "healthy_count": healthy_agents,
                    "unhealthy_count": unhealthy_agents,
                    "total_count": total_agents
                },
                "message_queue": {
                    "status": "connected" if message_queue_service.is_connected else "disconnected"
                }
            }
        )
        
    except Exception as e:
        logger.error("Failed to get system health", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system health"
        )


@router.post("/system/shutdown")
async def shutdown_all_agents():
    """Shutdown all agent runtimes (admin only)"""
    try:
        # TODO: Add admin authorization check
        
        success = await agent_manager.shutdown_all_agents(graceful=True)
        
        if success:
            return {"message": "All agents shutdown successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to shutdown all agents"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to shutdown all agents", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to shutdown all agents"
        )


@router.get("/messaging/status")
async def get_messaging_status():
    """Get message queue service status"""
    try:
        consumer_info = await message_queue_service.get_consumer_info()
        
        return {
            "connected": message_queue_service.is_connected,
            "topics": message_queue_service.topics,
            "active_subscriptions": consumer_info.get('active_subscriptions', []),
            "subscription_count": consumer_info.get('subscription_count', 0)
        }
        
    except Exception as e:
        logger.error("Failed to get messaging status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get messaging status"
        )


@router.post("/messaging/connect")
async def connect_message_queue():
    """Connect to message queue service"""
    try:
        success = await message_queue_service.connect()
        
        if success:
            return {"message": "Connected to message queue successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to connect to message queue"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to connect to message queue", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect to message queue"
        )