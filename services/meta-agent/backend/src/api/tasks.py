"""
AI Agent Platform - Tasks API Routes
"""

from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
import structlog

from database.connection import get_db
from database.models import Task, TaskStatus
from api.schemas import TaskResponse, TaskListResponse

logger = structlog.get_logger()

router = APIRouter(prefix="/tasks", tags=["tasks"])


@router.get("/", response_model=TaskListResponse)
async def list_tasks(
    status_filter: Optional[TaskStatus] = Query(None, alias="status"),
    task_type: Optional[str] = Query(None, alias="type"),
    agent_id: Optional[UUID] = None,
    limit: int = Query(100, ge=1, le=1000, alias="size"),
    offset: int = Query(0, ge=0, alias="page"),
    db: AsyncSession = Depends(get_db)
):
    """List tasks with optional filters"""
    try:
        # Build query
        query = select(Task)
        
        if status_filter:
            query = query.where(Task.status == status_filter)
        
        if task_type:
            query = query.where(Task.type == task_type)
            
        if agent_id:
            query = query.where(Task.assigned_agent_id == agent_id)
        
        # Apply pagination
        query = query.limit(limit).offset(offset * limit if offset > 0 else 0)
        
        # Execute query
        result = await db.execute(query)
        tasks = list(result.scalars().all())
        
        # Get total count
        count_query = select(func.count(Task.id))
        if status_filter:
            count_query = count_query.where(Task.status == status_filter)
        if task_type:
            count_query = count_query.where(Task.type == task_type)
        if agent_id:
            count_query = count_query.where(Task.assigned_agent_id == agent_id)
            
        count_result = await db.execute(count_query)
        total_count = count_result.scalar() or 0
        
        # Convert to response format
        task_responses = []
        for task in tasks:
            try:
                task_responses.append(TaskResponse.model_validate(task))
            except Exception as e:
                logger.error("Failed to serialize task", task_id=str(task.id), error=str(e))
                continue
        
        return TaskListResponse(
            tasks=task_responses,
            total=total_count,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error("Failed to list tasks via API", error=str(e), error_type=type(e).__name__)
        import traceback
        logger.error("Stack trace", stack_trace=traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list tasks: {str(e)}"
        )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Get task by ID"""
    try:
        result = await db.execute(
            select(Task).where(Task.id == task_id)
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        return TaskResponse.model_validate(task)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get task via API", task_id=str(task_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve task"
        )