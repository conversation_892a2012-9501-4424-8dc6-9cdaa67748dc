"""
AI Agent Platform - API Package
"""

from fastapi import APIRouter
from api.agents import router as agents_router
from api.tasks import router as tasks_router
from api.auth import router as auth_router
from api.runtime import router as runtime_router
from api.orchestration import router as orchestration_router
from api.ai import router as ai_router
from api.generation import router as generation_router
from api.vector_db import router as vector_db_router
from api.a2a import router as a2a_router
# Temporarily disabled due to dependency issues
# from api.google_adk import router as google_adk_router
from api.deployment import router as deployment_router
from api.evolution import router as evolution_router
from api.workflows import router as workflows_router
from api.ai_assistant import router as ai_assistant_router

# Import LangFlow routers - temporarily disabled due to missing dependencies
# from langflow.api import router as langflow_router, health_check_router, log_router

# Create main API router
api_router = APIRouter()

# Include all meta-agent sub-routers
api_router.include_router(auth_router)  # Now includes OAuth, MFA, and RBAC
api_router.include_router(agents_router)
api_router.include_router(tasks_router)
api_router.include_router(orchestration_router)
api_router.include_router(runtime_router)
api_router.include_router(ai_router)
api_router.include_router(generation_router)
api_router.include_router(vector_db_router)
api_router.include_router(a2a_router)
# api_router.include_router(google_adk_router)
api_router.include_router(deployment_router)
api_router.include_router(evolution_router)
api_router.include_router(workflows_router)
api_router.include_router(ai_assistant_router)

# Include LangFlow routers under /langflow prefix - temporarily disabled
# api_router.include_router(langflow_router, prefix="/langflow")
# api_router.include_router(health_check_router, prefix="/langflow")
# api_router.include_router(log_router, prefix="/langflow")

# TODO: Add other routers as they are implemented
# api_router.include_router(intelligence_router)