"""
Full-Stack Code Generator Service
Generates complete agent applications with API endpoints, database models, and business logic
"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
import structlog
from jinja2 import Environment, FileSystemLoader, Template

from .ai_service import ai_service, AIRequest, AICapability
from .frontend_generator import FrontendGenerator, extract_api_endpoints_from_code

logger = structlog.get_logger()

class CodeGenerator:
    """Generates full-stack agent code based on requirements"""
    
    def __init__(self):
        self.templates_dir = Path(__file__).parent.parent / "templates"
        self.templates_dir.mkdir(exist_ok=True)
        self.jinja_env = Environment(loader=FileSystemLoader(str(self.templates_dir)))
        
        # Add custom filters
        self.jinja_env.filters['tojsonfilter'] = lambda x: json.dumps(x)
        
        # Initialize frontend generator
        self.frontend_generator = FrontendGenerator(ai_service)
        
    async def generate_agent_code(self, config: Dict[str, Any], requirements: str) -> Dict[str, Any]:
        """Generate complete agent codebase with optimized AI calls"""
        logger.info("Generating agent code", agent_name=config.get("name"))
        
        # Generate different components with minimal AI calls
        components = {}

        # 1. Generate main API code (includes most functionality)
        components["api"] = await self._generate_api_code(config, requirements)

        # 2. Generate static configuration files (no AI needed)
        components["config"] = await self._generate_config_files(config)

        # 3. Generate Docker files (no AI needed)
        components["docker"] = await self._generate_docker_files(config)

        # 4. Generate basic documentation (no AI needed)
        components["docs"] = await self._generate_documentation(config, requirements)
        
        # 5. Generate frontend (if requested)
        if config.get("generate_frontend", True):  # Default to True for full-stack
            components["frontend"] = await self._generate_frontend_component(config, requirements, components)
        
        return {
            "agent_name": config.get("name"),
            "language": config.get("language"),
            "framework": config.get("framework"),
            "components": components,
            "deployment_config": config.get("deployment", {}),
            "generated_at": "2024-01-15T12:00:00Z"
        }
    
    async def _generate_api_code(self, config: Dict[str, Any], requirements: str) -> Dict[str, str]:
        """Generate API endpoint code"""
        language = config.get("language", "python")
        framework = config.get("framework", "fastapi")
        capabilities = config.get("capabilities", [])
        
        if language == "python" and framework == "fastapi":
            return await self._generate_fastapi_code(config, requirements, capabilities)
        elif language == "javascript" and framework == "express":
            return await self._generate_express_code(config, requirements, capabilities)
        else:
            return await self._generate_generic_api_code(config, requirements, capabilities)
    
    async def _generate_fastapi_code(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> Dict[str, str]:
        """AI-based FastAPI code generation with A2A protocol support - Single optimized call"""
        
        # Generate complete application in one AI call to avoid timeouts
        complete_app_prompt = f"""
        Generate a complete, production-ready FastAPI application for an AI agent.

        Requirements: {requirements}
        
        Agent Configuration:
        - Name: {config.get('name')}
        - Type: {config.get('type')}
        - Capabilities: {', '.join(capabilities)}
        
        Generate a SINGLE Python file that includes:
        
        1. ALL necessary imports (os, uuid, datetime, FastAPI, CORSMiddleware, BaseModel from pydantic)
        2. FastAPI app initialization with CORS middleware
        3. Generate unique AGENT_ID using uuid.uuid4()
        4. Root endpoint ("/") returning A2A agent information with ALL these fields:
           {{
               "agent_id": "generated-uuid",
               "name": "{config.get('name')}",
               "type": "{config.get('type')}",
               "capabilities": {capabilities},
               "status": "running",
               "version": "1.0.0",
               "description": "Brief description based on requirements",
               "created_at": "current timestamp",
               "a2a_protocol": "v1.0",
               "endpoints": [list of available endpoints]
           }}
        5. Health check endpoint ("/health")
        6. A2A info endpoint ("/a2a/info") 
        7. A2A capabilities endpoint ("/a2a/capabilities")
        8. Functional endpoints that implement the specific requirements
        9. Proper error handling and input validation using Pydantic models
        10. The uvicorn runner at the bottom:
            if __name__ == "__main__":
                import uvicorn
                port = int(os.getenv("PORT", 8000))
                uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
        
        Make sure the agent actually implements the functionality described in the requirements.
        Include proper docstrings and error handling.
        
        CRITICAL: Return ONLY raw Python code - no markdown blocks, no ```python, no explanations.
        Start directly with the imports.
        """
        
        main_response = await ai_service.generate(AIRequest(
            prompt=complete_app_prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=4000
        ))
        
        # Clean up any markdown formatting
        code_content = main_response.content.strip()
        if code_content.startswith('```python'):
            # Remove markdown code blocks
            lines = code_content.split('\n')
            # Remove first line (```python) and last line (```)
            if lines[-1].strip() == '```':
                lines = lines[1:-1]
            else:
                lines = lines[1:]  # Just remove first line
            code_content = '\n'.join(lines)
        
        # Generate simple requirements.txt without AI call
        requirements_content = """fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6
pydantic==2.5.0"""
        
        # Add specific requirements based on capabilities
        if "mathematics" in capabilities or "calculation" in capabilities:
            requirements_content += "\nnumpy==1.24.3\nscipy==1.11.4"
        if any("data" in cap for cap in capabilities):
            requirements_content += "\npandas==2.1.4"
        if "api_integration" in capabilities or "weather" in config.get("name", "").lower():
            requirements_content += "\nrequests==2.31.0\naiohttp==3.9.1"
        if "information_retrieval" in capabilities:
            requirements_content += "\nrequests==2.31.0\naiohttp==3.9.1"
        
        return {
            "main.py": code_content,
            "requirements.txt": requirements_content
        }
    
    async def _generate_routes_code(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> str:
        """Generate API routes based on capabilities"""
        
        routes_prompt = f"""
        Generate FastAPI router code for an agent with these capabilities:
        {', '.join(capabilities)}
        
        Requirements: {requirements}
        
        Create appropriate API endpoints for each capability. For example:
        - If 'data_processing' capability: POST /process-data
        - If 'conversation' capability: POST /chat
        - If 'analysis' capability: POST /analyze
        - If 'monitoring' capability: GET /metrics
        
        Include:
        1. Proper FastAPI router setup
        2. Pydantic request/response models
        3. Error handling
        4. Input validation
        5. Async endpoint functions
        6. Proper HTTP status codes
        
        Generate clean, well-documented Python code.
        """
        
        response = await ai_service.generate(AIRequest(
            prompt=routes_prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=2000
        ))
        
        return response.content
    
    async def _generate_express_code(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> Dict[str, str]:
        """Generate Express.js application code"""
        
        main_app_prompt = f"""
        Generate a complete Express.js application for an AI agent with these requirements:
        {requirements}
        
        Agent configuration:
        - Name: {config.get('name')}
        - Capabilities: {', '.join(capabilities)}
        - Type: {config.get('type')}
        
        Include:
        1. Main Express app with proper imports
        2. Middleware setup (CORS, body parser, logging)
        3. Health check endpoint
        4. API routes based on capabilities
        5. Error handling middleware
        6. Proper TypeScript types (if applicable)
        
        Generate clean, production-ready JavaScript/TypeScript code.
        """
        
        response = await ai_service.generate(AIRequest(
            prompt=main_app_prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=2500
        ))
        
        return {
            "app.js": response.content,
            "package.json": await self._generate_package_json(config),
            "routes/index.js": await self._generate_express_routes(config, requirements, capabilities)
        }
    
    async def _generate_generic_api_code(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> Dict[str, str]:
        """Generate generic API code for other languages/frameworks"""
        
        prompt = f"""
        Generate a complete API application in {config.get('language')} using {config.get('framework')} 
        for an AI agent with these requirements:
        {requirements}
        
        Capabilities: {', '.join(capabilities)}
        
        Include all necessary files for a production-ready application.
        """
        
        response = await ai_service.generate(AIRequest(
            prompt=prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=3000
        ))
        
        return {"main": response.content}

    async def _generate_a2a_integration(self, config: Dict[str, Any], requirements: str) -> Dict[str, str]:
        """Generate Agent-to-Agent protocol integration code"""

        language = config.get("language", "python")
        capabilities = config.get("capabilities", [])

        if language == "python":
            return await self._generate_python_a2a_integration(config, requirements, capabilities)
        elif language == "javascript":
            return await self._generate_javascript_a2a_integration(config, requirements, capabilities)
        else:
            return await self._generate_generic_a2a_integration(config, requirements, capabilities)

    async def _generate_python_a2a_integration(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> Dict[str, str]:
        """Generate Python A2A protocol integration"""

        a2a_client_prompt = f"""
        Generate a Python A2A (Agent-to-Agent) protocol client for an agent with these requirements:
        {requirements}

        Agent configuration:
        - Name: {config.get('name')}
        - Capabilities: {', '.join(capabilities)}
        - Type: {config.get('type')}

        Include:
        1. A2A client class with methods for:
           - Registering with the A2A network
           - Discovering other agents
           - Sending messages to other agents
           - Receiving and processing messages
           - Heartbeat management
        2. Message handlers for different actions
        3. Capability registration and advertisement
        4. Error handling and retry logic
        5. Async/await patterns
        6. Integration with the main agent application

        Use these imports:
        - aiohttp for HTTP communication
        - asyncio for async operations
        - logging for error handling
        - typing for type hints

        Generate clean, production-ready Python code with proper error handling.
        """

        a2a_client_response = await ai_service.generate(AIRequest(
            prompt=a2a_client_prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=3000
        ))

        return {
            "a2a_client.py": a2a_client_response.content,
            "a2a_config.py": self._generate_a2a_config_file(config)
        }

    async def _generate_javascript_a2a_integration(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> Dict[str, str]:
        """Generate JavaScript A2A protocol integration"""

        a2a_client_prompt = f"""
        Generate a JavaScript/Node.js A2A (Agent-to-Agent) protocol client for an agent with these requirements:
        {requirements}

        Agent configuration:
        - Name: {config.get('name')}
        - Capabilities: {', '.join(capabilities)}
        - Type: {config.get('type')}

        Include:
        1. A2A client class with methods for agent communication
        2. Message handling and routing
        3. Capability advertisement
        4. Heartbeat management
        5. Error handling and retries
        6. Integration with Express.js application

        Use modern JavaScript with async/await and proper error handling.
        """

        a2a_client_response = await ai_service.generate(AIRequest(
            prompt=a2a_client_prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=2500
        ))

        return {
            "a2aClient.js": a2a_client_response.content
        }

    async def _generate_generic_a2a_integration(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> Dict[str, str]:
        """Generate generic A2A protocol integration"""

        prompt = f"""
        Generate A2A (Agent-to-Agent) protocol integration code in {config.get('language')}
        for an agent with these requirements:
        {requirements}

        Capabilities: {', '.join(capabilities)}

        Include agent communication, message handling, and capability advertisement.
        """

        response = await ai_service.generate(AIRequest(
            prompt=prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=2000
        ))

        return {"a2a_integration": response.content}

    def _generate_a2a_config_file(self, config: Dict[str, Any]) -> str:
        """Generate A2A configuration file"""

        return f"""
# A2A Protocol Configuration
A2A_PLATFORM_URL = "http://localhost:8000"
A2A_AGENT_ID = "{config.get('name', 'agent')}-{{uuid4()}}"
A2A_AGENT_TYPE = "{config.get('type', 'api_service')}"
A2A_HEARTBEAT_INTERVAL = 60  # seconds
A2A_MESSAGE_TIMEOUT = 30  # seconds
A2A_RETRY_ATTEMPTS = 3
A2A_DISCOVERY_INTERVAL = 300  # seconds

# Agent capabilities for A2A advertisement
A2A_CAPABILITIES = {config.get('capabilities', [])}

# A2A message handlers mapping
A2A_HANDLERS = {{
    "ping": "handle_ping",
    "get_capabilities": "handle_get_capabilities",
    "get_status": "handle_get_status",
    "execute_task": "handle_execute_task"
}}
"""

    async def _generate_models_code(self, config: Dict[str, Any], requirements: str) -> Dict[str, str]:
        """Generate database models"""
        
        if "database" not in config.get("capabilities", []):
            return {}
        
        prompt = f"""
        Generate database models for an agent with these requirements:
        {requirements}
        
        Language: {config.get('language')}
        
        Include:
        1. Data models based on the agent's purpose
        2. Proper relationships between models
        3. Validation rules
        4. Database migration scripts (if applicable)
        
        Generate clean, well-structured code.
        """
        
        response = await ai_service.generate(AIRequest(
            prompt=prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=2000
        ))
        
        return {"models.py": response.content}
    
    async def _generate_services_code(self, config: Dict[str, Any], requirements: str) -> Dict[str, str]:
        """Generate business logic services"""
        
        prompt = f"""
        Generate business logic services for an agent with these requirements:
        {requirements}
        
        Capabilities: {', '.join(config.get('capabilities', []))}
        Language: {config.get('language')}
        
        Include:
        1. Service classes for each major capability
        2. Proper error handling
        3. Logging
        4. Input validation
        5. Async/await patterns (if applicable)
        
        Generate modular, testable code.
        """
        
        response = await ai_service.generate(AIRequest(
            prompt=prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=2500
        ))
        
        return {"services.py": response.content}
    
    async def _generate_config_files(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Generate configuration files"""
        
        files = {}
        
        # Environment configuration
        env_content = f"""
# Agent Configuration
AGENT_NAME={config.get('name', 'ai-agent')}
AGENT_TYPE={config.get('type', 'api_service')}
PORT={config.get('deployment', {}).get('port', 8000)}
LOG_LEVEL=INFO

# Database (if needed)
DATABASE_URL=sqlite:///./agent.db

# AI Provider (if needed)
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
"""
        files[".env"] = env_content
        
        return files
    
    async def _generate_docker_files(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Generate Docker configuration"""
        
        language = config.get("language", "python")
        
        if language == "python":
            dockerfile = f"""FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 agent
RUN chown -R agent:agent /app
USER agent

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["python", "main.py"]
"""
        else:
            dockerfile = f"""
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE {config.get('deployment', {}).get('port', 8000)}

CMD ["npm", "start"]
"""
        
        docker_compose = f"""
version: '3.8'

services:
  {config.get('name', 'agent')}:
    build: .
    ports:
      - "{config.get('deployment', {}).get('port', 8000)}:{config.get('deployment', {}).get('port', 8000)}"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
"""
        
        return {
            "Dockerfile": dockerfile,
            "docker-compose.yml": docker_compose
        }
    
    async def _generate_test_files(self, config: Dict[str, Any], requirements: str) -> Dict[str, str]:
        """Generate test files"""
        
        prompt = f"""
        Generate comprehensive test files for an agent with these requirements:
        {requirements}
        
        Language: {config.get('language')}
        Framework: {config.get('framework')}
        Capabilities: {', '.join(config.get('capabilities', []))}
        
        Include:
        1. Unit tests for all major functions
        2. Integration tests for API endpoints
        3. Test fixtures and mocks
        4. Test configuration
        
        Generate clean, comprehensive test code.
        """
        
        response = await ai_service.generate(AIRequest(
            prompt=prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=2000
        ))
        
        return {"test_main.py": response.content}
    
    async def _generate_documentation(self, config: Dict[str, Any], requirements: str) -> Dict[str, str]:
        """Generate documentation"""
        
        readme_content = f"""
# {config.get('name', 'AI Agent')}

## Description
{requirements}

## Capabilities
{chr(10).join(f'- {cap}' for cap in config.get('capabilities', []))}

## Technology Stack
- Language: {config.get('language')}
- Framework: {config.get('framework')}
- Type: {config.get('type')}

## Installation

1. Clone the repository
2. Install dependencies
3. Configure environment variables
4. Run the application

## API Endpoints

See the generated API documentation for detailed endpoint information.

## Deployment

This agent can be deployed using Docker:

```bash
docker-compose up -d
```

## Configuration

Configure the agent using environment variables in the `.env` file.
"""
        
        return {"README.md": readme_content}
    
    async def _generate_package_json(self, config: Dict[str, Any]) -> str:
        """Generate package.json for Node.js projects"""
        
        package_json = {
            "name": config.get('name', 'ai-agent'),
            "version": "1.0.0",
            "description": "AI Agent generated application",
            "main": "app.js",
            "scripts": {
                "start": "node app.js",
                "dev": "nodemon app.js",
                "test": "jest"
            },
            "dependencies": {
                "express": "^4.18.0",
                "cors": "^2.8.5",
                "helmet": "^6.0.0",
                "morgan": "^1.10.0"
            },
            "devDependencies": {
                "nodemon": "^2.0.0",
                "jest": "^29.0.0"
            }
        }
        
        return json.dumps(package_json, indent=2)
    
    async def _generate_express_routes(self, config: Dict[str, Any], requirements: str, capabilities: List[str]) -> str:
        """Generate Express.js routes"""
        
        prompt = f"""
        Generate Express.js routes for an agent with these capabilities:
        {', '.join(capabilities)}
        
        Requirements: {requirements}
        
        Include proper route handlers, middleware, and error handling.
        """
        
        response = await ai_service.generate(AIRequest(
            prompt=prompt,
            capability=AICapability.CODE_GENERATION,
            temperature=0.2,
            max_tokens=1500
        ))
        
        return response.content

    async def _generate_frontend_component(
        self, 
        config: Dict[str, Any], 
        requirements: str, 
        components: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate frontend application for the agent"""
        try:
            logger.info("Generating frontend component", agent_name=config.get("name"))
            
            # Extract API endpoints from the generated backend code
            api_code = components.get("api", {}).get("main", "")
            api_endpoints = extract_api_endpoints_from_code(api_code)
            
            # Use a temporary path for generation
            temp_frontend_path = Path("/tmp") / f"frontend_{config.get('name', 'agent')}"
            
            # Generate frontend using the frontend generator
            result = await self.frontend_generator.generate_frontend(
                agent_id=config.get("id", "temp"),
                agent_name=config.get("name", "Agent"),
                requirements=requirements,
                api_endpoints=api_endpoints,
                output_path=temp_frontend_path
            )
            
            # If generation was successful, read the files and return as content
            if result["status"] == "success" and temp_frontend_path.exists():
                frontend_files = {}
                for file_path in temp_frontend_path.rglob("*"):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(temp_frontend_path)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                # Add frontend/ prefix to create proper directory structure
                                frontend_files[f"frontend/{relative_path}"] = f.read()
                        except Exception as e:
                            logger.warning(f"Could not read file {relative_path}: {e}")
                
                # Clean up temp directory
                import shutil
                shutil.rmtree(temp_frontend_path, ignore_errors=True)
                
                return frontend_files  # Return files directly for integration into components
            else:
                logger.error("Frontend generation failed", error=result.get("error", "Unknown error"))
                return {}  # Return empty dict on failure
            
        except Exception as e:
            logger.error("Frontend generation failed", error=str(e))
            return {}  # Return empty dict on exception

# Global instance
code_generator = CodeGenerator()
