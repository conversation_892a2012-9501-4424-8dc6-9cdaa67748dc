DEFAULT_SUPERUSER = "langflow"
DEFAULT_SUPERUSER_PASSWORD = "langflow"  # noqa: S105
VARIABLES_TO_GET_FROM_ENVIRONMENT = [
    "COMPOSIO_API_KEY",
    "OPENAI_API_KEY",
    "ANTHROPIC_API_KEY",
    "GOOGLE_API_KEY",
    "AZURE_OPENAI_API_KEY",
    "AZURE_OPENAI_API_VERSION",
    "AZURE_OPENAI_API_INSTANCE_NAME",
    "AZURE_OPENAI_API_DEPLOYMENT_NAME",
    "AZURE_OPENAI_API_EMBEDDINGS_DEPLOYMENT_NAME",
    "ASTRA_DB_APPLICATION_TOKEN",
    "ASTRA_DB_API_ENDPOINT",
    "COHERE_API_KEY",
    "GROQ_API_KEY",
    "HUGGINGFACEHUB_API_TOKEN",
    "PINECONE_API_KEY",
    "SAMBANOVA_API_KEY",
    "SEARCHAPI_API_KEY",
    "SERPAPI_API_KEY",
    "UPSTASH_VECTOR_REST_URL",
    "UPSTASH_VECTOR_REST_TOKEN",
    "VECTARA_CUSTOMER_ID",
    "VECTARA_CORPUS_ID",
    "VECTARA_API_KEY",
    "AWS_ACCESS_KEY_ID",
    "AWS_SECRET_ACCESS_KEY",
    "NOVITA_API_KEY",
    "TAVILY_API_KEY",
]
