{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "YouTubeCommentsComponent", "id": "YouTubeCommentsComponent-y3wJZ", "name": "comments", "output_types": ["DataFrame"]}, "targetHandle": {"fieldName": "df", "id": "BatchRunComponent-30WdR", "inputTypes": ["DataFrame"], "type": "other"}}, "id": "reactflow__edge-YouTubeCommentsComponent-y3wJZ{œdataTypeœ:œYouTubeCommentsComponentœ,œidœ:œYouTubeCommentsComponent-y3wJZœ,œnameœ:œcommentsœ,œoutput_typesœ:[œDataFrameœ]}-BatchRunComponent-30WdR{œfieldNameœ:œdfœ,œidœ:œBatchRunComponent-30WdRœ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "selected": false, "source": "YouTubeCommentsComponent-y3wJZ", "sourceHandle": "{œdataTypeœ: œYouTubeCommentsComponentœ, œidœ: œYouTubeCommentsComponent-y3wJZœ, œnameœ: œcommentsœ, œoutput_typesœ: [œDataFrameœ]}", "target": "BatchRunComponent-30WdR", "targetHandle": "{œfieldNameœ: œdfœ, œidœ: œBatchRunComponent-30WdRœ, œinputTypesœ: [œDataFrameœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-yqoLt", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "Agent-<PERSON><PERSON><PERSON>", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-yqoLt{œdataTypeœ:œPromptœ,œidœ:œPrompt-yqoLtœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-Agent-<PERSON><PERSON><PERSON>{œfieldNameœ:œinput_valueœ,œidœ:œAgent-JRSRuœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-yqoLt", "sourceHandle": "{œdataTypeœ: œPromptœ, œidœ: œPrompt-yqoLtœ, œnameœ: œpromptœ, œoutput_typesœ: [œMessageœ]}", "target": "Agent-<PERSON><PERSON><PERSON>", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œAgent-JRSRuœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Agent", "id": "Agent-<PERSON><PERSON><PERSON>", "name": "response", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-vlskP", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-Agent-<PERSON><PERSON><PERSON>{œdataTypeœ:œAgentœ,œidœ:œAgent-JRSRuœ,œnameœ:œresponseœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-vlskP{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-vlskPœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Agent-<PERSON><PERSON><PERSON>", "sourceHandle": "{œdataTypeœ: œAgentœ, œidœ: œAgent-JRSRuœ, œnameœ: œresponseœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-vlskP", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-vlskPœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "YouTubeTranscripts", "id": "YouTubeTranscripts-TlFcG", "name": "component_as_tool", "output_types": ["Tool"]}, "targetHandle": {"fieldName": "tools", "id": "Agent-<PERSON><PERSON><PERSON>", "inputTypes": ["Tool"], "type": "other"}}, "id": "reactflow__edge-YouTubeTranscripts-TlFcG{œdataTypeœ:œYouTubeTranscriptsœ,œidœ:œYouTubeTranscripts-TlFcGœ,œnameœ:œcomponent_as_toolœ,œoutput_typesœ:[œToolœ]}-Agent-<PERSON><PERSON><PERSON>{œfieldNameœ:œtoolsœ,œidœ:œAgent-JRSRuœ,œinputTypesœ:[œToolœ],œtypeœ:œotherœ}", "selected": false, "source": "YouTubeTranscripts-TlFcG", "sourceHandle": "{œdataTypeœ: œYouTubeTranscriptsœ, œidœ: œYouTubeTranscripts-TlFcGœ, œnameœ: œcomponent_as_toolœ, œoutput_typesœ: [œToolœ]}", "target": "Agent-<PERSON><PERSON><PERSON>", "targetHandle": "{œfieldNameœ: œtoolsœ, œidœ: œAgent-JRSRuœ, œinputTypesœ: [œToolœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "BatchRunComponent", "id": "BatchRunComponent-30WdR", "name": "batch_results", "output_types": ["DataFrame"]}, "targetHandle": {"fieldName": "input_data", "id": "parser-k0Bpy", "inputTypes": ["DataFrame", "Data"], "type": "other"}}, "id": "reactflow__edge-BatchRunComponent-30WdR{œdataTypeœ:œBatchRunComponentœ,œidœ:œBatchRunComponent-30WdRœ,œnameœ:œbatch_resultsœ,œoutput_typesœ:[œDataFrameœ]}-parser-k0Bpy{œfieldNameœ:œinput_dataœ,œidœ:œparser-k0Bpyœ,œinputTypesœ:[œDataFrameœ,œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "BatchRunComponent-30WdR", "sourceHandle": "{œdataTypeœ: œBatchRunComponentœ, œidœ: œBatchRunComponent-30WdRœ, œnameœ: œbatch_resultsœ, œoutput_typesœ: [œDataFrameœ]}", "target": "parser-k0Bpy", "targetHandle": "{œfieldNameœ: œinput_dataœ, œidœ: œparser-k0Bpyœ, œinputTypesœ: [œDataFrameœ, œDataœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "parser", "id": "parser-k0Bpy", "name": "parsed_text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "analysis", "id": "Prompt-yqoLt", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-parser-k0Bpy{œdataTypeœ:œparserœ,œidœ:œparser-k0Bpyœ,œnameœ:œparsed_textœ,œoutput_typesœ:[œMessageœ]}-Prompt-yqoLt{œfieldNameœ:œanalysisœ,œidœ:œPrompt-yqoLtœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "parser-k0Bpy", "sourceHandle": "{œdataTypeœ: œparserœ, œidœ: œparser-k0Bpyœ, œnameœ: œparsed_textœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-yqoLt", "targetHandle": "{œfieldNameœ: œanalysisœ, œidœ: œPrompt-yqoLtœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "LanguageModelComponent", "id": "LanguageModelComponent-OvIt5", "name": "model_output", "output_types": ["LanguageModel"]}, "targetHandle": {"fieldName": "model", "id": "BatchRunComponent-30WdR", "inputTypes": ["LanguageModel"], "type": "other"}}, "id": "reactflow__edge-LanguageModelComponent-OvIt5{œdataTypeœ:œLanguageModelComponentœ,œidœ:œLanguageModelComponent-OvIt5œ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}-BatchRunComponent-30WdR{œfieldNameœ:œmodelœ,œidœ:œBatchRunComponent-30WdRœ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "selected": false, "source": "LanguageModelComponent-OvIt5", "sourceHandle": "{œdataTypeœ: œLanguageModelComponentœ, œidœ: œLanguageModelComponent-OvIt5œ, œnameœ: œmodel_outputœ, œoutput_typesœ: [œLanguageModelœ]}", "target": "BatchRunComponent-30WdR", "targetHandle": "{œfieldNameœ: œmodelœ, œidœ: œBatchRunComponent-30WdRœ, œinputTypesœ: [œLanguageModelœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-kaWcL", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "video_url", "id": "YouTubeCommentsComponent-y3wJZ", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-kaWcL{œdataTypeœ:œChatInputœ,œidœ:œChatInput-kaWcLœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-YouTubeCommentsComponent-y3wJZ{œfieldNameœ:œvideo_urlœ,œidœ:œYouTubeCommentsComponent-y3wJZœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-kaWcL", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-kaWcLœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "YouTubeCommentsComponent-y3wJZ", "targetHandle": "{œfieldNameœ: œvideo_urlœ, œidœ: œYouTubeCommentsComponent-y3wJZœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-kaWcL", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "url", "id": "Prompt-yqoLt", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-kaWcL{œdataTypeœ:œChatInputœ,œidœ:œChatInput-kaWcLœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-Prompt-yqoLt{œfieldNameœ:œurlœ,œidœ:œPrompt-yqoLtœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-kaWcL", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-kaWcLœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-yqoLt", "targetHandle": "{œfieldNameœ: œurlœ, œidœ: œPrompt-yqoLtœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}], "nodes": [{"data": {"id": "BatchRunComponent-30WdR", "node": {"base_classes": ["DataFrame"], "beta": false, "category": "helpers", "conditional_paths": [], "custom_fields": {}, "description": "Runs an LLM on each row of a DataFrame column. If no column is specified, all columns are used.", "display_name": "Batch Run", "documentation": "", "edited": false, "field_order": ["model", "system_message", "df", "column_name"], "frozen": false, "icon": "List", "key": "BatchRunComponent", "legacy": false, "lf_version": "1.4.3", "metadata": {"code_hash": "86f4b70ee039", "module": "langflow.components.processing.batch_run.BatchRunComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "LLM Results", "group_outputs": false, "method": "run_batch", "name": "batch_results", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.007568328950209746, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from __future__ import annotations\n\nfrom typing import TYPE_CHECKING, Any, cast\n\nimport toml  # type: ignore[import-untyped]\nfrom loguru import logger\n\nfrom langflow.custom.custom_component.component import Component\nfrom langflow.io import BoolInput, DataFrameInput, HandleInput, MessageTextInput, MultilineInput, Output\nfrom langflow.schema.dataframe import DataFrame\n\nif TYPE_CHECKING:\n    from langchain_core.runnables import Runnable\n\n\nclass BatchRunComponent(Component):\n    display_name = \"Batch Run\"\n    description = \"Runs an LLM on each row of a DataFrame column. If no column is specified, all columns are used.\"\n    documentation: str = \"https://docs.langflow.org/components-processing#batch-run\"\n    icon = \"List\"\n\n    inputs = [\n        HandleInput(\n            name=\"model\",\n            display_name=\"Language Model\",\n            info=\"Connect the 'Language Model' output from your LLM component here.\",\n            input_types=[\"LanguageModel\"],\n            required=True,\n        ),\n        MultilineInput(\n            name=\"system_message\",\n            display_name=\"Instructions\",\n            info=\"Multi-line system instruction for all rows in the DataFrame.\",\n            required=False,\n        ),\n        DataFrameInput(\n            name=\"df\",\n            display_name=\"DataFrame\",\n            info=\"The DataFrame whose column (specified by 'column_name') we'll treat as text messages.\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"column_name\",\n            display_name=\"Column Name\",\n            info=(\n                \"The name of the DataFrame column to treat as text messages. \"\n                \"If empty, all columns will be formatted in TOML.\"\n            ),\n            required=False,\n            advanced=False,\n        ),\n        MessageTextInput(\n            name=\"output_column_name\",\n            display_name=\"Output Column Name\",\n            info=\"Name of the column where the model's response will be stored.\",\n            value=\"model_response\",\n            required=False,\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"enable_metadata\",\n            display_name=\"Enable Metadata\",\n            info=\"If True, add metadata to the output DataFrame.\",\n            value=False,\n            required=False,\n            advanced=True,\n        ),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"LLM Results\",\n            name=\"batch_results\",\n            method=\"run_batch\",\n            info=\"A DataFrame with all original columns plus the model's response column.\",\n        ),\n    ]\n\n    def _format_row_as_toml(self, row: dict[str, Any]) -> str:\n        \"\"\"Convert a dictionary (row) into a TOML-formatted string.\"\"\"\n        formatted_dict = {str(col): {\"value\": str(val)} for col, val in row.items()}\n        return toml.dumps(formatted_dict)\n\n    def _create_base_row(\n        self, original_row: dict[str, Any], model_response: str = \"\", batch_index: int = -1\n    ) -> dict[str, Any]:\n        \"\"\"Create a base row with original columns and additional metadata.\"\"\"\n        row = original_row.copy()\n        row[self.output_column_name] = model_response\n        row[\"batch_index\"] = batch_index\n        return row\n\n    def _add_metadata(\n        self, row: dict[str, Any], *, success: bool = True, system_msg: str = \"\", error: str | None = None\n    ) -> None:\n        \"\"\"Add metadata to a row if enabled.\"\"\"\n        if not self.enable_metadata:\n            return\n\n        if success:\n            row[\"metadata\"] = {\n                \"has_system_message\": bool(system_msg),\n                \"input_length\": len(row.get(\"text_input\", \"\")),\n                \"response_length\": len(row[self.output_column_name]),\n                \"processing_status\": \"success\",\n            }\n        else:\n            row[\"metadata\"] = {\n                \"error\": error,\n                \"processing_status\": \"failed\",\n            }\n\n    async def run_batch(self) -> DataFrame:\n        \"\"\"Process each row in df[column_name] with the language model asynchronously.\n\n        Returns:\n            DataFrame: A new DataFrame containing:\n                - All original columns\n                - The model's response column (customizable name)\n                - 'batch_index' column for processing order\n                - 'metadata' (optional)\n\n        Raises:\n            ValueError: If the specified column is not found in the DataFrame\n            TypeError: If the model is not compatible or input types are wrong\n        \"\"\"\n        model: Runnable = self.model\n        system_msg = self.system_message or \"\"\n        df: DataFrame = self.df\n        col_name = self.column_name or \"\"\n\n        # Validate inputs first\n        if not isinstance(df, DataFrame):\n            msg = f\"Expected DataFrame input, got {type(df)}\"\n            raise TypeError(msg)\n\n        if col_name and col_name not in df.columns:\n            msg = f\"Column '{col_name}' not found in the DataFrame. Available columns: {', '.join(df.columns)}\"\n            raise ValueError(msg)\n\n        try:\n            # Determine text input for each row\n            if col_name:\n                user_texts = df[col_name].astype(str).tolist()\n            else:\n                user_texts = [\n                    self._format_row_as_toml(cast(dict[str, Any], row)) for row in df.to_dict(orient=\"records\")\n                ]\n\n            total_rows = len(user_texts)\n            logger.info(f\"Processing {total_rows} rows with batch run\")\n\n            # Prepare the batch of conversations\n            conversations = [\n                [{\"role\": \"system\", \"content\": system_msg}, {\"role\": \"user\", \"content\": text}]\n                if system_msg\n                else [{\"role\": \"user\", \"content\": text}]\n                for text in user_texts\n            ]\n\n            # Configure the model with project info and callbacks\n            model = model.with_config(\n                {\n                    \"run_name\": self.display_name,\n                    \"project_name\": self.get_project_name(),\n                    \"callbacks\": self.get_langchain_callbacks(),\n                }\n            )\n            # Process batches and track progress\n            responses_with_idx = list(\n                zip(\n                    range(len(conversations)),\n                    await model.abatch(list(conversations)),\n                    strict=True,\n                )\n            )\n\n            # Sort by index to maintain order\n            responses_with_idx.sort(key=lambda x: x[0])\n\n            # Build the final data with enhanced metadata\n            rows: list[dict[str, Any]] = []\n            for idx, (original_row, response) in enumerate(\n                zip(df.to_dict(orient=\"records\"), responses_with_idx, strict=False)\n            ):\n                response_text = response[1].content if hasattr(response[1], \"content\") else str(response[1])\n                row = self._create_base_row(\n                    cast(dict[str, Any], original_row), model_response=response_text, batch_index=idx\n                )\n                self._add_metadata(row, success=True, system_msg=system_msg)\n                rows.append(row)\n\n                # Log progress\n                if (idx + 1) % max(1, total_rows // 10) == 0:\n                    logger.info(f\"Processed {idx + 1}/{total_rows} rows\")\n\n            logger.info(\"Batch processing completed successfully\")\n            return DataFrame(rows)\n\n        except (KeyError, AttributeError) as e:\n            # Handle data structure and attribute access errors\n            logger.error(f\"Data processing error: {e!s}\")\n            error_row = self._create_base_row({col: \"\" for col in df.columns}, model_response=\"\", batch_index=-1)\n            self._add_metadata(error_row, success=False, error=str(e))\n            return DataFrame([error_row])\n"}, "column_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Column Name", "dynamic": false, "info": "The name of the DataFrame column to treat as text messages. If empty, all columns will be formatted in TOML.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "column_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text"}, "df": {"_input_type": "DataFrameInput", "advanced": false, "display_name": "DataFrame", "dynamic": false, "info": "The DataFrame whose column (specified by 'column_name') we'll treat as text messages.", "input_types": ["DataFrame"], "list": false, "list_add_label": "Add More", "name": "df", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "enable_metadata": {"_input_type": "BoolInput", "advanced": true, "display_name": "Enable <PERSON><PERSON><PERSON>", "dynamic": false, "info": "If True, add metadata to the output DataFrame.", "list": false, "list_add_label": "Add More", "name": "enable_metadata", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "model": {"_input_type": "HandleInput", "advanced": false, "display_name": "Language Model", "dynamic": false, "info": "Connect the 'Language Model' output from your LLM component here.", "input_types": ["LanguageModel"], "list": false, "list_add_label": "Add More", "name": "model", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "output_column_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Output Column Name", "dynamic": false, "info": "Name of the column where the model's response will be stored.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "output_column_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "model_response"}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "Instructions", "dynamic": false, "info": "Multi-line system instruction for all rows in the DataFrame.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "You are a sentiment analysis AI specialized in analyzing YouTube comments. Your task is to determine the emotional tone of each comment.\n\nAnalyze:\n- Word choice and tone\n- Emotional language \n- Context clues (emojis, punctuation, caps)\n\nClassify sentiment as:\n- Positive\n- Negative  \n- Neutral\n- Mixed\n- Sarcastic\n\nFormat:\n<sentiment_analysis>\n<concise_reasoning>\n[Brief 1-2 sentence analysis focusing on key emotional indicators]\n</concise_reasoning>\n<sentiment_type>\n[Sentiment category]\n</sentiment_type>\n</sentiment_analysis>\n\nExample:\nComment: \"Wow, this video is absolutely amazing! The creator did an incredible job explaining complex topics so clearly. 👏👏👏\"\n<sentiment_analysis>\n<concise_reasoning>\nUses enthusiastic words (\"amazing\", \"incredible\") with applause emojis, showing strong appreciation for the content's clarity.\n</concise_reasoning>\n<sentiment_type>\nPositive\n</sentiment_type>\n</sentiment_analysis>"}}, "tool_mode": false}, "selected_output": "batch_results", "showNode": true, "type": "BatchRunComponent"}, "dragging": false, "id": "BatchRunComponent-30WdR", "measured": {"height": 391, "width": 320}, "position": {"x": 635.0665302273813, "y": 5950.62139498088}, "selected": false, "type": "genericNode"}, {"data": {"id": "YouTubeCommentsComponent-y3wJZ", "node": {"base_classes": ["DataFrame"], "beta": false, "category": "youtube", "conditional_paths": [], "custom_fields": {}, "description": "Retrieves and analyzes comments from YouTube videos.", "display_name": "YouTube Comments", "documentation": "", "edited": false, "field_order": ["video_url", "api_key", "max_results", "sort_by", "include_replies", "include_metrics"], "frozen": false, "icon": "YouTube", "key": "YouTubeCommentsComponent", "legacy": false, "lf_version": "1.4.3", "metadata": {"code_hash": "aeda2975f4aa", "module": "langflow.components.youtube.comments.YouTubeCommentsComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Comments", "group_outputs": false, "method": "get_video_comments", "name": "comments", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.1676812955549333, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "YouTube API Key", "dynamic": false, "info": "Your YouTube Data API key.", "input_types": [], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "YOUTUBE_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from contextlib import contextmanager\n\nimport pandas as pd\nfrom googleapiclient.discovery import build\nfrom googleapiclient.errors import HttpError\n\nfrom langflow.custom.custom_component.component import Component\nfrom langflow.inputs.inputs import BoolInput, DropdownInput, IntInput, MessageTextInput, SecretStrInput\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.template.field.base import Output\n\n\nclass YouTubeCommentsComponent(Component):\n    \"\"\"A component that retrieves comments from YouTube videos.\"\"\"\n\n    display_name: str = \"YouTube Comments\"\n    description: str = \"Retrieves and analyzes comments from YouTube videos.\"\n    icon: str = \"YouTube\"\n\n    # Constants\n    COMMENTS_DISABLED_STATUS = 403\n    NOT_FOUND_STATUS = 404\n    API_MAX_RESULTS = 100\n\n    inputs = [\n        MessageTextInput(\n            name=\"video_url\",\n            display_name=\"Video URL\",\n            info=\"The URL of the YouTube video to get comments from.\",\n            tool_mode=True,\n            required=True,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"YouTube API Key\",\n            info=\"Your YouTube Data API key.\",\n            required=True,\n        ),\n        IntInput(\n            name=\"max_results\",\n            display_name=\"Max Results\",\n            value=20,\n            info=\"The maximum number of comments to return.\",\n        ),\n        DropdownInput(\n            name=\"sort_by\",\n            display_name=\"Sort By\",\n            options=[\"time\", \"relevance\"],\n            value=\"relevance\",\n            info=\"Sort comments by time or relevance.\",\n        ),\n        BoolInput(\n            name=\"include_replies\",\n            display_name=\"Include Replies\",\n            value=False,\n            info=\"Whether to include replies to comments.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"include_metrics\",\n            display_name=\"Include Metrics\",\n            value=True,\n            info=\"Include metrics like like count and reply count.\",\n            advanced=True,\n        ),\n    ]\n\n    outputs = [\n        Output(name=\"comments\", display_name=\"Comments\", method=\"get_video_comments\"),\n    ]\n\n    def _extract_video_id(self, video_url: str) -> str:\n        \"\"\"Extracts the video ID from a YouTube URL.\"\"\"\n        import re\n\n        patterns = [\n            r\"(?:youtube\\.com\\/watch\\?v=|youtu.be\\/|youtube.com\\/embed\\/)([^&\\n?#]+)\",\n            r\"youtube.com\\/shorts\\/([^&\\n?#]+)\",\n        ]\n\n        for pattern in patterns:\n            match = re.search(pattern, video_url)\n            if match:\n                return match.group(1)\n\n        return video_url.strip()\n\n    def _process_reply(self, reply: dict, parent_id: str, *, include_metrics: bool = True) -> dict:\n        \"\"\"Process a single reply comment.\"\"\"\n        reply_snippet = reply[\"snippet\"]\n        reply_data = {\n            \"comment_id\": reply[\"id\"],\n            \"parent_comment_id\": parent_id,\n            \"author\": reply_snippet[\"authorDisplayName\"],\n            \"text\": reply_snippet[\"textDisplay\"],\n            \"published_at\": reply_snippet[\"publishedAt\"],\n            \"is_reply\": True,\n        }\n        if include_metrics:\n            reply_data[\"like_count\"] = reply_snippet[\"likeCount\"]\n            reply_data[\"reply_count\"] = 0  # Replies can't have replies\n\n        return reply_data\n\n    def _process_comment(\n        self, item: dict, *, include_metrics: bool = True, include_replies: bool = False\n    ) -> list[dict]:\n        \"\"\"Process a single comment thread.\"\"\"\n        comment = item[\"snippet\"][\"topLevelComment\"][\"snippet\"]\n        comment_id = item[\"snippet\"][\"topLevelComment\"][\"id\"]\n\n        # Basic comment data\n        processed_comments = [\n            {\n                \"comment_id\": comment_id,\n                \"parent_comment_id\": \"\",  # Empty for top-level comments\n                \"author\": comment[\"authorDisplayName\"],\n                \"author_channel_url\": comment.get(\"authorChannelUrl\", \"\"),\n                \"text\": comment[\"textDisplay\"],\n                \"published_at\": comment[\"publishedAt\"],\n                \"updated_at\": comment[\"updatedAt\"],\n                \"is_reply\": False,\n            }\n        ]\n\n        # Add metrics if requested\n        if include_metrics:\n            processed_comments[0].update(\n                {\n                    \"like_count\": comment[\"likeCount\"],\n                    \"reply_count\": item[\"snippet\"][\"totalReplyCount\"],\n                }\n            )\n\n        # Add replies if requested\n        if include_replies and item[\"snippet\"][\"totalReplyCount\"] > 0 and \"replies\" in item:\n            for reply in item[\"replies\"][\"comments\"]:\n                reply_data = self._process_reply(reply, parent_id=comment_id, include_metrics=include_metrics)\n                processed_comments.append(reply_data)\n\n        return processed_comments\n\n    @contextmanager\n    def youtube_client(self):\n        \"\"\"Context manager for YouTube API client.\"\"\"\n        client = build(\"youtube\", \"v3\", developerKey=self.api_key)\n        try:\n            yield client\n        finally:\n            client.close()\n\n    def get_video_comments(self) -> DataFrame:\n        \"\"\"Retrieves comments from a YouTube video and returns as DataFrame.\"\"\"\n        try:\n            # Extract video ID from URL\n            video_id = self._extract_video_id(self.video_url)\n\n            # Use context manager for YouTube API client\n            with self.youtube_client() as youtube:\n                comments_data = []\n                results_count = 0\n                request = youtube.commentThreads().list(\n                    part=\"snippet,replies\",\n                    videoId=video_id,\n                    maxResults=min(self.API_MAX_RESULTS, self.max_results),\n                    order=self.sort_by,\n                    textFormat=\"plainText\",\n                )\n\n                while request and results_count < self.max_results:\n                    response = request.execute()\n\n                    for item in response.get(\"items\", []):\n                        if results_count >= self.max_results:\n                            break\n\n                        comments = self._process_comment(\n                            item, include_metrics=self.include_metrics, include_replies=self.include_replies\n                        )\n                        comments_data.extend(comments)\n                        results_count += 1\n\n                    # Get the next page if available and needed\n                    if \"nextPageToken\" in response and results_count < self.max_results:\n                        request = youtube.commentThreads().list(\n                            part=\"snippet,replies\",\n                            videoId=video_id,\n                            maxResults=min(self.API_MAX_RESULTS, self.max_results - results_count),\n                            order=self.sort_by,\n                            textFormat=\"plainText\",\n                            pageToken=response[\"nextPageToken\"],\n                        )\n                    else:\n                        request = None\n\n                # Convert to DataFrame\n                comments_df = pd.DataFrame(comments_data)\n\n                # Add video metadata\n                comments_df[\"video_id\"] = video_id\n                comments_df[\"video_url\"] = self.video_url\n\n                # Sort columns for better organization\n                column_order = [\n                    \"video_id\",\n                    \"video_url\",\n                    \"comment_id\",\n                    \"parent_comment_id\",\n                    \"is_reply\",\n                    \"author\",\n                    \"author_channel_url\",\n                    \"text\",\n                    \"published_at\",\n                    \"updated_at\",\n                ]\n\n                if self.include_metrics:\n                    column_order.extend([\"like_count\", \"reply_count\"])\n\n                comments_df = comments_df[column_order]\n\n                return DataFrame(comments_df)\n\n        except HttpError as e:\n            error_message = f\"YouTube API error: {e!s}\"\n            if e.resp.status == self.COMMENTS_DISABLED_STATUS:\n                error_message = \"Comments are disabled for this video or API quota exceeded.\"\n            elif e.resp.status == self.NOT_FOUND_STATUS:\n                error_message = \"Video not found.\"\n\n            return DataFrame(pd.DataFrame({\"error\": [error_message]}))\n"}, "include_metrics": {"_input_type": "BoolInput", "advanced": true, "display_name": "Include Metrics", "dynamic": false, "info": "Include metrics like like count and reply count.", "list": false, "list_add_label": "Add More", "name": "include_metrics", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "include_replies": {"_input_type": "BoolInput", "advanced": true, "display_name": "Include Replies", "dynamic": false, "info": "Whether to include replies to comments.", "list": false, "list_add_label": "Add More", "name": "include_replies", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_results": {"_input_type": "IntInput", "advanced": false, "display_name": "Max Results", "dynamic": false, "info": "The maximum number of comments to return.", "list": false, "list_add_label": "Add More", "name": "max_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 10}, "sort_by": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Sort By", "dynamic": false, "info": "Sort comments by time or relevance.", "name": "sort_by", "options": ["time", "relevance"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "relevance"}, "video_url": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Video URL", "dynamic": false, "info": "The URL of the YouTube video to get comments from.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "video_url", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "selected_output": "comments", "showNode": true, "type": "YouTubeCommentsComponent"}, "dragging": false, "id": "YouTubeCommentsComponent-y3wJZ", "measured": {"height": 467, "width": 320}, "position": {"x": 191.60600144515274, "y": 6042.239455161904}, "selected": false, "type": "genericNode"}, {"data": {"id": "Agent-<PERSON><PERSON><PERSON>", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Define the agent's instructions, then enter a task to complete using tools.", "display_name": "YT-Insight", "documentation": "", "edited": false, "field_order": ["agent_llm", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed", "max_retries", "timeout", "system_prompt", "n_messages", "tools", "input_value", "handle_parsing_errors", "verbose", "max_iterations", "agent_description", "add_current_date_tool"], "frozen": false, "icon": "bot", "last_updated": "2025-07-07T14:52:14.999Z", "legacy": false, "lf_version": "1.4.3", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Response", "group_outputs": false, "hidden": null, "method": "message_response", "name": "response", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "add_current_date_tool": {"_input_type": "BoolInput", "advanced": true, "display_name": "Current Date", "dynamic": false, "info": "If true, will add a tool to the agent that returns the current date.", "list": false, "list_add_label": "Add More", "name": "add_current_date_tool", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "agent_description": {"_input_type": "MultilineInput", "advanced": true, "copy_field": false, "display_name": "Agent Description [Deprecated]", "dynamic": false, "info": "The description of the agent. This is only used when in Tool Mode. Defaults to 'A helpful assistant with access to the following tools:' and tools are added dynamically. This feature is deprecated and will be removed in future versions.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "agent_description", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "A helpful assistant with access to the following tools:"}, "agent_llm": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Provider", "dynamic": false, "info": "The provider of the language model that the agent will use to generate responses.", "input_types": [], "name": "agent_llm", "options": ["Anthropic", "Google Generative AI", "Groq", "OpenAI", "Custom"], "options_metadata": [{"icon": "Anthropic"}, {"icon": "GoogleGenerativeAI"}, {"icon": "Groq"}, {"icon": "OpenAI"}, {"icon": "brain"}], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "OpenAI"}, "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "The OpenAI API Key to use for the OpenAI model.", "input_types": [], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import json\nimport re\n\nfrom langchain_core.tools import StructuredTool\n\nfrom langflow.base.agents.agent import LCToolsAgentComponent\nfrom langflow.base.agents.events import ExceptionWithMessageError\nfrom langflow.base.models.model_input_constants import (\n    ALL_PROVIDER_FIELDS,\n    MODEL_DYNAMIC_UPDATE_FIELDS,\n    MODEL_PROVIDERS,\n    MODEL_PROVIDERS_DICT,\n    MODELS_METADATA,\n)\nfrom langflow.base.models.model_utils import get_model_name\nfrom langflow.components.helpers.current_date import CurrentDateComponent\nfrom langflow.components.helpers.memory import MemoryComponent\nfrom langflow.components.langchain_utilities.tool_calling import ToolCallingAgentComponent\nfrom langflow.custom.custom_component.component import _get_component_toolkit\nfrom langflow.custom.utils import update_component_build_config\nfrom langflow.field_typing import Tool\nfrom langflow.io import BoolInput, DropdownInput, IntInput, MultilineInput, Output\nfrom langflow.logging import logger\nfrom langflow.schema.data import Data\nfrom langflow.schema.dotdict import dotdict\nfrom langflow.schema.message import Message\n\n\ndef set_advanced_true(component_input):\n    component_input.advanced = True\n    return component_input\n\n\nMODEL_PROVIDERS_LIST = [\"Anthropic\", \"Google Generative AI\", \"Groq\", \"OpenAI\"]\n\n\nclass AgentComponent(ToolCallingAgentComponent):\n    display_name: str = \"Agent\"\n    description: str = \"Define the agent's instructions, then enter a task to complete using tools.\"\n    documentation: str = \"https://docs.langflow.org/agents\"\n    icon = \"bot\"\n    beta = False\n    name = \"Agent\"\n\n    memory_inputs = [set_advanced_true(component_input) for component_input in MemoryComponent().inputs]\n\n    # Filter out json_mode from OpenAI inputs since we handle structured output differently\n    openai_inputs_filtered = [\n        input_field\n        for input_field in MODEL_PROVIDERS_DICT[\"OpenAI\"][\"inputs\"]\n        if not (hasattr(input_field, \"name\") and input_field.name == \"json_mode\")\n    ]\n\n    inputs = [\n        DropdownInput(\n            name=\"agent_llm\",\n            display_name=\"Model Provider\",\n            info=\"The provider of the language model that the agent will use to generate responses.\",\n            options=[*MODEL_PROVIDERS_LIST, \"Custom\"],\n            value=\"OpenAI\",\n            real_time_refresh=True,\n            input_types=[],\n            options_metadata=[MODELS_METADATA[key] for key in MODEL_PROVIDERS_LIST] + [{\"icon\": \"brain\"}],\n        ),\n        *openai_inputs_filtered,\n        MultilineInput(\n            name=\"system_prompt\",\n            display_name=\"Agent Instructions\",\n            info=\"System Prompt: Initial instructions and context provided to guide the agent's behavior.\",\n            value=\"You are a helpful assistant that can use tools to answer questions and perform tasks.\",\n            advanced=False,\n        ),\n        IntInput(\n            name=\"n_messages\",\n            display_name=\"Number of Chat History Messages\",\n            value=100,\n            info=\"Number of chat history messages to retrieve.\",\n            advanced=True,\n            show=True,\n        ),\n        *LCToolsAgentComponent._base_inputs,\n        # removed memory inputs from agent component\n        # *memory_inputs,\n        BoolInput(\n            name=\"add_current_date_tool\",\n            display_name=\"Current Date\",\n            advanced=True,\n            info=\"If true, will add a tool to the agent that returns the current date.\",\n            value=True,\n        ),\n    ]\n    outputs = [\n        Output(name=\"response\", display_name=\"Response\", method=\"message_response\"),\n        Output(name=\"structured_response\", display_name=\"Structured Response\", method=\"json_response\", tool_mode=False),\n    ]\n\n    async def message_response(self) -> Message:\n        try:\n            # Get LLM model and validate\n            llm_model, display_name = self.get_llm()\n            if llm_model is None:\n                msg = \"No language model selected. Please choose a model to proceed.\"\n                raise ValueError(msg)\n            self.model_name = get_model_name(llm_model, display_name=display_name)\n\n            # Get memory data\n            self.chat_history = await self.get_memory_data()\n            if isinstance(self.chat_history, Message):\n                self.chat_history = [self.chat_history]\n\n            # Add current date tool if enabled\n            if self.add_current_date_tool:\n                if not isinstance(self.tools, list):  # type: ignore[has-type]\n                    self.tools = []\n                current_date_tool = (await CurrentDateComponent(**self.get_base_args()).to_toolkit()).pop(0)\n                if not isinstance(current_date_tool, StructuredTool):\n                    msg = \"CurrentDateComponent must be converted to a StructuredTool\"\n                    raise TypeError(msg)\n                self.tools.append(current_date_tool)\n            # note the tools are not required to run the agent, hence the validation removed.\n\n            # Set up and run agent\n            self.set(\n                llm=llm_model,\n                tools=self.tools or [],\n                chat_history=self.chat_history,\n                input_value=self.input_value,\n                system_prompt=self.system_prompt,\n            )\n            agent = self.create_agent_runnable()\n            result = await self.run_agent(agent)\n\n            # Store result for potential JSON output\n            self._agent_result = result\n            # return result\n\n        except (ValueError, TypeError, KeyError) as e:\n            logger.error(f\"{type(e).__name__}: {e!s}\")\n            raise\n        except ExceptionWithMessageError as e:\n            logger.error(f\"ExceptionWithMessageError occurred: {e}\")\n            raise\n        except Exception as e:\n            logger.error(f\"Unexpected error: {e!s}\")\n            raise\n        else:\n            return result\n\n    async def json_response(self) -> Data:\n        \"\"\"Convert agent response to structured JSON Data output.\"\"\"\n        # Run the regular message response first to get the result\n        if not hasattr(self, \"_agent_result\"):\n            await self.message_response()\n\n        result = self._agent_result\n\n        # Extract content from result\n        if hasattr(result, \"content\"):\n            content = result.content\n        elif hasattr(result, \"text\"):\n            content = result.text\n        else:\n            content = str(result)\n\n        # Try to parse as JSON\n        try:\n            json_data = json.loads(content)\n            return Data(data=json_data)\n        except json.JSONDecodeError:\n            # If it's not valid JSON, try to extract JSON from the content\n            json_match = re.search(r\"\\{.*\\}\", content, re.DOTALL)\n            if json_match:\n                try:\n                    json_data = json.loads(json_match.group())\n                    return Data(data=json_data)\n                except json.JSONDecodeError:\n                    pass\n\n            # If we can't extract JSON, return the raw content as data\n            return Data(data={\"content\": content, \"error\": \"Could not parse as JSON\"})\n\n    async def get_memory_data(self):\n        # TODO: This is a temporary fix to avoid message duplication. We should develop a function for this.\n        messages = (\n            await MemoryComponent(**self.get_base_args())\n            .set(session_id=self.graph.session_id, order=\"Ascending\", n_messages=self.n_messages)\n            .retrieve_messages()\n        )\n        return [\n            message for message in messages if getattr(message, \"id\", None) != getattr(self.input_value, \"id\", None)\n        ]\n\n    def get_llm(self):\n        if not isinstance(self.agent_llm, str):\n            return self.agent_llm, None\n\n        try:\n            provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n            if not provider_info:\n                msg = f\"Invalid model provider: {self.agent_llm}\"\n                raise ValueError(msg)\n\n            component_class = provider_info.get(\"component_class\")\n            display_name = component_class.display_name\n            inputs = provider_info.get(\"inputs\")\n            prefix = provider_info.get(\"prefix\", \"\")\n\n            return self._build_llm_model(component_class, inputs, prefix), display_name\n\n        except Exception as e:\n            logger.error(f\"Error building {self.agent_llm} language model: {e!s}\")\n            msg = f\"Failed to initialize language model: {e!s}\"\n            raise ValueError(msg) from e\n\n    def _build_llm_model(self, component, inputs, prefix=\"\"):\n        model_kwargs = {}\n        for input_ in inputs:\n            if hasattr(self, f\"{prefix}{input_.name}\"):\n                model_kwargs[input_.name] = getattr(self, f\"{prefix}{input_.name}\")\n        return component.set(**model_kwargs).build_model()\n\n    def set_component_params(self, component):\n        provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n        if provider_info:\n            inputs = provider_info.get(\"inputs\")\n            prefix = provider_info.get(\"prefix\")\n            # Filter out json_mode and only use attributes that exist on this component\n            model_kwargs = {}\n            for input_ in inputs:\n                if hasattr(self, f\"{prefix}{input_.name}\"):\n                    model_kwargs[input_.name] = getattr(self, f\"{prefix}{input_.name}\")\n\n            return component.set(**model_kwargs)\n        return component\n\n    def delete_fields(self, build_config: dotdict, fields: dict | list[str]) -> None:\n        \"\"\"Delete specified fields from build_config.\"\"\"\n        for field in fields:\n            build_config.pop(field, None)\n\n    def update_input_types(self, build_config: dotdict) -> dotdict:\n        \"\"\"Update input types for all fields in build_config.\"\"\"\n        for key, value in build_config.items():\n            if isinstance(value, dict):\n                if value.get(\"input_types\") is None:\n                    build_config[key][\"input_types\"] = []\n            elif hasattr(value, \"input_types\") and value.input_types is None:\n                value.input_types = []\n        return build_config\n\n    async def update_build_config(\n        self, build_config: dotdict, field_value: str, field_name: str | None = None\n    ) -> dotdict:\n        # Iterate over all providers in the MODEL_PROVIDERS_DICT\n        # Existing logic for updating build_config\n        if field_name in (\"agent_llm\",):\n            build_config[\"agent_llm\"][\"value\"] = field_value\n            provider_info = MODEL_PROVIDERS_DICT.get(field_value)\n            if provider_info:\n                component_class = provider_info.get(\"component_class\")\n                if component_class and hasattr(component_class, \"update_build_config\"):\n                    # Call the component class's update_build_config method\n                    build_config = await update_component_build_config(\n                        component_class, build_config, field_value, \"model_name\"\n                    )\n\n            provider_configs: dict[str, tuple[dict, list[dict]]] = {\n                provider: (\n                    MODEL_PROVIDERS_DICT[provider][\"fields\"],\n                    [\n                        MODEL_PROVIDERS_DICT[other_provider][\"fields\"]\n                        for other_provider in MODEL_PROVIDERS_DICT\n                        if other_provider != provider\n                    ],\n                )\n                for provider in MODEL_PROVIDERS_DICT\n            }\n            if field_value in provider_configs:\n                fields_to_add, fields_to_delete = provider_configs[field_value]\n\n                # Delete fields from other providers\n                for fields in fields_to_delete:\n                    self.delete_fields(build_config, fields)\n\n                # Add provider-specific fields\n                if field_value == \"OpenAI\" and not any(field in build_config for field in fields_to_add):\n                    build_config.update(fields_to_add)\n                else:\n                    build_config.update(fields_to_add)\n                # Reset input types for agent_llm\n                build_config[\"agent_llm\"][\"input_types\"] = []\n            elif field_value == \"Custom\":\n                # Delete all provider fields\n                self.delete_fields(build_config, ALL_PROVIDER_FIELDS)\n                # Update with custom component\n                custom_component = DropdownInput(\n                    name=\"agent_llm\",\n                    display_name=\"Language Model\",\n                    options=[*sorted(MODEL_PROVIDERS), \"Custom\"],\n                    value=\"Custom\",\n                    real_time_refresh=True,\n                    input_types=[\"LanguageModel\"],\n                    options_metadata=[MODELS_METADATA[key] for key in sorted(MODELS_METADATA.keys())]\n                    + [{\"icon\": \"brain\"}],\n                )\n                build_config.update({\"agent_llm\": custom_component.to_dict()})\n            # Update input types for all fields\n            build_config = self.update_input_types(build_config)\n\n            # Validate required keys\n            default_keys = [\n                \"code\",\n                \"_type\",\n                \"agent_llm\",\n                \"tools\",\n                \"input_value\",\n                \"add_current_date_tool\",\n                \"system_prompt\",\n                \"agent_description\",\n                \"max_iterations\",\n                \"handle_parsing_errors\",\n                \"verbose\",\n            ]\n            missing_keys = [key for key in default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n        if (\n            isinstance(self.agent_llm, str)\n            and self.agent_llm in MODEL_PROVIDERS_DICT\n            and field_name in MODEL_DYNAMIC_UPDATE_FIELDS\n        ):\n            provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n            if provider_info:\n                component_class = provider_info.get(\"component_class\")\n                component_class = self.set_component_params(component_class)\n                prefix = provider_info.get(\"prefix\")\n                if component_class and hasattr(component_class, \"update_build_config\"):\n                    # Call each component class's update_build_config method\n                    # remove the prefix from the field_name\n                    if isinstance(field_name, str) and isinstance(prefix, str):\n                        field_name = field_name.replace(prefix, \"\")\n                    build_config = await update_component_build_config(\n                        component_class, build_config, field_value, \"model_name\"\n                    )\n        return dotdict({k: v.to_dict() if hasattr(v, \"to_dict\") else v for k, v in build_config.items()})\n\n    async def _get_tools(self) -> list[Tool]:\n        component_toolkit = _get_component_toolkit()\n        tools_names = self._build_tools_names()\n        agent_description = self.get_tool_description()\n        # TODO: Agent Description Depreciated Feature to be removed\n        description = f\"{agent_description}{tools_names}\"\n        tools = component_toolkit(component=self).get_tools(\n            tool_name=\"Call_Agent\", tool_description=description, callbacks=self.get_langchain_callbacks()\n        )\n        if hasattr(self, \"tools_metadata\"):\n            tools = component_toolkit(component=self, metadata=self.tools_metadata).update_tools_metadata(tools=tools)\n        return tools\n"}, "handle_parsing_errors": {"_input_type": "BoolInput", "advanced": true, "display_name": "<PERSON><PERSON> Parse Errors", "dynamic": false, "info": "Should the Agent fix errors when reading user input for better processing?", "list": false, "list_add_label": "Add More", "name": "handle_parsing_errors", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "input_value": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Input", "dynamic": false, "info": "The input provided by the user for the agent to process.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON Mode", "dynamic": false, "info": "If True, it will output JSON regardless of passing a schema.", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_iterations": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Iterations", "dynamic": false, "info": "The maximum number of attempts the agent can make to complete its task before it stops.", "list": false, "list_add_label": "Add More", "name": "max_iterations", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 15}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Retries", "dynamic": false, "info": "The maximum number of retries to make when generating.", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON>", "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "Model Kwargs", "dynamic": false, "info": "Additional keyword arguments to pass to the model.", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "To see the model names, first choose a provider. Then, enter your API key and click the refresh button next to the model name.", "load_from_db": false, "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo", "o1"], "options_metadata": [], "placeholder": "", "real_time_refresh": false, "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4.1-mini"}, "n_messages": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Chat History Messages", "dynamic": false, "info": "Number of chat history messages to retrieve.", "list": false, "list_add_label": "Add More", "name": "n_messages", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 100}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API Base", "dynamic": false, "info": "The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "Seed", "dynamic": false, "info": "The seed controls the reproducibility of the job.", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "system_prompt": {"_input_type": "MultilineInput", "advanced": false, "copy_field": false, "display_name": "Agent Instructions", "dynamic": false, "info": "System Prompt: Initial instructions and context provided to guide the agent's behavior.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_prompt", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "You are a specialized assistant focused on comprehensive YouTube video analysis. Your main responsibilities are:\n\n1. Extract video transcripts using YouTubeTranscripts-get_message_output tool\n2. Process sentiment analysis from YouTube comments provided in XML format\n3. Create comprehensive analysis by combining:\n   - Video content (from transcript)\n   - Audience reception (from comment sentiment analysis)\n\nYour analysis should:\n- Identify main themes and topics from the video transcript\n- Evaluate audience sentiment patterns from provided comments\n- Highlight any disconnect between video content and audience reception\n- Provide actionable insights based on both content and reception\n\nInput received:\n- Video transcript (obtained through tool)\n- Sentiment analysis of comments in XML format containing:\n  <sentiment_analysis>\n    <concise_reasoning>: Detailed analysis of comment\n    <sentiment_type>: Positive/Neutral/Negative\n  </sentiment_analysis>\n\nOutput format:\n1. Content Summary: Key points from transcript\n2. Audience Reception: Pattern analysis from sentiment data\n3. Synthesis: Combined analysis of content and reception\n4. Recommendations: Based on analysis"}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "Temperature", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "Timeout", "dynamic": false, "info": "The timeout for requests to OpenAI completion API.", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}, "tools": {"_input_type": "HandleInput", "advanced": false, "display_name": "Tools", "dynamic": false, "info": "These are the tools that the agent can use to help with tasks.", "input_types": ["Tool"], "list": true, "list_add_label": "Add More", "name": "tools", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "verbose": {"_input_type": "BoolInput", "advanced": true, "display_name": "Verbose", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "verbose", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}}, "tool_mode": false}, "selected_output": "response", "showNode": true, "type": "Agent"}, "dragging": false, "id": "Agent-<PERSON><PERSON><PERSON>", "measured": {"height": 594, "width": 320}, "position": {"x": 1982.1085644220088, "y": 6262.507022218113}, "selected": false, "type": "genericNode"}, {"data": {"id": "Prompt-yqoLt", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": ["url", "analysis"]}, "description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "documentation": "", "edited": false, "error": null, "field_order": ["template", "tool_placeholder"], "frozen": false, "full_path": null, "icon": "braces", "is_composition": null, "is_input": null, "is_output": null, "legacy": false, "lf_version": "1.4.3", "metadata": {}, "minimized": false, "name": "", "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Prompt", "group_outputs": false, "method": "build_prompt", "name": "prompt", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "analysis": {"advanced": false, "display_name": "analysis", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "analysis", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom.custom_component.component import Component\nfrom langflow.inputs.inputs import <PERSON>fa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"Prompt\"\n    description: str = \"Create a prompt template with dynamic variables.\"\n    icon = \"braces\"\n    trace_type = \"prompt\"\n    name = \"Prompt\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"Template\"),\n        MessageTextInput(\n            name=\"tool_placeholder\",\n            display_name=\"Tool Placeholder\",\n            tool_mode=True,\n            advanced=True,\n            info=\"A placeholder input for tool mode.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Prompt\", name=\"prompt\", method=\"build_prompt\"),\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "template": {"_input_type": "PromptInput", "advanced": false, "display_name": "Template", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "prompt", "value": "\nVideo URL:\n{url}\n\n\n Comment Analysis:\n{analysis}"}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Tool Placeholder", "dynamic": false, "info": "A placeholder input for tool mode.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "url": {"advanced": false, "display_name": "url", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "url", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}}, "tool_mode": false}, "selected_output": "prompt", "showNode": true, "type": "Prompt"}, "dragging": false, "id": "Prompt-yqoLt", "measured": {"height": 449, "width": 320}, "position": {"x": 1575.3649919098807, "y": 6461.250996552967}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatOutput-vlskP", "node": {"base_classes": ["Message"], "beta": false, "category": "outputs", "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatOutput", "legacy": false, "lf_version": "1.4.3", "metadata": {"code_hash": "6f74e04e39d5", "module": "langflow.components.input_output.chat_output.ChatOutput"}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Output Message", "group_outputs": false, "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.007568328950209746, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "Basic Clean Data", "dynamic": false, "info": "Whether to clean the data", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nimport orjson\nfrom fastapi.encoders import jsonable_encoder\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.helpers.data import safe_convert\nfrom langflow.inputs.inputs import BoolInput, DropdownInput, HandleInput, MessageTextInput\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.template.field.base import Output\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    documentation: str = \"https://docs.langflow.org/components-io#chat-output\"\n    icon = \"MessagesSquare\"\n    name = \"ChatOutput\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",\n            display_name=\"Inputs\",\n            info=\"Message to be passed as output.\",\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",\n            display_name=\"Basic Clean Data\",\n            value=True,\n            info=\"Whether to clean the data\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"Output Message\",\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _serialize_data(self, data: Data) -> str:\n        \"\"\"Serialize Data object to JSON string.\"\"\"\n        # Convert data.data to JSON-serializable format\n        serializable_data = jsonable_encoder(data.data)\n        # Serialize with orjson, enabling pretty printing with indentation\n        json_bytes = orjson.dumps(serializable_data, option=orjson.OPT_INDENT_2)\n        # Convert bytes to string and wrap in Markdown code blocks\n        return \"```json\\n\" + json_bytes.decode(\"utf-8\") + \"\\n```\"\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([safe_convert(item, clean_data=self.clean_data) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Data Template", "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "Inputs", "dynamic": false, "info": "Message to be passed as output.", "input_types": ["Data", "DataFrame", "Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "ChatOutput"}, "dragging": false, "id": "ChatOutput-vlskP", "measured": {"height": 204, "width": 320}, "position": {"x": 2365.8487393880428, "y": 6653.021671139973}, "selected": false, "type": "genericNode"}, {"data": {"id": "YouTubeTranscripts-TlFcG", "node": {"base_classes": ["Data", "DataFrame", "Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Extracts spoken content from YouTube videos with multiple output options.", "display_name": "YouTube Transcripts", "documentation": "", "edited": false, "field_order": ["url", "chunk_size_seconds", "translation"], "frozen": false, "icon": "YouTube", "last_updated": "2025-07-07T14:52:15.000Z", "legacy": false, "lf_version": "1.4.3", "metadata": {"code_hash": "c9f0262ff0b6", "module": "langflow.components.youtube.youtube_transcripts.YouTubeTranscriptsComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Toolset", "group_outputs": false, "hidden": null, "method": "to_toolkit", "name": "component_as_tool", "options": null, "required_inputs": null, "selected": "Tool", "tool_mode": true, "types": ["Tool"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "chunk_size_seconds": {"_input_type": "IntInput", "advanced": false, "display_name": "<PERSON><PERSON> (seconds)", "dynamic": false, "info": "The size of each transcript chunk in seconds.", "list": false, "list_add_label": "Add More", "name": "chunk_size_seconds", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 60}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import pandas as pd\nimport youtube_transcript_api\nfrom langchain_community.document_loaders import YoutubeLoader\nfrom langchain_community.document_loaders.youtube import TranscriptFormat\n\nfrom langflow.custom.custom_component.component import Component\nfrom langflow.inputs.inputs import DropdownInput, IntInput, MultilineInput\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\nfrom langflow.template.field.base import Output\n\n\nclass YouTubeTranscriptsComponent(Component):\n    \"\"\"A component that extracts spoken content from YouTube videos as transcripts.\"\"\"\n\n    display_name: str = \"YouTube Transcripts\"\n    description: str = \"Extracts spoken content from YouTube videos with multiple output options.\"\n    icon: str = \"YouTube\"\n    name = \"YouTubeTranscripts\"\n\n    inputs = [\n        MultilineInput(\n            name=\"url\",\n            display_name=\"Video URL\",\n            info=\"Enter the YouTube video URL to get transcripts from.\",\n            tool_mode=True,\n            required=True,\n        ),\n        IntInput(\n            name=\"chunk_size_seconds\",\n            display_name=\"Chunk Size (seconds)\",\n            value=60,\n            info=\"The size of each transcript chunk in seconds.\",\n        ),\n        DropdownInput(\n            name=\"translation\",\n            display_name=\"Translation Language\",\n            advanced=True,\n            options=[\"\", \"en\", \"es\", \"fr\", \"de\", \"it\", \"pt\", \"ru\", \"ja\", \"ko\", \"hi\", \"ar\", \"id\"],\n            info=\"Translate the transcripts to the specified language. Leave empty for no translation.\",\n        ),\n    ]\n\n    outputs = [\n        Output(name=\"dataframe\", display_name=\"Chunks\", method=\"get_dataframe_output\"),\n        Output(name=\"message\", display_name=\"Transcript\", method=\"get_message_output\"),\n        Output(name=\"data_output\", display_name=\"Transcript + Source\", method=\"get_data_output\"),\n    ]\n\n    def _load_transcripts(self, *, as_chunks: bool = True):\n        \"\"\"Internal method to load transcripts from YouTube.\"\"\"\n        loader = YoutubeLoader.from_youtube_url(\n            self.url,\n            transcript_format=TranscriptFormat.CHUNKS if as_chunks else TranscriptFormat.TEXT,\n            chunk_size_seconds=self.chunk_size_seconds,\n            translation=self.translation or None,\n        )\n        return loader.load()\n\n    def get_dataframe_output(self) -> DataFrame:\n        \"\"\"Provides transcript output as a DataFrame with timestamp and text columns.\"\"\"\n        try:\n            transcripts = self._load_transcripts(as_chunks=True)\n\n            # Create DataFrame with timestamp and text columns\n            data = []\n            for doc in transcripts:\n                start_seconds = int(doc.metadata[\"start_seconds\"])\n                start_minutes = start_seconds // 60\n                start_seconds %= 60\n                timestamp = f\"{start_minutes:02d}:{start_seconds:02d}\"\n                data.append({\"timestamp\": timestamp, \"text\": doc.page_content})\n\n            return DataFrame(pd.DataFrame(data))\n\n        except (youtube_transcript_api.TranscriptsDisabled, youtube_transcript_api.NoTranscriptFound) as exc:\n            return DataFrame(pd.DataFrame({\"error\": [f\"Failed to get YouTube transcripts: {exc!s}\"]}))\n\n    def get_message_output(self) -> Message:\n        \"\"\"Provides transcript output as continuous text.\"\"\"\n        try:\n            transcripts = self._load_transcripts(as_chunks=False)\n            result = transcripts[0].page_content\n            return Message(text=result)\n\n        except (youtube_transcript_api.TranscriptsDisabled, youtube_transcript_api.NoTranscriptFound) as exc:\n            error_msg = f\"Failed to get YouTube transcripts: {exc!s}\"\n            return Message(text=error_msg)\n\n    def get_data_output(self) -> Data:\n        \"\"\"Creates a structured data object with transcript and metadata.\n\n        Returns a Data object containing transcript text, video URL, and any error\n        messages that occurred during processing. The object includes:\n        - 'transcript': continuous text from the entire video (concatenated if multiple parts)\n        - 'video_url': the input YouTube URL\n        - 'error': error message if an exception occurs\n        \"\"\"\n        default_data = {\"transcript\": \"\", \"video_url\": self.url, \"error\": None}\n\n        try:\n            transcripts = self._load_transcripts(as_chunks=False)\n            if not transcripts:\n                default_data[\"error\"] = \"No transcripts found.\"\n                return Data(data=default_data)\n\n            # Combine all transcript parts\n            full_transcript = \" \".join(doc.page_content for doc in transcripts)\n            return Data(data={\"transcript\": full_transcript, \"video_url\": self.url})\n\n        except (\n            youtube_transcript_api.TranscriptsDisabled,\n            youtube_transcript_api.NoTranscriptFound,\n            youtube_transcript_api.CouldNotRetrieveTranscript,\n        ) as exc:\n            default_data[\"error\"] = str(exc)\n            return Data(data=default_data)\n"}, "tools_metadata": {"_input_type": "ToolsInput", "advanced": false, "display_name": "Actions", "dynamic": false, "info": "Modify tool names and descriptions to help agents understand when to use each tool.", "is_list": true, "list_add_label": "Add More", "name": "tools_metadata", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "tools", "value": [{"args": {"url": {"description": "Enter the YouTube video URL to get transcripts from.", "title": "Url", "type": "string"}}, "description": "YouTubeTranscripts. get_dataframe_output - Extracts spoken content from YouTube videos with multiple output options.", "display_description": "YouTubeTranscripts. get_dataframe_output - Extracts spoken content from YouTube videos with multiple output options.", "display_name": "get_dataframe_output", "name": "get_dataframe_output", "readonly": false, "status": true, "tags": ["get_dataframe_output"]}, {"args": {"url": {"description": "Enter the YouTube video URL to get transcripts from.", "title": "Url", "type": "string"}}, "description": "YouTubeTranscripts. get_message_output - Extracts spoken content from YouTube videos with multiple output options.", "display_description": "YouTubeTranscripts. get_message_output - Extracts spoken content from YouTube videos with multiple output options.", "display_name": "get_message_output", "name": "get_message_output", "readonly": false, "status": true, "tags": ["get_message_output"]}, {"args": {"url": {"description": "Enter the YouTube video URL to get transcripts from.", "title": "Url", "type": "string"}}, "description": "YouTubeTranscripts. get_data_output - Extracts spoken content from YouTube videos with multiple output options.", "display_description": "YouTubeTranscripts. get_data_output - Extracts spoken content from YouTube videos with multiple output options.", "display_name": "get_data_output", "name": "get_data_output", "readonly": false, "status": true, "tags": ["get_data_output"]}]}, "translation": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Translation Language", "dynamic": false, "info": "Translate the transcripts to the specified language. Leave empty for no translation.", "name": "translation", "options": ["", "en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "hi", "ar", "id"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "url": {"_input_type": "MultilineInput", "advanced": false, "display_name": "Video URL", "dynamic": false, "info": "Enter the YouTube video URL to get transcripts from.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "url", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": true}, "selected_output": "component_as_tool", "showNode": true, "type": "YouTubeTranscripts"}, "dragging": false, "id": "YouTubeTranscripts-TlFcG", "measured": {"height": 328, "width": 320}, "position": {"x": 1577.7800211610804, "y": 5995.403751540062}, "selected": false, "type": "genericNode"}, {"data": {"id": "note-eKoWw", "node": {"description": "# 📖 README\nThis flow performs comprehensive analysis of YouTube videos.\n1.  Extract video comments and transcripts.\n2.  Run sentiment analysis on comments using LLM.\n3.  Combine transcript content and comment sentiment for comprehensive video analysis.\n## Quickstart\n-  Add your **OpenAI API Key** to the **Language Model** and **YT-Insight** Agent Component\n-  Add your **YouTube Data API v3 key**\n- If you don't have a YoutTube API key, create one in the [Google Cloud Console](https://console.cloud.google.com).\n- Ensure the chat input is a valid YouTube video URL. A sample URL is provided in the chat input component.\n", "display_name": "", "documentation": "", "template": {"backgroundColor": "neutral"}}, "type": "note"}, "dragging": false, "height": 582, "id": "note-eKoWw", "measured": {"height": 582, "width": 553}, "position": {"x": -499.7725169737987, "y": 5666.258559615042}, "resizing": false, "selected": false, "type": "noteNode", "width": 553}, {"data": {"id": "parser-k0Bpy", "node": {"base_classes": ["Message"], "beta": false, "category": "processing", "conditional_paths": [], "custom_fields": {}, "description": "Format a DataFrame or Data object into text using a template. Enable 'Stringify' to convert input into a readable string instead.", "display_name": "<PERSON><PERSON><PERSON>", "documentation": "", "edited": false, "field_order": ["mode", "pattern", "input_data", "sep"], "frozen": false, "icon": "braces", "key": "parser", "legacy": false, "lf_version": "1.4.3", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Parsed Text", "method": "parse_combined_text", "name": "parsed_text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 2.220446049250313e-16, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import json\nfrom typing import Any\n\nfrom langflow.custom import Component\nfrom langflow.io import (\n    BoolInput,\n    HandleInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n    TabInput,\n)\nfrom langflow.schema import Data, DataFrame\nfrom langflow.schema.message import Message\n\n\nclass ParserComponent(Component):\n    name = \"parser\"\n    display_name = \"Parser\"\n    description = (\n        \"Format a DataFrame or Data object into text using a template. \"\n        \"Enable 'Stringify' to convert input into a readable string instead.\"\n    )\n    icon = \"braces\"\n\n    inputs = [\n        TabInput(\n            name=\"mode\",\n            display_name=\"Mode\",\n            options=[\"Parser\", \"Stringify\"],\n            value=\"Parser\",\n            info=\"Convert into raw string instead of using a template.\",\n            real_time_refresh=True,\n        ),\n        MultilineInput(\n            name=\"pattern\",\n            display_name=\"Template\",\n            info=(\n                \"Use variables within curly brackets to extract column values for DataFrames \"\n                \"or key values for Data.\"\n                \"For example: `Name: {Name}, Age: {Age}, Country: {Country}`\"\n            ),\n            value=\"Text: {text}\",  # Example default\n            dynamic=True,\n            show=True,\n            required=True,\n        ),\n        HandleInput(\n            name=\"input_data\",\n            display_name=\"Data or DataFrame\",\n            input_types=[\"DataFrame\", \"Data\"],\n            info=\"Accepts either a DataFrame or a Data object.\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"sep\",\n            display_name=\"Separator\",\n            advanced=True,\n            value=\"\\n\",\n            info=\"String used to separate rows/items.\",\n        ),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Parsed Text\",\n            name=\"parsed_text\",\n            info=\"Formatted text output.\",\n            method=\"parse_combined_text\",\n        ),\n    ]\n\n    def update_build_config(self, build_config, field_value, field_name=None):\n        \"\"\"Dynamically hide/show `template` and enforce requirement based on `stringify`.\"\"\"\n        if field_name == \"mode\":\n            build_config[\"pattern\"][\"show\"] = self.mode == \"Parser\"\n            build_config[\"pattern\"][\"required\"] = self.mode == \"Parser\"\n            if field_value:\n                clean_data = BoolInput(\n                    name=\"clean_data\",\n                    display_name=\"Clean Data\",\n                    info=(\n                        \"Enable to clean the data by removing empty rows and lines \"\n                        \"in each cell of the DataFrame/ Data object.\"\n                    ),\n                    value=True,\n                    advanced=True,\n                    required=False,\n                )\n                build_config[\"clean_data\"] = clean_data.to_dict()\n            else:\n                build_config.pop(\"clean_data\", None)\n\n        return build_config\n\n    def _clean_args(self):\n        \"\"\"Prepare arguments based on input type.\"\"\"\n        input_data = self.input_data\n\n        match input_data:\n            case list() if all(isinstance(item, Data) for item in input_data):\n                msg = \"List of Data objects is not supported.\"\n                raise ValueError(msg)\n            case DataFrame():\n                return input_data, None\n            case Data():\n                return None, input_data\n            case dict() if \"data\" in input_data:\n                try:\n                    if \"columns\" in input_data:  # Likely a DataFrame\n                        return DataFrame.from_dict(input_data), None\n                    # Likely a Data object\n                    return None, Data(**input_data)\n                except (TypeError, ValueError, KeyError) as e:\n                    msg = f\"Invalid structured input provided: {e!s}\"\n                    raise ValueError(msg) from e\n            case _:\n                msg = f\"Unsupported input type: {type(input_data)}. Expected DataFrame or Data.\"\n                raise ValueError(msg)\n\n    def parse_combined_text(self) -> Message:\n        \"\"\"Parse all rows/items into a single text or convert input to string if `stringify` is enabled.\"\"\"\n        # Early return for stringify option\n        if self.mode == \"Stringify\":\n            return self.convert_to_string()\n\n        df, data = self._clean_args()\n\n        lines = []\n        if df is not None:\n            for _, row in df.iterrows():\n                formatted_text = self.pattern.format(**row.to_dict())\n                lines.append(formatted_text)\n        elif data is not None:\n            formatted_text = self.pattern.format(**data.data)\n            lines.append(formatted_text)\n\n        combined_text = self.sep.join(lines)\n        self.status = combined_text\n        return Message(text=combined_text)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                return json.dumps(data.data)\n            if isinstance(data, DataFrame):\n                if hasattr(self, \"clean_data\") and self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n                return data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> Message:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        result = \"\"\n        if isinstance(self.input_data, list):\n            result = \"\\n\".join([self._safe_convert(item) for item in self.input_data])\n        else:\n            result = self._safe_convert(self.input_data)\n        self.log(f\"Converted to string with length: {len(result)}\")\n\n        message = Message(text=result)\n        self.status = message\n        return message\n"}, "input_data": {"_input_type": "HandleInput", "advanced": false, "display_name": "Data or DataFrame", "dynamic": false, "info": "Accepts either a DataFrame or a Data object.", "input_types": ["DataFrame", "Data"], "list": false, "list_add_label": "Add More", "name": "input_data", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "mode": {"_input_type": "TabInput", "advanced": false, "display_name": "Mode", "dynamic": false, "info": "Convert into raw string instead of using a template.", "name": "mode", "options": ["<PERSON><PERSON><PERSON>", "Stringify"], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "tab", "value": "<PERSON><PERSON><PERSON>"}, "pattern": {"_input_type": "MultilineInput", "advanced": false, "copy_field": false, "display_name": "Template", "dynamic": true, "info": "Use variables within curly brackets to extract column values for DataFrames or key values for Data.For example: `Name: {Name}, Age: {Age}, Country: {Country}`", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "pattern", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{model_response}"}, "sep": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Separator", "dynamic": false, "info": "String used to separate rows/items.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "\n"}}, "tool_mode": false}, "selected_output": "parsed_text", "showNode": true, "type": "parser"}, "dragging": false, "id": "parser-k0Bpy", "measured": {"height": 361, "width": 320}, "position": {"x": 1055.5958957337489, "y": 5951.514633172236}, "selected": false, "type": "genericNode"}, {"data": {"id": "LanguageModelComponent-OvIt5", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "category": "models", "conditional_paths": [], "custom_fields": {}, "description": "Runs a language model given a specified provider. ", "display_name": "Language Model", "documentation": "", "edited": false, "field_order": ["provider", "model_name", "api_key", "input_value", "system_message", "stream", "temperature"], "frozen": false, "icon": "brain-circuit", "key": "LanguageModelComponent", "last_updated": "2025-07-07T14:52:15.000Z", "legacy": false, "lf_version": "1.4.3", "metadata": {"keywords": ["model", "llm", "language model", "large language model"]}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Model Response", "group_outputs": false, "method": "text_response", "name": "text_output", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Language Model", "group_outputs": false, "method": "build_model", "name": "model_output", "options": null, "required_inputs": null, "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "priority": 0, "score": 0.28173906304863156, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "Model Provider API key", "input_types": [], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nfrom langchain_anthropic import Cha<PERSON><PERSON><PERSON><PERSON>\nfrom langchain_google_genai import ChatGoogleGenerativeAI\nfrom langchain_openai import ChatOpenAI\n\nfrom langflow.base.models.anthropic_constants import ANTHROPIC_MODELS\nfrom langflow.base.models.google_generative_ai_constants import GOOGLE_GENERATIVE_AI_MODELS\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_CHAT_MODEL_NAMES, OPENAI_REASONING_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs.inputs import BoolInput\nfrom langflow.io import DropdownInput, MessageInput, MultilineInput, SecretStrInput, SliderInput\nfrom langflow.schema.dotdict import dotdict\n\n\nclass LanguageModelComponent(LCModelComponent):\n    display_name = \"Language Model\"\n    description = \"Runs a language model given a specified provider.\"\n    documentation: str = \"https://docs.langflow.org/components-models\"\n    icon = \"brain-circuit\"\n    category = \"models\"\n    priority = 0  # Set priority to 0 to make it appear first\n\n    inputs = [\n        DropdownInput(\n            name=\"provider\",\n            display_name=\"Model Provider\",\n            options=[\"OpenAI\", \"Anthropic\", \"Google\"],\n            value=\"OpenAI\",\n            info=\"Select the model provider\",\n            real_time_refresh=True,\n            options_metadata=[{\"icon\": \"OpenAI\"}, {\"icon\": \"Anthropic\"}, {\"icon\": \"GoogleGenerativeAI\"}],\n        ),\n        DropdownInput(\n            name=\"model_name\",\n            display_name=\"Model Name\",\n            options=OPENAI_CHAT_MODEL_NAMES + OPENAI_REASONING_MODEL_NAMES,\n            value=OPENAI_CHAT_MODEL_NAMES[0],\n            info=\"Select the model to use\",\n            real_time_refresh=True,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API Key\",\n            info=\"Model Provider API key\",\n            required=False,\n            show=True,\n            real_time_refresh=True,\n        ),\n        MessageInput(\n            name=\"input_value\",\n            display_name=\"Input\",\n            info=\"The input text to send to the model\",\n        ),\n        MultilineInput(\n            name=\"system_message\",\n            display_name=\"System Message\",\n            info=\"A system message that helps set the behavior of the assistant\",\n            advanced=False,\n        ),\n        BoolInput(\n            name=\"stream\",\n            display_name=\"Stream\",\n            info=\"Whether to stream the response\",\n            value=False,\n            advanced=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"Temperature\",\n            value=0.1,\n            info=\"Controls randomness in responses\",\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:\n        provider = self.provider\n        model_name = self.model_name\n        temperature = self.temperature\n        stream = self.stream\n\n        if provider == \"OpenAI\":\n            if not self.api_key:\n                msg = \"OpenAI API key is required when using OpenAI provider\"\n                raise ValueError(msg)\n\n            if model_name in OPENAI_REASONING_MODEL_NAMES:\n                # reasoning models do not support temperature (yet)\n                temperature = None\n\n            return ChatOpenAI(\n                model_name=model_name,\n                temperature=temperature,\n                streaming=stream,\n                openai_api_key=self.api_key,\n            )\n        if provider == \"Anthropic\":\n            if not self.api_key:\n                msg = \"Anthropic API key is required when using Anthropic provider\"\n                raise ValueError(msg)\n            return ChatAnthropic(\n                model=model_name,\n                temperature=temperature,\n                streaming=stream,\n                anthropic_api_key=self.api_key,\n            )\n        if provider == \"Google\":\n            if not self.api_key:\n                msg = \"Google API key is required when using Google provider\"\n                raise ValueError(msg)\n            return ChatGoogleGenerativeAI(\n                model=model_name,\n                temperature=temperature,\n                streaming=stream,\n                google_api_key=self.api_key,\n            )\n        msg = f\"Unknown provider: {provider}\"\n        raise ValueError(msg)\n\n    def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None) -> dotdict:\n        if field_name == \"provider\":\n            if field_value == \"OpenAI\":\n                build_config[\"model_name\"][\"options\"] = OPENAI_CHAT_MODEL_NAMES + OPENAI_REASONING_MODEL_NAMES\n                build_config[\"model_name\"][\"value\"] = OPENAI_CHAT_MODEL_NAMES[0]\n                build_config[\"api_key\"][\"display_name\"] = \"OpenAI API Key\"\n            elif field_value == \"Anthropic\":\n                build_config[\"model_name\"][\"options\"] = ANTHROPIC_MODELS\n                build_config[\"model_name\"][\"value\"] = ANTHROPIC_MODELS[0]\n                build_config[\"api_key\"][\"display_name\"] = \"Anthropic API Key\"\n            elif field_value == \"Google\":\n                build_config[\"model_name\"][\"options\"] = GOOGLE_GENERATIVE_AI_MODELS\n                build_config[\"model_name\"][\"value\"] = GOOGLE_GENERATIVE_AI_MODELS[0]\n                build_config[\"api_key\"][\"display_name\"] = \"Google API Key\"\n        elif field_name == \"model_name\" and field_value.startswith(\"o1\") and self.provider == \"OpenAI\":\n            # Hide system_message for o1 models - currently unsupported\n            if \"system_message\" in build_config:\n                build_config[\"system_message\"][\"show\"] = False\n        elif field_name == \"model_name\" and not field_value.startswith(\"o1\") and \"system_message\" in build_config:\n            build_config[\"system_message\"][\"show\"] = True\n        return build_config\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "Input", "dynamic": false, "info": "The input text to send to the model", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "Select the model to use", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o-mini"}, "provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Provider", "dynamic": false, "info": "Select the model provider", "name": "provider", "options": ["OpenAI", "Anthropic", "Google"], "options_metadata": [{"icon": "OpenAI"}, {"icon": "Anthropic"}, {"icon": "GoogleGenerativeAI"}], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "OpenAI"}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "Stream", "dynamic": false, "info": "Whether to stream the response", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": true, "copy_field": false, "display_name": "System Message", "dynamic": false, "info": "A system message that helps set the behavior of the assistant", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "Temperature", "dynamic": false, "info": "Controls randomness in responses", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}}, "tool_mode": false}, "selected_output": "model_output", "showNode": true, "type": "LanguageModelComponent"}, "dragging": false, "id": "LanguageModelComponent-OvIt5", "measured": {"height": 451, "width": 320}, "position": {"x": 169.44463633485168, "y": 5440.597009802169}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatInput-kaWcL", "node": {"base_classes": ["Message"], "beta": false, "category": "input_output", "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "https://docs.langflow.org/components-io#chat-input", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatInput", "legacy": false, "lf_version": "1.4.3", "metadata": {"code_hash": "192913db3453", "module": "langflow.components.input_output.chat.ChatInput"}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Chat Message", "group_outputs": false, "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.0020353564437605998, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"Chat Input\"\n    description = \"Get chat inputs from the Playground.\"\n    documentation: str = \"https://docs.langflow.org/components-io#chat-input\"\n    icon = \"MessagesSquare\"\n    name = \"ChatInput\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Input Text\",\n            value=\"\",\n            info=\"Message to be passed as input.\",\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"Type of sender.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",\n            display_name=\"Files\",\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"Files to be sent with the message.\",\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Chat Message\", name=\"message\", method=\"message_response\"),\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"_input_type": "FileInput", "advanced": true, "display_name": "Files", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "Files to be sent with the message.", "list": true, "list_add_label": "Add More", "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "copy_field": false, "display_name": "Input Text", "dynamic": false, "info": "Message to be passed as input.", "input_types": [], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "https://www.youtube.com/watch?v=8f61j3W-27U&ab_channel=Langflow"}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "ChatInput"}, "dragging": false, "id": "ChatInput-kaWcL", "measured": {"height": 204, "width": 320}, "position": {"x": -467.5929504081053, "y": 6344.887293468443}, "selected": false, "type": "genericNode"}], "viewport": {"x": 508.76864320642323, "y": -4013.82831792662, "zoom": 0.763024505964603}}, "description": "The YouTube Analysis flow extracts video comments and transcripts, analyzing sentiment patterns and content themes.", "endpoint_name": null, "id": "3d1e15c2-b095-46de-8247-949c7a5bda04", "is_component": false, "last_tested_version": "1.4.3", "name": "YouTube Analysis", "tags": ["agents", "assistants"]}