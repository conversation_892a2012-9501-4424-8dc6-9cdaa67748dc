{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-et7o5", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "question", "id": "Prompt-V3tlJ", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "reactflow__edge-ChatInput-et7o5{œdataTypeœ:œChatInputœ,œidœ:œChatInput-et7o5œ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-Prompt-V3tlJ{œfieldNameœ:œquestionœ,œidœ:œPrompt-V3tlJœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-et7o5", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-et7o5œ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-V3tlJ", "targetHandle": "{œfieldNameœ: œquestionœ, œidœ: œPrompt-V3tlJœ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "parser", "id": "parser-WUXPk", "name": "parsed_text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "context", "id": "Prompt-V3tlJ", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "reactflow__edge-parser-WUXPk{œdataTypeœ:œparserœ,œidœ:œparser-WUXPkœ,œnameœ:œparsed_textœ,œoutput_typesœ:[œMessageœ]}-Prompt-V3tlJ{œfieldNameœ:œcontextœ,œidœ:œPrompt-V3tlJœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "source": "parser-WUXPk", "sourceHandle": "{œdataTypeœ: œparserœ, œidœ: œparser-WUXPkœ, œnameœ: œparsed_textœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-V3tlJ", "targetHandle": "{œfieldNameœ: œcontextœ, œidœ: œPrompt-V3tlJœ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIEmbeddings", "id": "OpenAIEmbeddings-oFtHy", "name": "embeddings", "output_types": ["Embeddings"]}, "targetHandle": {"fieldName": "embedding_model", "id": "AstraDB-W6NB4", "inputTypes": ["Embeddings"], "type": "other"}}, "id": "reactflow__edge-OpenAIEmbeddings-oFtHy{œdataTypeœ:œOpenAIEmbeddingsœ,œidœ:œOpenAIEmbeddings-oFtHyœ,œnameœ:œembeddingsœ,œoutput_typesœ:[œEmbeddingsœ]}-AstraDB-W6NB4{œfieldNameœ:œembedding_modelœ,œidœ:œAstraDB-W6NB4œ,œinputTypesœ:[œEmbeddingsœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIEmbeddings-oFtHy", "sourceHandle": "{œdataTypeœ: œOpenAIEmbeddingsœ, œidœ: œOpenAIEmbeddings-oFtHyœ, œnameœ: œembeddingsœ, œoutput_typesœ: [œEmbeddingsœ]}", "target": "AstraDB-W6NB4", "targetHandle": "{œfieldNameœ: œembedding_modelœ, œidœ: œAstraDB-W6NB4œ, œinputTypesœ: [œEmbeddingsœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIEmbeddings", "id": "OpenAIEmbeddings-v0rcw", "name": "embeddings", "output_types": ["Embeddings"]}, "targetHandle": {"fieldName": "embedding_model", "id": "AstraDB-JsRrT", "inputTypes": ["Embeddings"], "type": "other"}}, "id": "reactflow__edge-OpenAIEmbeddings-v0rcw{œdataTypeœ:œOpenAIEmbeddingsœ,œidœ:œOpenAIEmbeddings-v0rcwœ,œnameœ:œembeddingsœ,œoutput_typesœ:[œEmbeddingsœ]}-AstraDB-JsRrT{œfieldNameœ:œembedding_modelœ,œidœ:œAstraDB-JsRrTœ,œinputTypesœ:[œEmbeddingsœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIEmbeddings-v0rcw", "sourceHandle": "{œdataTypeœ: œOpenAIEmbeddingsœ, œidœ: œOpenAIEmbeddings-v0rcwœ, œnameœ: œembeddingsœ, œoutput_typesœ: [œEmbeddingsœ]}", "target": "AstraDB-JsRrT", "targetHandle": "{œfieldNameœ: œembedding_modelœ, œidœ: œAstraDB-JsRrTœ, œinputTypesœ: [œEmbeddingsœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-et7o5", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "search_query", "id": "AstraDB-JsRrT", "inputTypes": ["Message"], "type": "query"}}, "id": "reactflow__edge-ChatInput-et7o5{œdataTypeœ:œChatInputœ,œidœ:œChatInput-et7o5œ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-AstraDB-JsRrT{œfieldNameœ:œsearch_queryœ,œidœ:œAstraDB-JsRrTœ,œinputTypesœ:[œMessageœ],œtypeœ:œqueryœ}", "selected": false, "source": "ChatInput-et7o5", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-et7o5œ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "AstraDB-JsRrT", "targetHandle": "{œfieldNameœ: œsearch_queryœ, œidœ: œAstraDB-JsRrTœ, œinputTypesœ: [œMessageœ], œtypeœ: œqueryœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "AstraDB", "id": "AstraDB-JsRrT", "name": "dataframe", "output_types": ["DataFrame"]}, "targetHandle": {"fieldName": "input_data", "id": "parser-WUXPk", "inputTypes": ["DataFrame", "Data"], "type": "other"}}, "id": "reactflow__edge-AstraDB-JsRrT{œdataTypeœ:œAstraDBœ,œidœ:œAstraDB-JsRrTœ,œnameœ:œdataframeœ,œoutput_typesœ:[œDataFrameœ]}-parser-WUXPk{œfieldNameœ:œinput_dataœ,œidœ:œparser-WUXPkœ,œinputTypesœ:[œDataFrameœ,œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "AstraDB-JsRrT", "sourceHandle": "{œdataTypeœ: œAstraDBœ, œidœ: œAstraDB-JsRrTœ, œnameœ: œdataframeœ, œoutput_typesœ: [œDataFrameœ]}", "target": "parser-WUXPk", "targetHandle": "{œfieldNameœ: œinput_dataœ, œidœ: œparser-WUXPkœ, œinputTypesœ: [œDataFrameœ, œDataœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "SplitText", "id": "SplitText-6H5cD", "name": "dataframe", "output_types": ["DataFrame"]}, "targetHandle": {"fieldName": "ingest_data", "id": "AstraDB-W6NB4", "inputTypes": ["Data", "DataFrame"], "type": "other"}}, "id": "reactflow__edge-SplitText-6H5cD{œdataTypeœ:œSplitTextœ,œidœ:œSplitText-6H5cDœ,œnameœ:œdataframeœ,œoutput_typesœ:[œDataFrameœ]}-AstraDB-W6NB4{œfieldNameœ:œingest_dataœ,œidœ:œAstraDB-W6NB4œ,œinputTypesœ:[œDataœ,œDataFrameœ],œtypeœ:œotherœ}", "selected": false, "source": "SplitText-6H5cD", "sourceHandle": "{œdataTypeœ: œSplitTextœ, œidœ: œSplitText-6H5cDœ, œnameœ: œdataframeœ, œoutput_typesœ: [œDataFrameœ]}", "target": "AstraDB-W6NB4", "targetHandle": "{œfieldNameœ: œingest_dataœ, œidœ: œAstraDB-W6NB4œ, œinputTypesœ: [œDataœ, œDataFrameœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "File", "id": "File-vusZ2", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "data_inputs", "id": "SplitText-6H5cD", "inputTypes": ["Data", "DataFrame", "Message"], "type": "other"}}, "id": "reactflow__edge-File-vusZ2{œdataTypeœ:œFileœ,œidœ:œFile-vusZ2œ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-SplitText-6H5cD{œfieldNameœ:œdata_inputsœ,œidœ:œSplitText-6H5cDœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œotherœ}", "selected": false, "source": "File-vusZ2", "sourceHandle": "{œdataTypeœ: œFileœ, œidœ: œFile-vusZ2œ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "SplitText-6H5cD", "targetHandle": "{œfieldNameœ: œdata_inputsœ, œidœ: œSplitText-6H5cDœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-V3tlJ", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "LanguageModelComponent-1uhUK", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-V3tlJ{œdataTypeœ:œPromptœ,œidœ:œPrompt-V3tlJœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-LanguageModelComponent-1uhUK{œfieldNameœ:œinput_valueœ,œidœ:œLanguageModelComponent-1uhUKœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-V3tlJ", "sourceHandle": "{œdataTypeœ: œPromptœ, œidœ: œPrompt-V3tlJœ, œnameœ: œpromptœ, œoutput_typesœ: [œMessageœ]}", "target": "LanguageModelComponent-1uhUK", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œLanguageModelComponent-1uhUKœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "LanguageModelComponent", "id": "LanguageModelComponent-1uhUK", "name": "text_output", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-ZaYDW", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-LanguageModelComponent-1uhUK{œdataTypeœ:œLanguageModelComponentœ,œidœ:œLanguageModelComponent-1uhUKœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-ZaYDW{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-ZaYDWœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "LanguageModelComponent-1uhUK", "sourceHandle": "{œdataTypeœ: œLanguageModelComponentœ, œidœ: œLanguageModelComponent-1uhUKœ, œnameœ: œtext_outputœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-ZaYDW", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-ZaYDWœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}], "nodes": [{"data": {"description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "id": "ChatInput-et7o5", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.2.0", "metadata": {"code_hash": "192913db3453", "module": "langflow.components.input_output.chat.ChatInput"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Chat Message", "group_outputs": false, "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"Chat Input\"\n    description = \"Get chat inputs from the Playground.\"\n    documentation: str = \"https://docs.langflow.org/components-io#chat-input\"\n    icon = \"MessagesSquare\"\n    name = \"ChatInput\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Input Text\",\n            value=\"\",\n            info=\"Message to be passed as input.\",\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"Type of sender.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",\n            display_name=\"Files\",\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"Files to be sent with the message.\",\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Chat Message\", name=\"message\", method=\"message_response\"),\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"advanced": true, "display_name": "Files", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "Files to be sent with the message.", "list": true, "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"advanced": false, "display_name": "Input Text", "dynamic": false, "info": "Message to be passed as input.", "input_types": [], "list": false, "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "What is this document about?"}, "sender": {"advanced": true, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}}, "selected_output": "message", "type": "ChatInput"}, "dragging": false, "height": 234, "id": "ChatInput-et7o5", "measured": {"height": 234, "width": 320}, "position": {"x": 827.4877596995269, "y": 421.8759496538444}, "positionAbsolute": {"x": 743.9745420290319, "y": 463.6977510207854}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "id": "Prompt-V3tlJ", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": ["context", "question"]}, "description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "documentation": "", "edited": false, "error": null, "field_order": ["template"], "frozen": false, "full_path": null, "icon": "braces", "is_composition": null, "is_input": null, "is_output": null, "legacy": false, "lf_version": "1.1.1", "metadata": {"code_hash": "3bf0b511e227", "module": "langflow.components.prompts.prompt.PromptComponent"}, "name": "", "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Prompt", "group_outputs": false, "method": "build_prompt", "name": "prompt", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom.custom_component.component import Component\nfrom langflow.inputs.inputs import <PERSON>fa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"Prompt\"\n    description: str = \"Create a prompt template with dynamic variables.\"\n    icon = \"braces\"\n    trace_type = \"prompt\"\n    name = \"Prompt\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"Template\"),\n        MessageTextInput(\n            name=\"tool_placeholder\",\n            display_name=\"Tool Placeholder\",\n            tool_mode=True,\n            advanced=True,\n            info=\"A placeholder input for tool mode.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Prompt\", name=\"prompt\", method=\"build_prompt\"),\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "context": {"advanced": false, "display_name": "context", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "context", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "question": {"advanced": false, "display_name": "question", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "question", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "template": {"advanced": false, "display_name": "Template", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "prompt", "value": "{context}\n\n---\n\nGiven the context above, answer the question as best as possible.\n\nQuestion: {question}\n\nAnswer: "}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Tool Placeholder", "dynamic": false, "info": "A placeholder input for tool mode.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "selected_output": "prompt", "type": "Prompt"}, "dragging": false, "height": 433, "id": "Prompt-V3tlJ", "measured": {"height": 433, "width": 320}, "position": {"x": 1977.9097981422992, "y": 640.5656416923846}, "positionAbsolute": {"x": 1977.9097981422992, "y": 640.5656416923846}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Split text into chunks based on specified criteria.", "display_name": "Split Text", "id": "SplitText-6H5cD", "node": {"base_classes": ["Data"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Split text into chunks based on specified criteria.", "display_name": "Split Text", "documentation": "", "edited": false, "field_order": ["data_inputs", "chunk_overlap", "chunk_size", "separator"], "frozen": false, "icon": "scissors-line-dashed", "legacy": false, "lf_version": "1.1.1", "metadata": {"code_hash": "dbf2e9d2319d", "module": "langflow.components.processing.split_text.SplitTextComponent"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Chunks", "group_outputs": false, "method": "split_text", "name": "dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "chunk_overlap": {"advanced": false, "display_name": "<PERSON><PERSON>", "dynamic": false, "info": "Number of characters to overlap between chunks.", "list": false, "name": "chunk_overlap", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 200}, "chunk_size": {"advanced": false, "display_name": "Chunk Size", "dynamic": false, "info": "The maximum length of each chunk. Text is first split by separator, then chunks are merged up to this size. Individual splits larger than this won't be further divided.", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_text_splitters import CharacterTextSplitter\n\nfrom langflow.custom.custom_component.component import Component\nfrom langflow.io import DropdownInput, HandleInput, IntInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\nfrom langflow.utils.util import unescape_string\n\n\nclass SplitTextComponent(Component):\n    display_name: str = \"Split Text\"\n    description: str = \"Split text into chunks based on specified criteria.\"\n    documentation: str = \"https://docs.langflow.org/components-processing#split-text\"\n    icon = \"scissors-line-dashed\"\n    name = \"SplitText\"\n\n    inputs = [\n        HandleInput(\n            name=\"data_inputs\",\n            display_name=\"Input\",\n            info=\"The data with texts to split in chunks.\",\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        IntInput(\n            name=\"chunk_overlap\",\n            display_name=\"Chunk Overlap\",\n            info=\"Number of characters to overlap between chunks.\",\n            value=200,\n        ),\n        IntInput(\n            name=\"chunk_size\",\n            display_name=\"Chunk Size\",\n            info=(\n                \"The maximum length of each chunk. Text is first split by separator, \"\n                \"then chunks are merged up to this size. \"\n                \"Individual splits larger than this won't be further divided.\"\n            ),\n            value=1000,\n        ),\n        MessageTextInput(\n            name=\"separator\",\n            display_name=\"Separator\",\n            info=(\n                \"The character to split on. Use \\\\n for newline. \"\n                \"Examples: \\\\n\\\\n for paragraphs, \\\\n for lines, . for sentences\"\n            ),\n            value=\"\\n\",\n        ),\n        MessageTextInput(\n            name=\"text_key\",\n            display_name=\"Text Key\",\n            info=\"The key to use for the text column.\",\n            value=\"text\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"keep_separator\",\n            display_name=\"Keep Separator\",\n            info=\"Whether to keep the separator in the output chunks and where to place it.\",\n            options=[\"False\", \"True\", \"Start\", \"End\"],\n            value=\"False\",\n            advanced=True,\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Chunks\", name=\"dataframe\", method=\"split_text\"),\n    ]\n\n    def _docs_to_data(self, docs) -> list[Data]:\n        return [Data(text=doc.page_content, data=doc.metadata) for doc in docs]\n\n    def _fix_separator(self, separator: str) -> str:\n        \"\"\"Fix common separator issues and convert to proper format.\"\"\"\n        if separator == \"/n\":\n            return \"\\n\"\n        if separator == \"/t\":\n            return \"\\t\"\n        return separator\n\n    def split_text_base(self):\n        separator = self._fix_separator(self.separator)\n        separator = unescape_string(separator)\n\n        if isinstance(self.data_inputs, DataFrame):\n            if not len(self.data_inputs):\n                msg = \"DataFrame is empty\"\n                raise TypeError(msg)\n\n            self.data_inputs.text_key = self.text_key\n            try:\n                documents = self.data_inputs.to_lc_documents()\n            except Exception as e:\n                msg = f\"Error converting DataFrame to documents: {e}\"\n                raise TypeError(msg) from e\n        elif isinstance(self.data_inputs, Message):\n            self.data_inputs = [self.data_inputs.to_data()]\n            return self.split_text_base()\n        else:\n            if not self.data_inputs:\n                msg = \"No data inputs provided\"\n                raise TypeError(msg)\n\n            documents = []\n            if isinstance(self.data_inputs, Data):\n                self.data_inputs.text_key = self.text_key\n                documents = [self.data_inputs.to_lc_document()]\n            else:\n                try:\n                    documents = [input_.to_lc_document() for input_ in self.data_inputs if isinstance(input_, Data)]\n                    if not documents:\n                        msg = f\"No valid Data inputs found in {type(self.data_inputs)}\"\n                        raise TypeError(msg)\n                except AttributeError as e:\n                    msg = f\"Invalid input type in collection: {e}\"\n                    raise TypeError(msg) from e\n        try:\n            # Convert string 'False'/'True' to boolean\n            keep_sep = self.keep_separator\n            if isinstance(keep_sep, str):\n                if keep_sep.lower() == \"false\":\n                    keep_sep = False\n                elif keep_sep.lower() == \"true\":\n                    keep_sep = True\n                # 'start' and 'end' are kept as strings\n\n            splitter = CharacterTextSplitter(\n                chunk_overlap=self.chunk_overlap,\n                chunk_size=self.chunk_size,\n                separator=separator,\n                keep_separator=keep_sep,\n            )\n            return splitter.split_documents(documents)\n        except Exception as e:\n            msg = f\"Error splitting text: {e}\"\n            raise TypeError(msg) from e\n\n    def split_text(self) -> DataFrame:\n        return DataFrame(self._docs_to_data(self.split_text_base()))\n"}, "data_inputs": {"advanced": false, "display_name": "Input", "dynamic": false, "info": "The data with texts to split in chunks.", "input_types": ["Data", "DataFrame", "Message"], "list": false, "name": "data_inputs", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "keep_separator": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Keep Separator", "dynamic": false, "info": "Whether to keep the separator in the output chunks and where to place it.", "name": "keep_separator", "options": ["False", "True", "Start", "End"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "False"}, "separator": {"advanced": false, "display_name": "Separator", "dynamic": false, "info": "The character to split on. Use \\n for newline. Examples: \\n\\n for paragraphs, \\n for lines, . for sentences", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "separator", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "\n"}, "text_key": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Key", "dynamic": false, "info": "The key to use for the text column.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_key", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "text"}}}, "selected_output": "chunks", "type": "SplitText"}, "dragging": false, "height": 475, "id": "SplitText-6H5cD", "measured": {"height": 475, "width": 320}, "position": {"x": 1692.461995335383, "y": 1328.2681481569232}, "positionAbsolute": {"x": 1683.4543896546102, "y": 1350.7871623588553}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-rR7dl", "node": {"description": "## 🐕 2. Retriever Flow\n\nThis flow answers your questions with contextual data retrieved from your vector database.\n\nOpen the **Playground** and ask, \n\n```\nWhat is this document about?\n```\n", "display_name": "", "documentation": "", "template": {"backgroundColor": "neutral"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-rR7dl", "measured": {"height": 324, "width": 324}, "position": {"x": 374.388314931542, "y": 486.18094072679895}, "positionAbsolute": {"x": 374.388314931542, "y": 486.18094072679895}, "resizing": false, "selected": false, "style": {"height": 324, "width": 324}, "type": "noteNode", "width": 324}, {"data": {"id": "note-zTYux", "node": {"description": "## 📖 README\n\nLoad your data into a vector database with the 📚 **Load Data** flow, and then use your data as chat context with the 🐕 **Retriever** flow.\n\n**🚨 Add your OpenAI API key as a global variable to easily add it to all of the OpenAI components in this flow.** \n\n**Quick start**\n1. Run the 📚 **Load Data** flow.\n2. Run the 🐕 **Retriever** flow.\n\n**Next steps** \n\n- Experiment by changing the prompt and the loaded data to see how the bot's responses change. \n\nFor more info, see the [Langflow docs](https://docs.langflow.org/starter-projects-vector-store-rag).", "display_name": "Read Me", "documentation": "", "template": {"backgroundColor": "neutral"}}, "type": "note"}, "dragging": false, "height": 556, "id": "note-zTYux", "measured": {"height": 556, "width": 389}, "position": {"x": 191.12162720143235, "y": 1157.6038620251531}, "positionAbsolute": {"x": 94.28986613312418, "y": 907.6428043837066}, "resizing": false, "selected": false, "style": {"height": 324, "width": 324}, "type": "noteNode", "width": 389}, {"data": {"description": "Display a chat message in the Playground.", "display_name": "Chat Output", "id": "ChatOutput-ZaYDW", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.1.1", "metadata": {"code_hash": "6f74e04e39d5", "module": "langflow.components.input_output.chat_output.ChatOutput"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Output Message", "group_outputs": false, "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "Basic Clean Data", "dynamic": false, "info": "Whether to clean the data", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nimport orjson\nfrom fastapi.encoders import jsonable_encoder\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.helpers.data import safe_convert\nfrom langflow.inputs.inputs import BoolInput, DropdownInput, HandleInput, MessageTextInput\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.template.field.base import Output\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    documentation: str = \"https://docs.langflow.org/components-io#chat-output\"\n    icon = \"MessagesSquare\"\n    name = \"ChatOutput\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",\n            display_name=\"Inputs\",\n            info=\"Message to be passed as output.\",\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",\n            display_name=\"Basic Clean Data\",\n            value=True,\n            info=\"Whether to clean the data\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"Output Message\",\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _serialize_data(self, data: Data) -> str:\n        \"\"\"Serialize Data object to JSON string.\"\"\"\n        # Convert data.data to JSON-serializable format\n        serializable_data = jsonable_encoder(data.data)\n        # Serialize with orjson, enabling pretty printing with indentation\n        json_bytes = orjson.dumps(serializable_data, option=orjson.OPT_INDENT_2)\n        # Convert bytes to string and wrap in Markdown code blocks\n        return \"```json\\n\" + json_bytes.decode(\"utf-8\") + \"\\n```\"\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([safe_convert(item, clean_data=self.clean_data) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Data Template", "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "Inputs", "dynamic": false, "info": "Message to be passed as output.", "input_types": ["Data", "DataFrame", "Message"], "list": false, "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "ChatOutput"}, "dragging": false, "height": 234, "id": "ChatOutput-ZaYDW", "measured": {"height": 234, "width": 320}, "position": {"x": 2738.611008351098, "y": 829.6219994149209}, "positionAbsolute": {"x": 2734.385670401691, "y": 810.6079786425926}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "OpenAIEmbeddings-v0rcw", "node": {"base_classes": ["Embeddings"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Generate embeddings using OpenAI models.", "display_name": "OpenAI Embeddings", "documentation": "", "edited": false, "field_order": ["default_headers", "default_query", "chunk_size", "client", "deployment", "embedding_ctx_length", "max_retries", "model", "model_kwargs", "openai_api_key", "openai_api_base", "openai_api_type", "openai_api_version", "openai_organization", "openai_proxy", "request_timeout", "show_progress_bar", "skip_empty", "tiktoken_model_name", "tiktoken_enable", "dimensions"], "frozen": false, "icon": "OpenAI", "legacy": false, "lf_version": "1.2.0", "metadata": {"code_hash": "2691dee277c9", "module": "langflow.components.openai.openai.OpenAIEmbeddingsComponent"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Embedding Model", "group_outputs": false, "method": "build_embeddings", "name": "embeddings", "selected": "Embeddings", "tool_mode": true, "types": ["Embeddings"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "chunk_size": {"_input_type": "IntInput", "advanced": true, "display_name": "Chunk Size", "dynamic": false, "info": "", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "client": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Client", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "client", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import OpenAIEmbeddings\n\nfrom langflow.base.embeddings.model import LCEmbeddingsModel\nfrom langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES\nfrom langflow.field_typing import Embeddings\nfrom langflow.io import BoolInput, DictInput, DropdownInput, FloatInput, IntInput, MessageTextInput, SecretStrInput\n\n\nclass OpenAIEmbeddingsComponent(LCEmbeddingsModel):\n    display_name = \"OpenAI Embeddings\"\n    description = \"Generate embeddings using OpenAI models.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIEmbeddings\"\n\n    inputs = [\n        DictInput(\n            name=\"default_headers\",\n            display_name=\"Default Headers\",\n            advanced=True,\n            info=\"Default headers to use for the API request.\",\n        ),\n        DictInput(\n            name=\"default_query\",\n            display_name=\"Default Query\",\n            advanced=True,\n            info=\"Default query parameters to use for the API request.\",\n        ),\n        IntInput(name=\"chunk_size\", display_name=\"Chunk Size\", advanced=True, value=1000),\n        MessageTextInput(name=\"client\", display_name=\"Client\", advanced=True),\n        MessageTextInput(name=\"deployment\", display_name=\"Deployment\", advanced=True),\n        IntInput(name=\"embedding_ctx_length\", display_name=\"Embedding Context Length\", advanced=True, value=1536),\n        IntInput(name=\"max_retries\", display_name=\"Max Retries\", value=3, advanced=True),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"Model\",\n            advanced=False,\n            options=OPENAI_EMBEDDING_MODEL_NAMES,\n            value=\"text-embedding-3-small\",\n        ),\n        DictInput(name=\"model_kwargs\", display_name=\"Model Kwargs\", advanced=True),\n        SecretStrInput(name=\"openai_api_key\", display_name=\"OpenAI API Key\", value=\"OPENAI_API_KEY\", required=True),\n        MessageTextInput(name=\"openai_api_base\", display_name=\"OpenAI API Base\", advanced=True),\n        MessageTextInput(name=\"openai_api_type\", display_name=\"OpenAI API Type\", advanced=True),\n        MessageTextInput(name=\"openai_api_version\", display_name=\"OpenAI API Version\", advanced=True),\n        MessageTextInput(\n            name=\"openai_organization\",\n            display_name=\"OpenAI Organization\",\n            advanced=True,\n        ),\n        MessageTextInput(name=\"openai_proxy\", display_name=\"OpenAI Proxy\", advanced=True),\n        FloatInput(name=\"request_timeout\", display_name=\"Request Timeout\", advanced=True),\n        BoolInput(name=\"show_progress_bar\", display_name=\"Show Progress Bar\", advanced=True),\n        BoolInput(name=\"skip_empty\", display_name=\"Skip Empty\", advanced=True),\n        MessageTextInput(\n            name=\"tiktoken_model_name\",\n            display_name=\"TikToken Model Name\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"tiktoken_enable\",\n            display_name=\"TikToken Enable\",\n            advanced=True,\n            value=True,\n            info=\"If False, you must have transformers installed.\",\n        ),\n        IntInput(\n            name=\"dimensions\",\n            display_name=\"Dimensions\",\n            info=\"The number of dimensions the resulting output embeddings should have. \"\n            \"Only supported by certain models.\",\n            advanced=True,\n        ),\n    ]\n\n    def build_embeddings(self) -> Embeddings:\n        return OpenAIEmbeddings(\n            client=self.client or None,\n            model=self.model,\n            dimensions=self.dimensions or None,\n            deployment=self.deployment or None,\n            api_version=self.openai_api_version or None,\n            base_url=self.openai_api_base or None,\n            openai_api_type=self.openai_api_type or None,\n            openai_proxy=self.openai_proxy or None,\n            embedding_ctx_length=self.embedding_ctx_length,\n            api_key=self.openai_api_key or None,\n            organization=self.openai_organization or None,\n            allowed_special=\"all\",\n            disallowed_special=\"all\",\n            chunk_size=self.chunk_size,\n            max_retries=self.max_retries,\n            timeout=self.request_timeout or None,\n            tiktoken_enabled=self.tiktoken_enable,\n            tiktoken_model_name=self.tiktoken_model_name or None,\n            show_progress_bar=self.show_progress_bar,\n            model_kwargs=self.model_kwargs,\n            skip_empty=self.skip_empty,\n            default_headers=self.default_headers or None,\n            default_query=self.default_query or None,\n        )\n"}, "default_headers": {"_input_type": "DictInput", "advanced": true, "display_name": "De<PERSON>ult Head<PERSON>", "dynamic": false, "info": "Default headers to use for the API request.", "list": false, "name": "default_headers", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "default_query": {"_input_type": "DictInput", "advanced": true, "display_name": "De<PERSON><PERSON>", "dynamic": false, "info": "Default query parameters to use for the API request.", "list": false, "name": "default_query", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "deployment": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Deployment", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "deployment", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "dimensions": {"_input_type": "IntInput", "advanced": true, "display_name": "Dimensions", "dynamic": false, "info": "The number of dimensions the resulting output embeddings should have. Only supported by certain models.", "list": false, "name": "dimensions", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": ""}, "embedding_ctx_length": {"_input_type": "IntInput", "advanced": true, "display_name": "Embedding Context Length", "dynamic": false, "info": "", "list": false, "name": "embedding_ctx_length", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1536}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Retries", "dynamic": false, "info": "", "list": false, "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 3}, "model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "display_name": "Model", "dynamic": false, "info": "", "name": "model", "options": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text-embedding-3-small"}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "Model Kwargs", "dynamic": false, "info": "", "list": false, "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "openai_api_base": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API Base", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "", "input_types": [], "load_from_db": true, "name": "openai_api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "openai_api_type": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API Type", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_type", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_version": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API Version", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_version", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_organization": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI Organization", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_organization", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_proxy": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI Proxy", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_proxy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "request_timeout": {"_input_type": "FloatInput", "advanced": true, "display_name": "Request Timeout", "dynamic": false, "info": "", "list": false, "name": "request_timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "float", "value": ""}, "show_progress_bar": {"_input_type": "BoolInput", "advanced": true, "display_name": "Show Progress Bar", "dynamic": false, "info": "", "list": false, "name": "show_progress_bar", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "skip_empty": {"_input_type": "BoolInput", "advanced": true, "display_name": "Skip Empty", "dynamic": false, "info": "", "list": false, "name": "skip_empty", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "tiktoken_enable": {"_input_type": "BoolInput", "advanced": true, "display_name": "TikToken Enable", "dynamic": false, "info": "If False, you must have transformers installed.", "list": false, "name": "tiktoken_enable", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "tiktoken_model_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "TikToken Model Name", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tiktoken_model_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "selected_output": "embeddings", "type": "OpenAIEmbeddings"}, "dragging": false, "height": 320, "id": "OpenAIEmbeddings-v0rcw", "measured": {"height": 320, "width": 320}, "position": {"x": 825.435626932521, "y": 739.6327999745448}, "positionAbsolute": {"x": 825.435626932521, "y": 739.6327999745448}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-i07aq", "node": {"description": "## 📚 1. Load Data Flow\n\nRun this first! Load data from a local file and embed it into the vector database.\n\nSelect a Database and a Collection, or create new ones. \n\nClick  **Run component** on the **Astra DB** component to load your data.\n\n\n### Next steps:\n Experiment by changing the prompt and the contextual data to see how the retrieval flow's responses change.", "display_name": "", "documentation": "", "template": {"backgroundColor": "neutral"}}, "type": "note"}, "dragging": false, "height": 460, "id": "note-i07aq", "measured": {"height": 460, "width": 340}, "position": {"x": 913.9906853654297, "y": 1523.8879126168624}, "positionAbsolute": {"x": 955.3277857006676, "y": 1552.171191793604}, "resizing": false, "selected": false, "style": {"height": 324, "width": 324}, "type": "noteNode", "width": 340}, {"data": {"id": "OpenAIEmbeddings-oFtHy", "node": {"base_classes": ["Embeddings"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Generate embeddings using OpenAI models.", "display_name": "OpenAI Embeddings", "documentation": "", "edited": false, "field_order": ["default_headers", "default_query", "chunk_size", "client", "deployment", "embedding_ctx_length", "max_retries", "model", "model_kwargs", "openai_api_key", "openai_api_base", "openai_api_type", "openai_api_version", "openai_organization", "openai_proxy", "request_timeout", "show_progress_bar", "skip_empty", "tiktoken_model_name", "tiktoken_enable", "dimensions"], "frozen": false, "icon": "OpenAI", "legacy": false, "lf_version": "1.1.1", "metadata": {"code_hash": "2691dee277c9", "module": "langflow.components.openai.openai.OpenAIEmbeddingsComponent"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Embedding Model", "group_outputs": false, "method": "build_embeddings", "name": "embeddings", "selected": "Embeddings", "tool_mode": true, "types": ["Embeddings"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "chunk_size": {"_input_type": "IntInput", "advanced": true, "display_name": "Chunk Size", "dynamic": false, "info": "", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "client": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Client", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "client", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import OpenAIEmbeddings\n\nfrom langflow.base.embeddings.model import LCEmbeddingsModel\nfrom langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES\nfrom langflow.field_typing import Embeddings\nfrom langflow.io import BoolInput, DictInput, DropdownInput, FloatInput, IntInput, MessageTextInput, SecretStrInput\n\n\nclass OpenAIEmbeddingsComponent(LCEmbeddingsModel):\n    display_name = \"OpenAI Embeddings\"\n    description = \"Generate embeddings using OpenAI models.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIEmbeddings\"\n\n    inputs = [\n        DictInput(\n            name=\"default_headers\",\n            display_name=\"Default Headers\",\n            advanced=True,\n            info=\"Default headers to use for the API request.\",\n        ),\n        DictInput(\n            name=\"default_query\",\n            display_name=\"Default Query\",\n            advanced=True,\n            info=\"Default query parameters to use for the API request.\",\n        ),\n        IntInput(name=\"chunk_size\", display_name=\"Chunk Size\", advanced=True, value=1000),\n        MessageTextInput(name=\"client\", display_name=\"Client\", advanced=True),\n        MessageTextInput(name=\"deployment\", display_name=\"Deployment\", advanced=True),\n        IntInput(name=\"embedding_ctx_length\", display_name=\"Embedding Context Length\", advanced=True, value=1536),\n        IntInput(name=\"max_retries\", display_name=\"Max Retries\", value=3, advanced=True),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"Model\",\n            advanced=False,\n            options=OPENAI_EMBEDDING_MODEL_NAMES,\n            value=\"text-embedding-3-small\",\n        ),\n        DictInput(name=\"model_kwargs\", display_name=\"Model Kwargs\", advanced=True),\n        SecretStrInput(name=\"openai_api_key\", display_name=\"OpenAI API Key\", value=\"OPENAI_API_KEY\", required=True),\n        MessageTextInput(name=\"openai_api_base\", display_name=\"OpenAI API Base\", advanced=True),\n        MessageTextInput(name=\"openai_api_type\", display_name=\"OpenAI API Type\", advanced=True),\n        MessageTextInput(name=\"openai_api_version\", display_name=\"OpenAI API Version\", advanced=True),\n        MessageTextInput(\n            name=\"openai_organization\",\n            display_name=\"OpenAI Organization\",\n            advanced=True,\n        ),\n        MessageTextInput(name=\"openai_proxy\", display_name=\"OpenAI Proxy\", advanced=True),\n        FloatInput(name=\"request_timeout\", display_name=\"Request Timeout\", advanced=True),\n        BoolInput(name=\"show_progress_bar\", display_name=\"Show Progress Bar\", advanced=True),\n        BoolInput(name=\"skip_empty\", display_name=\"Skip Empty\", advanced=True),\n        MessageTextInput(\n            name=\"tiktoken_model_name\",\n            display_name=\"TikToken Model Name\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"tiktoken_enable\",\n            display_name=\"TikToken Enable\",\n            advanced=True,\n            value=True,\n            info=\"If False, you must have transformers installed.\",\n        ),\n        IntInput(\n            name=\"dimensions\",\n            display_name=\"Dimensions\",\n            info=\"The number of dimensions the resulting output embeddings should have. \"\n            \"Only supported by certain models.\",\n            advanced=True,\n        ),\n    ]\n\n    def build_embeddings(self) -> Embeddings:\n        return OpenAIEmbeddings(\n            client=self.client or None,\n            model=self.model,\n            dimensions=self.dimensions or None,\n            deployment=self.deployment or None,\n            api_version=self.openai_api_version or None,\n            base_url=self.openai_api_base or None,\n            openai_api_type=self.openai_api_type or None,\n            openai_proxy=self.openai_proxy or None,\n            embedding_ctx_length=self.embedding_ctx_length,\n            api_key=self.openai_api_key or None,\n            organization=self.openai_organization or None,\n            allowed_special=\"all\",\n            disallowed_special=\"all\",\n            chunk_size=self.chunk_size,\n            max_retries=self.max_retries,\n            timeout=self.request_timeout or None,\n            tiktoken_enabled=self.tiktoken_enable,\n            tiktoken_model_name=self.tiktoken_model_name or None,\n            show_progress_bar=self.show_progress_bar,\n            model_kwargs=self.model_kwargs,\n            skip_empty=self.skip_empty,\n            default_headers=self.default_headers or None,\n            default_query=self.default_query or None,\n        )\n"}, "default_headers": {"_input_type": "DictInput", "advanced": true, "display_name": "De<PERSON>ult Head<PERSON>", "dynamic": false, "info": "Default headers to use for the API request.", "list": false, "name": "default_headers", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "default_query": {"_input_type": "DictInput", "advanced": true, "display_name": "De<PERSON><PERSON>", "dynamic": false, "info": "Default query parameters to use for the API request.", "list": false, "name": "default_query", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "deployment": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Deployment", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "deployment", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "dimensions": {"_input_type": "IntInput", "advanced": true, "display_name": "Dimensions", "dynamic": false, "info": "The number of dimensions the resulting output embeddings should have. Only supported by certain models.", "list": false, "name": "dimensions", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": ""}, "embedding_ctx_length": {"_input_type": "IntInput", "advanced": true, "display_name": "Embedding Context Length", "dynamic": false, "info": "", "list": false, "name": "embedding_ctx_length", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1536}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Retries", "dynamic": false, "info": "", "list": false, "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 3}, "model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "display_name": "Model", "dynamic": false, "info": "", "name": "model", "options": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text-embedding-3-small"}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "Model Kwargs", "dynamic": false, "info": "", "list": false, "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "openai_api_base": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API Base", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "", "input_types": [], "load_from_db": true, "name": "openai_api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "openai_api_type": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API Type", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_type", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_version": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API Version", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_version", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_organization": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI Organization", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_organization", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_proxy": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI Proxy", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_proxy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "request_timeout": {"_input_type": "FloatInput", "advanced": true, "display_name": "Request Timeout", "dynamic": false, "info": "", "list": false, "name": "request_timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "float", "value": ""}, "show_progress_bar": {"_input_type": "BoolInput", "advanced": true, "display_name": "Show Progress Bar", "dynamic": false, "info": "", "list": false, "name": "show_progress_bar", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "skip_empty": {"_input_type": "BoolInput", "advanced": true, "display_name": "Skip Empty", "dynamic": false, "info": "", "list": false, "name": "skip_empty", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "tiktoken_enable": {"_input_type": "BoolInput", "advanced": true, "display_name": "TikToken Enable", "dynamic": false, "info": "If False, you must have transformers installed.", "list": false, "name": "tiktoken_enable", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "tiktoken_model_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "TikToken Model Name", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tiktoken_model_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "selected_output": "embeddings", "type": "OpenAIEmbeddings"}, "dragging": false, "height": 320, "id": "OpenAIEmbeddings-oFtHy", "measured": {"height": 320, "width": 320}, "position": {"x": 1690.9220896443658, "y": 1866.483269483266}, "positionAbsolute": {"x": 1690.9220896443658, "y": 1866.483269483266}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-YhXtV", "node": {"description": "### 💡 Add your OpenAI API key here 👇", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-YhXtV", "measured": {"height": 324, "width": 324}, "position": {"x": 1692.2322233423606, "y": 1821.9077961087607}, "positionAbsolute": {"x": 1692.2322233423606, "y": 1821.9077961087607}, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "note-AdydJ", "node": {"description": "### 💡 Add your OpenAI API key here 👇", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-AdydJ", "measured": {"height": 324, "width": 324}, "position": {"x": 824.1003268813427, "y": 698.6951695764802}, "positionAbsolute": {"x": 824.1003268813427, "y": 698.6951695764802}, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "note-aBKhj", "node": {"description": "### 💡 Add your OpenAI API key here 👇", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-aBKhj", "measured": {"height": 324, "width": 324}, "position": {"x": 2350.297636215281, "y": 577.4592910079571}, "positionAbsolute": {"x": 2350.297636215281, "y": 525.0687902842766}, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "parser-WUXPk", "node": {"base_classes": ["Message"], "beta": false, "category": "processing", "conditional_paths": [], "custom_fields": {}, "description": "Format a DataFrame or Data object into text using a template. Enable 'Stringify' to convert input into a readable string instead.", "display_name": "<PERSON><PERSON><PERSON>", "documentation": "", "edited": false, "field_order": ["mode", "pattern", "input_data", "sep"], "frozen": false, "icon": "braces", "key": "parser", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Parsed Text", "hidden": false, "method": "parse_combined_text", "name": "parsed_text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 2.220446049250313e-16, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import json\nfrom typing import Any\n\nfrom langflow.custom import Component\nfrom langflow.io import (\n    BoolInput,\n    HandleInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n    TabInput,\n)\nfrom langflow.schema import Data, DataFrame\nfrom langflow.schema.message import Message\n\n\nclass ParserComponent(Component):\n    name = \"parser\"\n    display_name = \"Parser\"\n    description = (\n        \"Format a DataFrame or Data object into text using a template. \"\n        \"Enable 'Stringify' to convert input into a readable string instead.\"\n    )\n    icon = \"braces\"\n\n    inputs = [\n        TabInput(\n            name=\"mode\",\n            display_name=\"Mode\",\n            options=[\"Parser\", \"Stringify\"],\n            value=\"Parser\",\n            info=\"Convert into raw string instead of using a template.\",\n            real_time_refresh=True,\n        ),\n        MultilineInput(\n            name=\"pattern\",\n            display_name=\"Template\",\n            info=(\n                \"Use variables within curly brackets to extract column values for DataFrames \"\n                \"or key values for Data.\"\n                \"For example: `Name: {Name}, Age: {Age}, Country: {Country}`\"\n            ),\n            value=\"Text: {text}\",  # Example default\n            dynamic=True,\n            show=True,\n            required=True,\n        ),\n        HandleInput(\n            name=\"input_data\",\n            display_name=\"Data or DataFrame\",\n            input_types=[\"DataFrame\", \"Data\"],\n            info=\"Accepts either a DataFrame or a Data object.\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"sep\",\n            display_name=\"Separator\",\n            advanced=True,\n            value=\"\\n\",\n            info=\"String used to separate rows/items.\",\n        ),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"Parsed Text\",\n            name=\"parsed_text\",\n            info=\"Formatted text output.\",\n            method=\"parse_combined_text\",\n        ),\n    ]\n\n    def update_build_config(self, build_config, field_value, field_name=None):\n        \"\"\"Dynamically hide/show `template` and enforce requirement based on `stringify`.\"\"\"\n        if field_name == \"mode\":\n            build_config[\"pattern\"][\"show\"] = self.mode == \"Parser\"\n            build_config[\"pattern\"][\"required\"] = self.mode == \"Parser\"\n            if field_value:\n                clean_data = BoolInput(\n                    name=\"clean_data\",\n                    display_name=\"Clean Data\",\n                    info=(\n                        \"Enable to clean the data by removing empty rows and lines \"\n                        \"in each cell of the DataFrame/ Data object.\"\n                    ),\n                    value=True,\n                    advanced=True,\n                    required=False,\n                )\n                build_config[\"clean_data\"] = clean_data.to_dict()\n            else:\n                build_config.pop(\"clean_data\", None)\n\n        return build_config\n\n    def _clean_args(self):\n        \"\"\"Prepare arguments based on input type.\"\"\"\n        input_data = self.input_data\n\n        match input_data:\n            case list() if all(isinstance(item, Data) for item in input_data):\n                msg = \"List of Data objects is not supported.\"\n                raise ValueError(msg)\n            case DataFrame():\n                return input_data, None\n            case Data():\n                return None, input_data\n            case dict() if \"data\" in input_data:\n                try:\n                    if \"columns\" in input_data:  # Likely a DataFrame\n                        return DataFrame.from_dict(input_data), None\n                    # Likely a Data object\n                    return None, Data(**input_data)\n                except (TypeError, ValueError, KeyError) as e:\n                    msg = f\"Invalid structured input provided: {e!s}\"\n                    raise ValueError(msg) from e\n            case _:\n                msg = f\"Unsupported input type: {type(input_data)}. Expected DataFrame or Data.\"\n                raise ValueError(msg)\n\n    def parse_combined_text(self) -> Message:\n        \"\"\"Parse all rows/items into a single text or convert input to string if `stringify` is enabled.\"\"\"\n        # Early return for stringify option\n        if self.mode == \"Stringify\":\n            return self.convert_to_string()\n\n        df, data = self._clean_args()\n\n        lines = []\n        if df is not None:\n            for _, row in df.iterrows():\n                formatted_text = self.pattern.format(**row.to_dict())\n                lines.append(formatted_text)\n        elif data is not None:\n            formatted_text = self.pattern.format(**data.data)\n            lines.append(formatted_text)\n\n        combined_text = self.sep.join(lines)\n        self.status = combined_text\n        return Message(text=combined_text)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                return json.dumps(data.data)\n            if isinstance(data, DataFrame):\n                if hasattr(self, \"clean_data\") and self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n                return data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> Message:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        result = \"\"\n        if isinstance(self.input_data, list):\n            result = \"\\n\".join([self._safe_convert(item) for item in self.input_data])\n        else:\n            result = self._safe_convert(self.input_data)\n        self.log(f\"Converted to string with length: {len(result)}\")\n\n        message = Message(text=result)\n        self.status = message\n        return message\n"}, "input_data": {"_input_type": "HandleInput", "advanced": false, "display_name": "Data or DataFrame", "dynamic": false, "info": "Accepts either a DataFrame or a Data object.", "input_types": ["DataFrame", "Data"], "list": false, "list_add_label": "Add More", "name": "input_data", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "mode": {"_input_type": "TabInput", "advanced": false, "display_name": "Mode", "dynamic": false, "info": "Convert into raw string instead of using a template.", "name": "mode", "options": ["<PERSON><PERSON><PERSON>", "Stringify"], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "tab", "value": "<PERSON><PERSON><PERSON>"}, "pattern": {"_input_type": "MultilineInput", "advanced": false, "copy_field": false, "display_name": "Template", "dynamic": true, "info": "Use variables within curly brackets to extract column values for DataFrames or key values for Data.For example: `Name: {Name}, Age: {Age}, Country: {Country}`", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "pattern", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "Text: {text}"}, "sep": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Separator", "dynamic": false, "info": "String used to separate rows/items.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "\n"}}, "tool_mode": false}, "selected_output": "parsed_text", "showNode": true, "type": "parser"}, "dragging": false, "id": "parser-WUXPk", "measured": {"height": 361, "width": 320}, "position": {"x": 1583.5982144641368, "y": 651.635660385082}, "selected": false, "type": "genericNode"}, {"data": {"id": "AstraDB-JsRrT", "node": {"base_classes": ["Data", "DataFrame", "VectorStore"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Ingest and search documents in Astra DB", "display_name": "Astra DB", "documentation": "https://docs.datastax.com/en/langflow/astra-components.html", "edited": false, "field_order": ["token", "environment", "database_name", "api_endpoint", "keyspace", "collection_name", "embedding_model", "ingest_data", "search_query", "should_cache_vector_store", "search_method", "reranker", "lexical_terms", "number_of_results", "search_type", "search_score_threshold", "advanced_search_filter", "autodetect_collection", "content_field", "deletion_field", "ignore_invalid_documents", "astradb_vectorstore_kwargs"], "frozen": false, "icon": "AstraDB", "legacy": false, "metadata": {"code_hash": "38a337e89ff4", "module": "langflow.components.vectorstores.astradb.AstraDBVectorStoreComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Search Results", "group_outputs": false, "method": "search_documents", "name": "search_results", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "DataFrame", "group_outputs": false, "method": "as_dataframe", "name": "dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Vector Store Connection", "group_outputs": false, "hidden": true, "method": "as_vector_store", "name": "vectorstoreconnection", "selected": "VectorStore", "tool_mode": true, "types": ["VectorStore"], "value": "__UNDEFINED__"}], "pinned": false, "selected_output": "dataframe", "template": {"_type": "Component", "advanced_search_filter": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "Search Metadata Filter", "dynamic": false, "info": "Optional dictionary of filters to apply to the search query.", "list": false, "list_add_label": "Add More", "name": "advanced_search_filter", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "api_endpoint": {"_input_type": "StrInput", "advanced": false, "display_name": "Astra DB API Endpoint", "dynamic": false, "info": "The API Endpoint for the Astra DB instance. Supercedes database selection.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "api_endpoint", "placeholder": "", "required": false, "show": false, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "astradb_vectorstore_kwargs": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "AstraDBVectorStore Parameters", "dynamic": false, "info": "Optional dictionary of additional parameters for the AstraDBVectorStore.", "list": false, "list_add_label": "Add More", "name": "astradb_vectorstore_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "autodetect_collection": {"_input_type": "BoolInput", "advanced": true, "display_name": "Autodetect Collection", "dynamic": false, "info": "Boolean flag to determine whether to autodetect the collection.", "list": false, "list_add_label": "Add More", "name": "autodetect_collection", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import re\nfrom collections import defaultdict\nfrom dataclasses import asdict, dataclass, field\n\nfrom astrapy import DataAPIClient, Database\nfrom astrapy.data.info.reranking import RerankServiceOptions\nfrom astrapy.info import CollectionDescriptor, CollectionLexicalOptions, CollectionRerankOptions\nfrom langchain_astradb import AstraDBVectorStore, VectorServiceOptions\nfrom langchain_astradb.utils.astradb import HybridSearchMode, _AstraDBCollectionEnvironment\nfrom langchain_core.documents import Document\n\nfrom langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store\nfrom langflow.base.vectorstores.vector_store_connection_decorator import vector_store_connection\nfrom langflow.helpers.data import docs_to_data\nfrom langflow.inputs.inputs import FloatInput, NestedDictInput\nfrom langflow.io import (\n    BoolInput,\n    DropdownInput,\n    HandleInput,\n    IntInput,\n    QueryInput,\n    SecretStrInput,\n    StrInput,\n)\nfrom langflow.schema.data import Data\nfrom langflow.serialization import serialize\nfrom langflow.utils.version import get_version_info\n\n\n@vector_store_connection\nclass AstraDBVectorStoreComponent(LCVectorStoreComponent):\n    display_name: str = \"Astra DB\"\n    description: str = \"Ingest and search documents in Astra DB\"\n    documentation: str = \"https://docs.datastax.com/en/langflow/astra-components.html\"\n    name = \"AstraDB\"\n    icon: str = \"AstraDB\"\n\n    _cached_vector_store: AstraDBVectorStore | None = None\n\n    @dataclass\n    class NewDatabaseInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_database\",\n                        \"description\": \"Please allow several minutes for creation to complete.\",\n                        \"display_name\": \"Create new database\",\n                        \"field_order\": [\"01_new_database_name\", \"02_cloud_provider\", \"03_region\"],\n                        \"template\": {\n                            \"01_new_database_name\": StrInput(\n                                name=\"new_database_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new database to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_cloud_provider\": DropdownInput(\n                                name=\"cloud_provider\",\n                                display_name=\"Cloud provider\",\n                                info=\"Cloud provider for the new database.\",\n                                options=[],\n                                required=True,\n                                real_time_refresh=True,\n                            ),\n                            \"03_region\": DropdownInput(\n                                name=\"region\",\n                                display_name=\"Region\",\n                                info=\"Region for the new database.\",\n                                options=[],\n                                required=True,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    @dataclass\n    class NewCollectionInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_collection\",\n                        \"description\": \"Please allow several seconds for creation to complete.\",\n                        \"display_name\": \"Create new collection\",\n                        \"field_order\": [\n                            \"01_new_collection_name\",\n                            \"02_embedding_generation_provider\",\n                            \"03_embedding_generation_model\",\n                            \"04_dimension\",\n                        ],\n                        \"template\": {\n                            \"01_new_collection_name\": StrInput(\n                                name=\"new_collection_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new collection to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_embedding_generation_provider\": DropdownInput(\n                                name=\"embedding_generation_provider\",\n                                display_name=\"Embedding generation method\",\n                                info=\"Provider to use for generating embeddings.\",\n                                helper_text=(\n                                    \"To create collections with more embedding provider options, go to \"\n                                    '<a class=\"underline\" href=\"https://astra.datastax.com/\" target=\" _blank\" '\n                                    'rel=\"noopener noreferrer\">your database in Astra DB</a>'\n                                ),\n                                real_time_refresh=True,\n                                required=True,\n                                options=[],\n                            ),\n                            \"03_embedding_generation_model\": DropdownInput(\n                                name=\"embedding_generation_model\",\n                                display_name=\"Embedding model\",\n                                info=\"Model to use for generating embeddings.\",\n                                real_time_refresh=True,\n                                options=[],\n                            ),\n                            \"04_dimension\": IntInput(\n                                name=\"dimension\",\n                                display_name=\"Dimensions\",\n                                info=\"Dimensions of the embeddings to generate.\",\n                                value=None,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    inputs = [\n        SecretStrInput(\n            name=\"token\",\n            display_name=\"Astra DB Application Token\",\n            info=\"Authentication token for accessing Astra DB.\",\n            value=\"ASTRA_DB_APPLICATION_TOKEN\",\n            required=True,\n            real_time_refresh=True,\n            input_types=[],\n        ),\n        DropdownInput(\n            name=\"environment\",\n            display_name=\"Environment\",\n            info=\"The environment for the Astra DB API Endpoint.\",\n            options=[\"prod\", \"test\", \"dev\"],\n            value=\"prod\",\n            advanced=True,\n            real_time_refresh=True,\n            combobox=True,\n        ),\n        DropdownInput(\n            name=\"database_name\",\n            display_name=\"Database\",\n            info=\"The Database name for the Astra DB instance.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewDatabaseInput()),\n            combobox=True,\n        ),\n        StrInput(\n            name=\"api_endpoint\",\n            display_name=\"Astra DB API Endpoint\",\n            info=\"The API Endpoint for the Astra DB instance. Supercedes database selection.\",\n            show=False,\n        ),\n        DropdownInput(\n            name=\"keyspace\",\n            display_name=\"Keyspace\",\n            info=\"Optional keyspace within Astra DB to use for the collection.\",\n            advanced=True,\n            options=[],\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"collection_name\",\n            display_name=\"Collection\",\n            info=\"The name of the collection within Astra DB where the vectors will be stored.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewCollectionInput()),\n            combobox=True,\n            show=False,\n        ),\n        HandleInput(\n            name=\"embedding_model\",\n            display_name=\"Embedding Model\",\n            input_types=[\"Embeddings\"],\n            info=\"Specify the Embedding Model. Not required for Astra Vectorize collections.\",\n            required=False,\n            show=False,\n        ),\n        *LCVectorStoreComponent.inputs,\n        DropdownInput(\n            name=\"search_method\",\n            display_name=\"Search Method\",\n            info=(\n                \"Determine how your content is matched: Vector finds semantic similarity, \"\n                \"and Hybrid Search (suggested) combines both approaches \"\n                \"with a reranker.\"\n            ),\n            options=[\"Hybrid Search\", \"Vector Search\"],  # TODO: Restore Lexical Search?\n            options_metadata=[{\"icon\": \"SearchHybrid\"}, {\"icon\": \"SearchVector\"}],\n            value=\"Vector Search\",\n            advanced=True,\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"reranker\",\n            display_name=\"Reranker\",\n            info=\"Post-retrieval model that re-scores results for optimal relevance ranking.\",\n            show=False,\n            toggle=True,\n        ),\n        QueryInput(\n            name=\"lexical_terms\",\n            display_name=\"Lexical Terms\",\n            info=\"Add additional terms/keywords to augment search precision.\",\n            placeholder=\"Enter terms to search...\",\n            separator=\" \",\n            show=False,\n            value=\"\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"number_of_results\",\n            display_name=\"Number of Search Results\",\n            info=\"Number of search results to return.\",\n            advanced=True,\n            value=4,\n        ),\n        DropdownInput(\n            name=\"search_type\",\n            display_name=\"Search Type\",\n            info=\"Search type to use\",\n            options=[\"Similarity\", \"Similarity with score threshold\", \"MMR (Max Marginal Relevance)\"],\n            value=\"Similarity\",\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"search_score_threshold\",\n            display_name=\"Search Score Threshold\",\n            info=\"Minimum similarity score threshold for search results. \"\n            \"(when using 'Similarity with score threshold')\",\n            value=0,\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"advanced_search_filter\",\n            display_name=\"Search Metadata Filter\",\n            info=\"Optional dictionary of filters to apply to the search query.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"autodetect_collection\",\n            display_name=\"Autodetect Collection\",\n            info=\"Boolean flag to determine whether to autodetect the collection.\",\n            advanced=True,\n            value=True,\n        ),\n        StrInput(\n            name=\"content_field\",\n            display_name=\"Content Field\",\n            info=\"Field to use as the text content field for the vector store.\",\n            advanced=True,\n        ),\n        StrInput(\n            name=\"deletion_field\",\n            display_name=\"Deletion Based On Field\",\n            info=\"When this parameter is provided, documents in the target collection with \"\n            \"metadata field values matching the input metadata field value will be deleted \"\n            \"before new data is loaded.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"ignore_invalid_documents\",\n            display_name=\"Ignore Invalid Documents\",\n            info=\"Boolean flag to determine whether to ignore invalid documents at runtime.\",\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"astradb_vectorstore_kwargs\",\n            display_name=\"AstraDBVectorStore Parameters\",\n            info=\"Optional dictionary of additional parameters for the AstraDBVectorStore.\",\n            advanced=True,\n        ),\n    ]\n\n    @classmethod\n    def map_cloud_providers(cls):\n        # TODO: Programmatically fetch the regions for each cloud provider\n        return {\n            \"dev\": {\n                \"Amazon Web Services\": {\n                    \"id\": \"aws\",\n                    \"regions\": [\"us-west-2\"],\n                },\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\", \"europe-west4\"],\n                },\n            },\n            \"test\": {\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\"],\n                },\n            },\n            \"prod\": {\n                \"Amazon Web Services\": {\n                    \"id\": \"aws\",\n                    \"regions\": [\"us-east-2\", \"ap-south-1\", \"eu-west-1\"],\n                },\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-east1\"],\n                },\n                \"Microsoft Azure\": {\n                    \"id\": \"azure\",\n                    \"regions\": [\"westus3\"],\n                },\n            },\n        }\n\n    @classmethod\n    def get_vectorize_providers(cls, token: str, environment: str | None = None, api_endpoint: str | None = None):\n        try:\n            # Get the admin object\n            client = DataAPIClient(environment=environment)\n            admin_client = client.get_admin()\n            db_admin = admin_client.get_database_admin(api_endpoint, token=token)\n\n            # Get the list of embedding providers\n            embedding_providers = db_admin.find_embedding_providers()\n\n            vectorize_providers_mapping = {}\n            # Map the provider display name to the provider key and models\n            for provider_key, provider_data in embedding_providers.embedding_providers.items():\n                # Get the provider display name and models\n                display_name = provider_data.display_name\n                models = [model.name for model in provider_data.models]\n\n                # Build our mapping\n                vectorize_providers_mapping[display_name] = [provider_key, models]\n\n            # Sort the resulting dictionary\n            return defaultdict(list, dict(sorted(vectorize_providers_mapping.items())))\n        except Exception as _:  # noqa: BLE001\n            return {}\n\n    @classmethod\n    async def create_database_api(\n        cls,\n        new_database_name: str,\n        cloud_provider: str,\n        region: str,\n        token: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n    ):\n        client = DataAPIClient(environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the environment, set to prod if null like\n        my_env = environment or \"prod\"\n\n        # Raise a value error if name isn't provided\n        if not new_database_name:\n            msg = \"Database name is required to create a new database.\"\n            raise ValueError(msg)\n\n        # Call the create database function\n        return await admin_client.async_create_database(\n            name=new_database_name,\n            cloud_provider=cls.map_cloud_providers()[my_env][cloud_provider][\"id\"],\n            region=region,\n            keyspace=keyspace,\n            wait_until_active=False,\n        )\n\n    @classmethod\n    async def create_collection_api(\n        cls,\n        new_collection_name: str,\n        token: str,\n        api_endpoint: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n        dimension: int | None = None,\n        embedding_generation_provider: str | None = None,\n        embedding_generation_model: str | None = None,\n        reranker: str | None = None,\n    ):\n        # Build vectorize options, if needed\n        vectorize_options = None\n        if not dimension:\n            providers = cls.get_vectorize_providers(token=token, environment=environment, api_endpoint=api_endpoint)\n            vectorize_options = VectorServiceOptions(\n                provider=providers.get(embedding_generation_provider, [None, []])[0],\n                model_name=embedding_generation_model,\n            )\n\n        # Raise a value error if name isn't provided\n        if not new_collection_name:\n            msg = \"Collection name is required to create a new collection.\"\n            raise ValueError(msg)\n\n        # Define the base arguments being passed to the create collection function\n        base_args = {\n            \"collection_name\": new_collection_name,\n            \"token\": token,\n            \"api_endpoint\": api_endpoint,\n            \"keyspace\": keyspace,\n            \"environment\": environment,\n            \"embedding_dimension\": dimension,\n            \"collection_vector_service_options\": vectorize_options,\n        }\n\n        # Add optional arguments if the reranker is set\n        if reranker:\n            # Split the reranker field into a provider a model name\n            provider, _ = reranker.split(\"/\")\n            base_args[\"collection_rerank\"] = CollectionRerankOptions(\n                service=RerankServiceOptions(provider=provider, model_name=reranker),\n            )\n            base_args[\"collection_lexical\"] = CollectionLexicalOptions(analyzer=\"STANDARD\")\n\n        _AstraDBCollectionEnvironment(**base_args)\n\n    @classmethod\n    def get_database_list_static(cls, token: str, environment: str | None = None):\n        client = DataAPIClient(environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the list of databases\n        db_list = admin_client.list_databases()\n\n        # Generate the api endpoint for each database\n        db_info_dict = {}\n        for db in db_list:\n            try:\n                # Get the API endpoint for the database\n                api_endpoint = db.regions[0].api_endpoint\n\n                # Get the number of collections\n                try:\n                    # Get the number of collections in the database\n                    num_collections = len(\n                        client.get_database(\n                            api_endpoint,\n                            token=token,\n                        ).list_collection_names()\n                    )\n                except Exception:  # noqa: BLE001\n                    if db.status != \"PENDING\":\n                        continue\n                    num_collections = 0\n\n                # Add the database to the dictionary\n                db_info_dict[db.name] = {\n                    \"api_endpoint\": api_endpoint,\n                    \"keyspaces\": db.keyspaces,\n                    \"collections\": num_collections,\n                    \"status\": db.status if db.status != \"ACTIVE\" else None,\n                    \"org_id\": db.org_id if db.org_id else None,\n                }\n            except Exception:  # noqa: BLE001, S110\n                pass\n\n        return db_info_dict\n\n    def get_database_list(self):\n        return self.get_database_list_static(\n            token=self.token,\n            environment=self.environment,\n        )\n\n    @classmethod\n    def get_api_endpoint_static(\n        cls,\n        token: str,\n        environment: str | None = None,\n        api_endpoint: str | None = None,\n        database_name: str | None = None,\n    ):\n        # If the api_endpoint is set, return it\n        if api_endpoint:\n            return api_endpoint\n\n        # Check if the database_name is like a url\n        if database_name and database_name.startswith(\"https://\"):\n            return database_name\n\n        # If the database is not set, nothing we can do.\n        if not database_name:\n            return None\n\n        # Grab the database object\n        db = cls.get_database_list_static(token=token, environment=environment).get(database_name)\n        if not db:\n            return None\n\n        # Otherwise, get the URL from the database list\n        return db.get(\"api_endpoint\")\n\n    def get_api_endpoint(self):\n        return self.get_api_endpoint_static(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=self.api_endpoint,\n            database_name=self.database_name,\n        )\n\n    @classmethod\n    def get_database_id_static(cls, api_endpoint: str) -> str | None:\n        # Pattern matches standard UUID format: 8-4-4-4-12 hexadecimal characters\n        uuid_pattern = r\"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\"\n        match = re.search(uuid_pattern, api_endpoint)\n\n        return match.group(0) if match else None\n\n    def get_database_id(self):\n        return self.get_database_id_static(api_endpoint=self.get_api_endpoint())\n\n    def get_keyspace(self):\n        keyspace = self.keyspace\n\n        if keyspace:\n            return keyspace.strip()\n\n        return \"default_keyspace\"\n\n    def get_database_object(self, api_endpoint: str | None = None):\n        try:\n            client = DataAPIClient(environment=self.environment)\n\n            return client.get_database(\n                api_endpoint or self.get_api_endpoint(),\n                token=self.token,\n                keyspace=self.get_keyspace(),\n            )\n        except Exception as e:\n            msg = f\"Error fetching database object: {e}\"\n            raise ValueError(msg) from e\n\n    def collection_data(self, collection_name: str, database: Database | None = None):\n        try:\n            if not database:\n                client = DataAPIClient(environment=self.environment)\n\n                database = client.get_database(\n                    self.get_api_endpoint(),\n                    token=self.token,\n                    keyspace=self.get_keyspace(),\n                )\n\n            collection = database.get_collection(collection_name)\n\n            return collection.estimated_document_count()\n        except Exception as e:  # noqa: BLE001\n            self.log(f\"Error checking collection data: {e}\")\n\n            return None\n\n    def _initialize_database_options(self):\n        try:\n            return [\n                {\n                    \"name\": name,\n                    \"status\": info[\"status\"],\n                    \"collections\": info[\"collections\"],\n                    \"api_endpoint\": info[\"api_endpoint\"],\n                    \"keyspaces\": info[\"keyspaces\"],\n                    \"org_id\": info[\"org_id\"],\n                }\n                for name, info in self.get_database_list().items()\n            ]\n        except Exception as e:\n            msg = f\"Error fetching database options: {e}\"\n            raise ValueError(msg) from e\n\n    @classmethod\n    def get_provider_icon(cls, collection: CollectionDescriptor | None = None, provider_name: str | None = None) -> str:\n        # Get the provider name from the collection\n        provider_name = provider_name or (\n            collection.definition.vector.service.provider\n            if (\n                collection\n                and collection.definition\n                and collection.definition.vector\n                and collection.definition.vector.service\n            )\n            else None\n        )\n\n        # If there is no provider, use the vector store icon\n        if not provider_name or provider_name.lower() == \"bring your own\":\n            return \"vectorstores\"\n\n        # Map provider casings\n        case_map = {\n            \"nvidia\": \"NVIDIA\",\n            \"openai\": \"OpenAI\",\n            \"amazon bedrock\": \"AmazonBedrockEmbeddings\",\n            \"azure openai\": \"AzureOpenAiEmbeddings\",\n            \"cohere\": \"Cohere\",\n            \"jina ai\": \"JinaAI\",\n            \"mistral ai\": \"MistralAI\",\n            \"upstage\": \"Upstage\",\n            \"voyage ai\": \"VoyageAI\",\n        }\n\n        # Adjust the casing on some like nvidia\n        return case_map[provider_name.lower()] if provider_name.lower() in case_map else provider_name.title()\n\n    def _initialize_collection_options(self, api_endpoint: str | None = None):\n        # Nothing to generate if we don't have an API endpoint yet\n        api_endpoint = api_endpoint or self.get_api_endpoint()\n        if not api_endpoint:\n            return []\n\n        # Retrieve the database object\n        database = self.get_database_object(api_endpoint=api_endpoint)\n\n        # Get the list of collections\n        collection_list = database.list_collections(keyspace=self.get_keyspace())\n\n        # Return the list of collections and metadata associated\n        return [\n            {\n                \"name\": col.name,\n                \"records\": self.collection_data(collection_name=col.name, database=database),\n                \"provider\": (\n                    col.definition.vector.service.provider\n                    if col.definition.vector and col.definition.vector.service\n                    else None\n                ),\n                \"icon\": self.get_provider_icon(collection=col),\n                \"model\": (\n                    col.definition.vector.service.model_name\n                    if col.definition.vector and col.definition.vector.service\n                    else None\n                ),\n            }\n            for col in collection_list\n        ]\n\n    def reset_provider_options(self, build_config: dict) -> dict:\n        \"\"\"Reset provider options and related configurations in the build_config dictionary.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get vectorize providers\n        vectorize_providers_api = self.get_vectorize_providers(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n        )\n\n        # Create a new dictionary with \"Bring your own\" first\n        vectorize_providers: dict[str, list[list[str]]] = {\"Bring your own\": [[], []]}\n\n        # Add the remaining items (only Nvidia) from the original dictionary\n        vectorize_providers.update(\n            {\n                k: v\n                for k, v in vectorize_providers_api.items()\n                if k.lower() in [\"nvidia\"]  # TODO: Eventually support more\n            }\n        )\n\n        # Set provider options\n        provider_field = \"02_embedding_generation_provider\"\n        template[provider_field][\"options\"] = list(vectorize_providers.keys())\n\n        # Add metadata for each provider option\n        template[provider_field][\"options_metadata\"] = [\n            {\"icon\": self.get_provider_icon(provider_name=provider)} for provider in template[provider_field][\"options\"]\n        ]\n\n        # Get selected embedding provider\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure embedding model field\n        model_field = \"03_embedding_generation_model\"\n        template[model_field].update(\n            {\n                \"options\": vectorize_providers.get(embedding_provider, [[], []])[1],\n                \"placeholder\": \"Bring your own\" if is_bring_your_own else None,\n                \"readonly\": is_bring_your_own,\n                \"required\": not is_bring_your_own,\n                \"value\": None,\n            }\n        )\n\n        # If this is a bring your own, set dimensions to 0\n        return self.reset_dimension_field(build_config)\n\n    def reset_dimension_field(self, build_config: dict) -> dict:\n        \"\"\"Reset dimension field options based on provided configuration.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get selected embedding model\n        provider_field = \"02_embedding_generation_provider\"\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure dimension field\n        dimension_field = \"04_dimension\"\n        dimension_value = 1024 if not is_bring_your_own else None  # TODO: Dynamically figure this out\n        template[dimension_field].update(\n            {\n                \"placeholder\": dimension_value,\n                \"value\": dimension_value,\n                \"readonly\": not is_bring_your_own,\n                \"required\": is_bring_your_own,\n            }\n        )\n\n        return build_config\n\n    def reset_collection_list(self, build_config: dict) -> dict:\n        \"\"\"Reset collection list options based on provided configuration.\"\"\"\n        # Get collection options\n        collection_options = self._initialize_collection_options(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        # Update collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update(\n            {\n                \"options\": [col[\"name\"] for col in collection_options],\n                \"options_metadata\": [{k: v for k, v in col.items() if k != \"name\"} for col in collection_options],\n            }\n        )\n\n        # Reset selected collection if not in options\n        if collection_config[\"value\"] not in collection_config[\"options\"]:\n            collection_config[\"value\"] = \"\"\n\n        # Set advanced status based on database selection\n        collection_config[\"show\"] = bool(build_config[\"database_name\"][\"value\"])\n\n        return build_config\n\n    def reset_database_list(self, build_config: dict) -> dict:\n        \"\"\"Reset database list options and related configurations.\"\"\"\n        # Get database options\n        database_options = self._initialize_database_options()\n\n        # Update cloud provider options\n        env = self.environment\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_cloud_provider\"][\"options\"] = list(self.map_cloud_providers()[env].keys())\n\n        # Update database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update(\n            {\n                \"options\": [db[\"name\"] for db in database_options],\n                \"options_metadata\": [{k: v for k, v in db.items() if k != \"name\"} for db in database_options],\n            }\n        )\n\n        # Reset selections if value not in options\n        if database_config[\"value\"] not in database_config[\"options\"]:\n            database_config[\"value\"] = \"\"\n            build_config[\"api_endpoint\"][\"value\"] = \"\"\n            build_config[\"collection_name\"][\"show\"] = False\n\n        # Set advanced status based on token presence\n        database_config[\"show\"] = bool(build_config[\"token\"][\"value\"])\n\n        return build_config\n\n    def reset_build_config(self, build_config: dict) -> dict:\n        \"\"\"Reset all build configuration options to default empty state.\"\"\"\n        # Reset database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"show\": False})\n        build_config[\"api_endpoint\"][\"value\"] = \"\"\n\n        # Reset collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"show\": False})\n\n        return build_config\n\n    def _handle_hybrid_search_options(self, build_config: dict) -> dict:\n        \"\"\"Set hybrid search options in the build configuration.\"\"\"\n        # Detect what hybrid options are available\n        # Get the admin object\n        client = DataAPIClient(environment=self.environment)\n        admin_client = client.get_admin()\n        db_admin = admin_client.get_database_admin(self.get_api_endpoint(), token=self.token)\n\n        # We will try to get the reranking providers to see if its hybrid emabled\n        try:\n            providers = db_admin.find_reranking_providers()\n            build_config[\"reranker\"][\"options\"] = [\n                model.name for provider_data in providers.reranking_providers.values() for model in provider_data.models\n            ]\n            build_config[\"reranker\"][\"options_metadata\"] = [\n                {\"icon\": self.get_provider_icon(provider_name=model.name.split(\"/\")[0])}\n                for provider in providers.reranking_providers.values()\n                for model in provider.models\n            ]\n            build_config[\"reranker\"][\"value\"] = build_config[\"reranker\"][\"options\"][0]\n\n            # Set the default search field to hybrid search\n            build_config[\"search_method\"][\"show\"] = True\n            build_config[\"search_method\"][\"options\"] = [\"Hybrid Search\", \"Vector Search\"]\n            build_config[\"search_method\"][\"value\"] = \"Hybrid Search\"\n        except Exception as _:  # noqa: BLE001\n            build_config[\"reranker\"][\"options\"] = []\n            build_config[\"reranker\"][\"options_metadata\"] = []\n\n            # Set the default search field to vector search\n            build_config[\"search_method\"][\"show\"] = False\n            build_config[\"search_method\"][\"options\"] = [\"Vector Search\"]\n            build_config[\"search_method\"][\"value\"] = \"Vector Search\"\n\n        # Set reranker and lexical terms options based on search method\n        build_config[\"reranker\"][\"toggle_value\"] = True\n        build_config[\"reranker\"][\"show\"] = build_config[\"search_method\"][\"value\"] == \"Hybrid Search\"\n        build_config[\"reranker\"][\"toggle_disable\"] = build_config[\"search_method\"][\"value\"] == \"Hybrid Search\"\n        if build_config[\"reranker\"][\"show\"]:\n            build_config[\"search_type\"][\"value\"] = \"Similarity\"\n\n        return build_config\n\n    async def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:\n        \"\"\"Update build configuration based on field name and value.\"\"\"\n        # Early return if no token provided\n        if not self.token:\n            return self.reset_build_config(build_config)\n\n        # Database creation callback\n        if field_name == \"database_name\" and isinstance(field_value, dict):\n            if \"01_new_database_name\" in field_value:\n                await self._create_new_database(build_config, field_value)\n                return self.reset_collection_list(build_config)\n            return self._update_cloud_regions(build_config, field_value)\n\n        # Collection creation callback\n        if field_name == \"collection_name\" and isinstance(field_value, dict):\n            # Case 1: New collection creation\n            if \"01_new_collection_name\" in field_value:\n                await self._create_new_collection(build_config, field_value)\n                return build_config\n\n            # Case 2: Update embedding provider options\n            if \"02_embedding_generation_provider\" in field_value:\n                return self.reset_provider_options(build_config)\n\n            # Case 3: Update dimension field\n            if \"03_embedding_generation_model\" in field_value:\n                return self.reset_dimension_field(build_config)\n\n        # Initial execution or token/environment change\n        first_run = field_name == \"collection_name\" and not field_value and not build_config[\"database_name\"][\"options\"]\n        if first_run or field_name in {\"token\", \"environment\"}:\n            return self.reset_database_list(build_config)\n\n        # Database selection change\n        if field_name == \"database_name\" and not isinstance(field_value, dict):\n            return self._handle_database_selection(build_config, field_value)\n\n        # Keyspace selection change\n        if field_name == \"keyspace\":\n            return self.reset_collection_list(build_config)\n\n        # Collection selection change\n        if field_name == \"collection_name\" and not isinstance(field_value, dict):\n            return self._handle_collection_selection(build_config, field_value)\n\n        # Search method selection change\n        if field_name == \"search_method\":\n            is_vector_search = field_value == \"Vector Search\"\n            is_autodetect = build_config[\"autodetect_collection\"][\"value\"]\n\n            # Configure lexical terms (same for both cases)\n            build_config[\"lexical_terms\"][\"show\"] = not is_vector_search\n            build_config[\"lexical_terms\"][\"value\"] = \"\" if is_vector_search else build_config[\"lexical_terms\"][\"value\"]\n\n            # Disable reranker disabling if hybrid search is selected\n            build_config[\"reranker\"][\"toggle_disable\"] = not is_vector_search\n            build_config[\"reranker\"][\"toggle_value\"] = True\n            build_config[\"reranker\"][\"value\"] = build_config[\"reranker\"][\"options\"][0]\n\n            # Toggle search type and score threshold based on search method\n            build_config[\"search_type\"][\"show\"] = is_vector_search\n            build_config[\"search_score_threshold\"][\"show\"] = is_vector_search\n\n            # Make sure the search_type is set to \"Similarity\"\n            if not is_vector_search or is_autodetect:\n                build_config[\"search_type\"][\"value\"] = \"Similarity\"\n\n        return build_config\n\n    async def _create_new_database(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new database and update build config options.\"\"\"\n        try:\n            await self.create_database_api(\n                new_database_name=field_value[\"01_new_database_name\"],\n                token=self.token,\n                keyspace=self.get_keyspace(),\n                environment=self.environment,\n                cloud_provider=field_value[\"02_cloud_provider\"],\n                region=field_value[\"03_region\"],\n            )\n        except Exception as e:\n            msg = f\"Error creating database: {e}\"\n            raise ValueError(msg) from e\n\n        build_config[\"database_name\"][\"options\"].append(field_value[\"01_new_database_name\"])\n        build_config[\"database_name\"][\"options_metadata\"].append(\n            {\n                \"status\": \"PENDING\",\n                \"collections\": 0,\n                \"api_endpoint\": None,\n                \"keyspaces\": [self.get_keyspace()],\n                \"org_id\": None,\n            }\n        )\n\n    def _update_cloud_regions(self, build_config: dict, field_value: dict) -> dict:\n        \"\"\"Update cloud provider regions in build config.\"\"\"\n        env = self.environment\n        cloud_provider = field_value[\"02_cloud_provider\"]\n\n        # Update the region options based on the selected cloud provider\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"03_region\"][\"options\"] = self.map_cloud_providers()[env][cloud_provider][\"regions\"]\n\n        # Reset the the 03_region value if it's not in the new options\n        if template[\"03_region\"][\"value\"] not in template[\"03_region\"][\"options\"]:\n            template[\"03_region\"][\"value\"] = None\n\n        return build_config\n\n    async def _create_new_collection(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new collection and update build config options.\"\"\"\n        embedding_provider = field_value.get(\"02_embedding_generation_provider\")\n        try:\n            await self.create_collection_api(\n                new_collection_name=field_value[\"01_new_collection_name\"],\n                token=self.token,\n                api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n                environment=self.environment,\n                keyspace=self.get_keyspace(),\n                dimension=field_value.get(\"04_dimension\") if embedding_provider == \"Bring your own\" else None,\n                embedding_generation_provider=embedding_provider,\n                embedding_generation_model=field_value.get(\"03_embedding_generation_model\"),\n                reranker=self.reranker,\n            )\n        except Exception as e:\n            msg = f\"Error creating collection: {e}\"\n            raise ValueError(msg) from e\n\n        provider = embedding_provider.lower() if embedding_provider and embedding_provider != \"Bring your own\" else None\n        build_config[\"collection_name\"].update(\n            {\n                \"value\": field_value[\"01_new_collection_name\"],\n                \"options\": build_config[\"collection_name\"][\"options\"] + [field_value[\"01_new_collection_name\"]],\n            }\n        )\n        build_config[\"embedding_model\"][\"show\"] = not bool(provider)\n        build_config[\"embedding_model\"][\"required\"] = not bool(provider)\n        build_config[\"collection_name\"][\"options_metadata\"].append(\n            {\n                \"records\": 0,\n                \"provider\": provider,\n                \"icon\": self.get_provider_icon(provider_name=provider),\n                \"model\": field_value.get(\"03_embedding_generation_model\"),\n            }\n        )\n\n        # Make sure we always show the reranker options if the collection is hybrid enabled\n        # And right now they always are\n        build_config[\"lexical_terms\"][\"show\"] = True\n\n    def _handle_database_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle database selection and update related configurations.\"\"\"\n        build_config = self.reset_database_list(build_config)\n\n        # Reset collection list if database selection changes\n        if field_value not in build_config[\"database_name\"][\"options\"]:\n            build_config[\"database_name\"][\"value\"] = \"\"\n            return build_config\n\n        # Get the api endpoint for the selected database\n        index = build_config[\"database_name\"][\"options\"].index(field_value)\n        build_config[\"api_endpoint\"][\"value\"] = build_config[\"database_name\"][\"options_metadata\"][index][\"api_endpoint\"]\n\n        # Get the org_id for the selected database\n        org_id = build_config[\"database_name\"][\"options_metadata\"][index][\"org_id\"]\n        if not org_id:\n            return build_config\n\n        # Update the list of keyspaces based on the db info\n        build_config[\"keyspace\"][\"options\"] = build_config[\"database_name\"][\"options_metadata\"][index][\"keyspaces\"]\n        build_config[\"keyspace\"][\"value\"] = (\n            build_config[\"keyspace\"][\"options\"] and build_config[\"keyspace\"][\"options\"][0]\n            if build_config[\"keyspace\"][\"value\"] not in build_config[\"keyspace\"][\"options\"]\n            else build_config[\"keyspace\"][\"value\"]\n        )\n\n        # Get the database id for the selected database\n        db_id = self.get_database_id_static(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        keyspace = self.get_keyspace()\n\n        # Update the helper text for the embedding provider field\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_embedding_generation_provider\"][\"helper_text\"] = (\n            \"To create collections with more embedding provider options, go to \"\n            f'<a class=\"underline\" target=\"_blank\" rel=\"noopener noreferrer\" '\n            f'href=\"https://astra.datastax.com/org/{org_id}/database/{db_id}/data-explorer?createCollection=1&namespace={keyspace}\">'\n            \"your database in Astra DB</a>.\"\n        )\n\n        # Reset provider options\n        build_config = self.reset_provider_options(build_config)\n\n        # Handle hybrid search options\n        build_config = self._handle_hybrid_search_options(build_config)\n\n        return self.reset_collection_list(build_config)\n\n    def _handle_collection_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle collection selection and update embedding options.\"\"\"\n        build_config[\"autodetect_collection\"][\"value\"] = True\n        build_config = self.reset_collection_list(build_config)\n\n        # Reset embedding model if collection selection changes\n        if field_value and field_value not in build_config[\"collection_name\"][\"options\"]:\n            build_config[\"collection_name\"][\"options\"].append(field_value)\n            build_config[\"collection_name\"][\"options_metadata\"].append(\n                {\n                    \"records\": 0,\n                    \"provider\": None,\n                    \"icon\": \"vectorstores\",\n                    \"model\": None,\n                }\n            )\n            build_config[\"autodetect_collection\"][\"value\"] = False\n\n        if not field_value:\n            return build_config\n\n        # Get the selected collection index\n        index = build_config[\"collection_name\"][\"options\"].index(field_value)\n\n        # Set the provider of the selected collection\n        provider = build_config[\"collection_name\"][\"options_metadata\"][index][\"provider\"]\n        build_config[\"embedding_model\"][\"show\"] = not bool(provider)\n        build_config[\"embedding_model\"][\"required\"] = not bool(provider)\n\n        # Grab the collection object\n        database = self.get_database_object(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        collection = database.get_collection(\n            name=field_value,\n            keyspace=build_config[\"keyspace\"][\"value\"],\n        )\n\n        # Check if hybrid and lexical are enabled\n        col_options = collection.options()\n        hyb_enabled = col_options.rerank and col_options.rerank.enabled\n        lex_enabled = col_options.lexical and col_options.lexical.enabled\n        user_hyb_enabled = build_config[\"search_method\"][\"value\"] == \"Hybrid Search\"\n\n        # Show lexical terms if the collection is hybrid enabled\n        build_config[\"lexical_terms\"][\"show\"] = hyb_enabled and lex_enabled and user_hyb_enabled\n\n        return build_config\n\n    @check_cached_vector_store\n    def build_vector_store(self):\n        try:\n            from langchain_astradb import AstraDBVectorStore\n        except ImportError as e:\n            msg = (\n                \"Could not import langchain Astra DB integration package. \"\n                \"Please install it with `pip install langchain-astradb`.\"\n            )\n            raise ImportError(msg) from e\n\n        # Get the embedding model and additional params\n        embedding_params = {\"embedding\": self.embedding_model} if self.embedding_model else {}\n\n        # Get the additional parameters\n        additional_params = self.astradb_vectorstore_kwargs or {}\n\n        # Get Langflow version and platform information\n        __version__ = get_version_info()[\"version\"]\n        langflow_prefix = \"\"\n        # if os.getenv(\"AWS_EXECUTION_ENV\") == \"AWS_ECS_FARGATE\":  # TODO: More precise way of detecting\n        #     langflow_prefix = \"ds-\"\n\n        # Get the database object\n        database = self.get_database_object()\n        autodetect = self.collection_name in database.list_collection_names() and self.autodetect_collection\n\n        # Bundle up the auto-detect parameters\n        autodetect_params = {\n            \"autodetect_collection\": autodetect,\n            \"content_field\": (\n                self.content_field\n                if self.content_field and embedding_params\n                else (\n                    \"page_content\"\n                    if embedding_params\n                    and self.collection_data(collection_name=self.collection_name, database=database) == 0\n                    else None\n                )\n            ),\n            \"ignore_invalid_documents\": self.ignore_invalid_documents,\n        }\n\n        # Choose HybridSearchMode based on the selected param\n        hybrid_search_mode = HybridSearchMode.DEFAULT if self.search_method == \"Hybrid Search\" else HybridSearchMode.OFF\n\n        # Attempt to build the Vector Store object\n        try:\n            vector_store = AstraDBVectorStore(\n                # Astra DB Authentication Parameters\n                token=self.token,\n                api_endpoint=database.api_endpoint,\n                namespace=database.keyspace,\n                collection_name=self.collection_name,\n                environment=self.environment,\n                # Hybrid Search Parameters\n                hybrid_search=hybrid_search_mode,\n                # Astra DB Usage Tracking Parameters\n                ext_callers=[(f\"{langflow_prefix}langflow\", __version__)],\n                # Astra DB Vector Store Parameters\n                **autodetect_params,\n                **embedding_params,\n                **additional_params,\n            )\n        except Exception as e:\n            msg = f\"Error initializing AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        # Add documents to the vector store\n        self._add_documents_to_vector_store(vector_store)\n\n        return vector_store\n\n    def _add_documents_to_vector_store(self, vector_store) -> None:\n        self.ingest_data = self._prepare_ingest_data()\n\n        documents = []\n        for _input in self.ingest_data or []:\n            if isinstance(_input, Data):\n                documents.append(_input.to_lc_document())\n            else:\n                msg = \"Vector Store Inputs must be Data objects.\"\n                raise TypeError(msg)\n\n        documents = [\n            Document(page_content=doc.page_content, metadata=serialize(doc.metadata, to_str=True)) for doc in documents\n        ]\n\n        if documents and self.deletion_field:\n            self.log(f\"Deleting documents where {self.deletion_field}\")\n            try:\n                database = self.get_database_object()\n                collection = database.get_collection(self.collection_name, keyspace=database.keyspace)\n                delete_values = list({doc.metadata[self.deletion_field] for doc in documents})\n                self.log(f\"Deleting documents where {self.deletion_field} matches {delete_values}.\")\n                collection.delete_many({f\"metadata.{self.deletion_field}\": {\"$in\": delete_values}})\n            except Exception as e:\n                msg = f\"Error deleting documents from AstraDBVectorStore based on '{self.deletion_field}': {e}\"\n                raise ValueError(msg) from e\n\n        if documents:\n            self.log(f\"Adding {len(documents)} documents to the Vector Store.\")\n            try:\n                vector_store.add_documents(documents)\n            except Exception as e:\n                msg = f\"Error adding documents to AstraDBVectorStore: {e}\"\n                raise ValueError(msg) from e\n        else:\n            self.log(\"No documents to add to the Vector Store.\")\n\n    def _map_search_type(self) -> str:\n        search_type_mapping = {\n            \"Similarity with score threshold\": \"similarity_score_threshold\",\n            \"MMR (Max Marginal Relevance)\": \"mmr\",\n        }\n\n        return search_type_mapping.get(self.search_type, \"similarity\")\n\n    def _build_search_args(self):\n        # Clean up the search query\n        query = self.search_query if isinstance(self.search_query, str) and self.search_query.strip() else None\n        lexical_terms = self.lexical_terms or None\n\n        # Check if we have a search query, and if so set the args\n        if query:\n            args = {\n                \"query\": query,\n                \"search_type\": self._map_search_type(),\n                \"k\": self.number_of_results,\n                \"score_threshold\": self.search_score_threshold,\n                \"lexical_query\": lexical_terms,\n            }\n        elif self.advanced_search_filter:\n            args = {\n                \"n\": self.number_of_results,\n            }\n        else:\n            return {}\n\n        filter_arg = self.advanced_search_filter or {}\n        if filter_arg:\n            args[\"filter\"] = filter_arg\n\n        return args\n\n    def search_documents(self, vector_store=None) -> list[Data]:\n        vector_store = vector_store or self.build_vector_store()\n\n        self.log(f\"Search input: {self.search_query}\")\n        self.log(f\"Search type: {self.search_type}\")\n        self.log(f\"Number of results: {self.number_of_results}\")\n        self.log(f\"store.hybrid_search: {vector_store.hybrid_search}\")\n        self.log(f\"Lexical terms: {self.lexical_terms}\")\n        self.log(f\"Reranker: {self.reranker}\")\n\n        try:\n            search_args = self._build_search_args()\n        except Exception as e:\n            msg = f\"Error in AstraDBVectorStore._build_search_args: {e}\"\n            raise ValueError(msg) from e\n\n        if not search_args:\n            self.log(\"No search input or filters provided. Skipping search.\")\n            return []\n\n        docs = []\n        search_method = \"search\" if \"query\" in search_args else \"metadata_search\"\n\n        try:\n            self.log(f\"Calling vector_store.{search_method} with args: {search_args}\")\n            docs = getattr(vector_store, search_method)(**search_args)\n        except Exception as e:\n            msg = f\"Error performing {search_method} in AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        self.log(f\"Retrieved documents: {len(docs)}\")\n\n        data = docs_to_data(docs)\n        self.log(f\"Converted documents to data: {len(data)}\")\n        self.status = data\n\n        return data\n\n    def get_retriever_kwargs(self):\n        search_args = self._build_search_args()\n\n        return {\n            \"search_type\": self._map_search_type(),\n            \"search_kwargs\": search_args,\n        }\n"}, "collection_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several seconds for creation to complete.", "display_name": "Create new collection", "field_order": ["01_new_collection_name", "02_embedding_generation_provider", "03_embedding_generation_model", "04_dimension"], "name": "create_collection", "template": {"01_new_collection_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new collection to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_collection_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_embedding_generation_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding generation method", "dynamic": false, "helper_text": "To create collections with more embedding provider options, go to <a class=\"underline\" href=\"https://astra.datastax.com/\" target=\" _blank\" rel=\"noopener noreferrer\">your database in Astra DB</a>", "info": "Provider to use for generating embeddings.", "name": "embedding_generation_provider", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_embedding_generation_model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding model", "dynamic": false, "info": "Model to use for generating embeddings.", "name": "embedding_generation_model", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "04_dimension": {"_input_type": "IntInput", "advanced": false, "display_name": "Dimensions", "dynamic": false, "info": "Dimensions of the embeddings to generate.", "list": false, "list_add_label": "Add More", "name": "dimension", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int"}}}}}, "functionality": "create"}, "display_name": "Collection", "dynamic": false, "info": "The name of the collection within Astra DB where the vectors will be stored.", "name": "collection_name", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": false, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "content_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Content Field", "dynamic": false, "info": "Field to use as the text content field for the vector store.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "content_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "database_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several minutes for creation to complete.", "display_name": "Create new database", "field_order": ["01_new_database_name", "02_cloud_provider", "03_region"], "name": "create_database", "template": {"01_new_database_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new database to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_database_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_cloud_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Cloud provider", "dynamic": false, "info": "Cloud provider for the new database.", "name": "cloud_provider", "options": ["Amazon Web Services", "Google Cloud Platform", "Microsoft Azure"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_region": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Region", "dynamic": false, "info": "Region for the new database.", "name": "region", "options": [], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}}}}}, "functionality": "create"}, "display_name": "Database", "dynamic": false, "info": "The Database name for the Astra DB instance.", "name": "database_name", "options": [], "options_metadata": [{"api_endpoint": "https://5b8bb22c-4a38-4f0a-865c-a18ed7590bd1-us-east-2.apps.astra.datastax.com", "collections": 5, "keyspaces": ["default_keyspace", "samples_dataflow"], "org_id": "260f986d-e65c-4f05-94a3-7cebfcb867a3", "status": null}], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "deletion_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Deletion Based On Field", "dynamic": false, "info": "When this parameter is provided, documents in the target collection with metadata field values matching the input metadata field value will be deleted before new data is loaded.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "deletion_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "embedding_model": {"_input_type": "HandleInput", "advanced": false, "display_name": "Embedding Model", "dynamic": false, "info": "Specify the Embedding Model. Not required for Astra Vectorize collections.", "input_types": ["Embeddings"], "list": false, "list_add_label": "Add More", "name": "embedding_model", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "environment": {"_input_type": "DropdownInput", "advanced": true, "combobox": true, "dialog_inputs": {}, "display_name": "Environment", "dynamic": false, "info": "The environment for the Astra DB API Endpoint.", "name": "environment", "options": ["prod", "test", "dev"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "prod"}, "ignore_invalid_documents": {"_input_type": "BoolInput", "advanced": true, "display_name": "Ignore Invalid Documents", "dynamic": false, "info": "Boolean flag to determine whether to ignore invalid documents at runtime.", "list": false, "list_add_label": "Add More", "name": "ignore_invalid_documents", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "ingest_data": {"_input_type": "HandleInput", "advanced": false, "display_name": "Ingest Data", "dynamic": false, "info": "", "input_types": ["Data", "DataFrame"], "list": true, "list_add_label": "Add More", "name": "ingest_data", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "keyspace": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Keyspace", "dynamic": false, "info": "Optional keyspace within Astra DB to use for the collection.", "name": "keyspace", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "lexical_terms": {"_input_type": "QueryInput", "advanced": true, "display_name": "Lexical Terms", "dynamic": false, "info": "Add additional terms/keywords to augment search precision.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "lexical_terms", "placeholder": "Enter terms to search...", "required": false, "separator": " ", "show": false, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "query", "value": ""}, "number_of_results": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Search Results", "dynamic": false, "info": "Number of search results to return.", "list": false, "list_add_label": "Add More", "name": "number_of_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 4}, "reranker": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "<PERSON><PERSON><PERSON>", "dynamic": false, "info": "Post-retrieval model that re-scores results for optimal relevance ranking.", "name": "reranker", "options": [], "options_metadata": [], "placeholder": "", "required": false, "show": false, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "search_method": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Search Method", "dynamic": false, "info": "Determine how your content is matched: Vector finds semantic similarity, and Hybrid Search (suggested) combines both approaches with a reranker.", "name": "search_method", "options": ["Hybrid Search", "Vector Search"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Vector Search"}, "search_query": {"_input_type": "QueryInput", "advanced": false, "display_name": "Search Query", "dynamic": false, "info": "Enter a query to run a similarity search.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "search_query", "placeholder": "Enter a query...", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "query", "value": ""}, "search_score_threshold": {"_input_type": "FloatInput", "advanced": true, "display_name": "Search Score Threshold", "dynamic": false, "info": "Minimum similarity score threshold for search results. (when using 'Similarity with score threshold')", "list": false, "list_add_label": "Add More", "name": "search_score_threshold", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": 0}, "search_type": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Search Type", "dynamic": false, "info": "Search type to use", "name": "search_type", "options": ["Similarity", "Similarity with score threshold", "MMR (Max Marginal Relevance)"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Similarity"}, "should_cache_vector_store": {"_input_type": "BoolInput", "advanced": true, "display_name": "Cache Vector Store", "dynamic": false, "info": "If True, the vector store will be cached for the current build of the component. This is useful for components that have multiple output methods and want to share the same vector store.", "list": false, "list_add_label": "Add More", "name": "should_cache_vector_store", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "token": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Astra DB Application Token", "dynamic": false, "info": "Authentication token for accessing Astra DB.", "input_types": [], "load_from_db": true, "name": "token", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_APPLICATION_TOKEN"}}, "tool_mode": false}, "selected_output": "dataframe", "showNode": true, "type": "AstraDB"}, "dragging": false, "id": "AstraDB-JsRrT", "measured": {"height": 502, "width": 320}, "position": {"x": 1206.2272993725155, "y": 491.41485400844977}, "selected": false, "type": "genericNode"}, {"data": {"id": "AstraDB-W6NB4", "node": {"base_classes": ["Data", "DataFrame", "VectorStore"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Ingest and search documents in Astra DB", "display_name": "Astra DB", "documentation": "https://docs.datastax.com/en/langflow/astra-components.html", "edited": false, "field_order": ["token", "environment", "database_name", "api_endpoint", "keyspace", "collection_name", "embedding_model", "ingest_data", "search_query", "should_cache_vector_store", "search_method", "reranker", "lexical_terms", "number_of_results", "search_type", "search_score_threshold", "advanced_search_filter", "autodetect_collection", "content_field", "deletion_field", "ignore_invalid_documents", "astradb_vectorstore_kwargs"], "frozen": false, "icon": "AstraDB", "legacy": false, "metadata": {"code_hash": "38a337e89ff4", "module": "langflow.components.vectorstores.astradb.AstraDBVectorStoreComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Search Results", "group_outputs": false, "method": "search_documents", "name": "search_results", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "DataFrame", "group_outputs": false, "method": "as_dataframe", "name": "dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Vector Store Connection", "group_outputs": false, "hidden": true, "method": "as_vector_store", "name": "vectorstoreconnection", "selected": "VectorStore", "tool_mode": true, "types": ["VectorStore"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "advanced_search_filter": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "Search Metadata Filter", "dynamic": false, "info": "Optional dictionary of filters to apply to the search query.", "list": false, "list_add_label": "Add More", "name": "advanced_search_filter", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "api_endpoint": {"_input_type": "StrInput", "advanced": false, "display_name": "Astra DB API Endpoint", "dynamic": false, "info": "The API Endpoint for the Astra DB instance. Supercedes database selection.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "api_endpoint", "placeholder": "", "required": false, "show": false, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "astradb_vectorstore_kwargs": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "AstraDBVectorStore Parameters", "dynamic": false, "info": "Optional dictionary of additional parameters for the AstraDBVectorStore.", "list": false, "list_add_label": "Add More", "name": "astradb_vectorstore_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "autodetect_collection": {"_input_type": "BoolInput", "advanced": true, "display_name": "Autodetect Collection", "dynamic": false, "info": "Boolean flag to determine whether to autodetect the collection.", "list": false, "list_add_label": "Add More", "name": "autodetect_collection", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import re\nfrom collections import defaultdict\nfrom dataclasses import asdict, dataclass, field\n\nfrom astrapy import DataAPIClient, Database\nfrom astrapy.data.info.reranking import RerankServiceOptions\nfrom astrapy.info import CollectionDescriptor, CollectionLexicalOptions, CollectionRerankOptions\nfrom langchain_astradb import AstraDBVectorStore, VectorServiceOptions\nfrom langchain_astradb.utils.astradb import HybridSearchMode, _AstraDBCollectionEnvironment\nfrom langchain_core.documents import Document\n\nfrom langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store\nfrom langflow.base.vectorstores.vector_store_connection_decorator import vector_store_connection\nfrom langflow.helpers.data import docs_to_data\nfrom langflow.inputs.inputs import FloatInput, NestedDictInput\nfrom langflow.io import (\n    BoolInput,\n    DropdownInput,\n    HandleInput,\n    IntInput,\n    QueryInput,\n    SecretStrInput,\n    StrInput,\n)\nfrom langflow.schema.data import Data\nfrom langflow.serialization import serialize\nfrom langflow.utils.version import get_version_info\n\n\n@vector_store_connection\nclass AstraDBVectorStoreComponent(LCVectorStoreComponent):\n    display_name: str = \"Astra DB\"\n    description: str = \"Ingest and search documents in Astra DB\"\n    documentation: str = \"https://docs.datastax.com/en/langflow/astra-components.html\"\n    name = \"AstraDB\"\n    icon: str = \"AstraDB\"\n\n    _cached_vector_store: AstraDBVectorStore | None = None\n\n    @dataclass\n    class NewDatabaseInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_database\",\n                        \"description\": \"Please allow several minutes for creation to complete.\",\n                        \"display_name\": \"Create new database\",\n                        \"field_order\": [\"01_new_database_name\", \"02_cloud_provider\", \"03_region\"],\n                        \"template\": {\n                            \"01_new_database_name\": StrInput(\n                                name=\"new_database_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new database to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_cloud_provider\": DropdownInput(\n                                name=\"cloud_provider\",\n                                display_name=\"Cloud provider\",\n                                info=\"Cloud provider for the new database.\",\n                                options=[],\n                                required=True,\n                                real_time_refresh=True,\n                            ),\n                            \"03_region\": DropdownInput(\n                                name=\"region\",\n                                display_name=\"Region\",\n                                info=\"Region for the new database.\",\n                                options=[],\n                                required=True,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    @dataclass\n    class NewCollectionInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_collection\",\n                        \"description\": \"Please allow several seconds for creation to complete.\",\n                        \"display_name\": \"Create new collection\",\n                        \"field_order\": [\n                            \"01_new_collection_name\",\n                            \"02_embedding_generation_provider\",\n                            \"03_embedding_generation_model\",\n                            \"04_dimension\",\n                        ],\n                        \"template\": {\n                            \"01_new_collection_name\": StrInput(\n                                name=\"new_collection_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new collection to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_embedding_generation_provider\": DropdownInput(\n                                name=\"embedding_generation_provider\",\n                                display_name=\"Embedding generation method\",\n                                info=\"Provider to use for generating embeddings.\",\n                                helper_text=(\n                                    \"To create collections with more embedding provider options, go to \"\n                                    '<a class=\"underline\" href=\"https://astra.datastax.com/\" target=\" _blank\" '\n                                    'rel=\"noopener noreferrer\">your database in Astra DB</a>'\n                                ),\n                                real_time_refresh=True,\n                                required=True,\n                                options=[],\n                            ),\n                            \"03_embedding_generation_model\": DropdownInput(\n                                name=\"embedding_generation_model\",\n                                display_name=\"Embedding model\",\n                                info=\"Model to use for generating embeddings.\",\n                                real_time_refresh=True,\n                                options=[],\n                            ),\n                            \"04_dimension\": IntInput(\n                                name=\"dimension\",\n                                display_name=\"Dimensions\",\n                                info=\"Dimensions of the embeddings to generate.\",\n                                value=None,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    inputs = [\n        SecretStrInput(\n            name=\"token\",\n            display_name=\"Astra DB Application Token\",\n            info=\"Authentication token for accessing Astra DB.\",\n            value=\"ASTRA_DB_APPLICATION_TOKEN\",\n            required=True,\n            real_time_refresh=True,\n            input_types=[],\n        ),\n        DropdownInput(\n            name=\"environment\",\n            display_name=\"Environment\",\n            info=\"The environment for the Astra DB API Endpoint.\",\n            options=[\"prod\", \"test\", \"dev\"],\n            value=\"prod\",\n            advanced=True,\n            real_time_refresh=True,\n            combobox=True,\n        ),\n        DropdownInput(\n            name=\"database_name\",\n            display_name=\"Database\",\n            info=\"The Database name for the Astra DB instance.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewDatabaseInput()),\n            combobox=True,\n        ),\n        StrInput(\n            name=\"api_endpoint\",\n            display_name=\"Astra DB API Endpoint\",\n            info=\"The API Endpoint for the Astra DB instance. Supercedes database selection.\",\n            show=False,\n        ),\n        DropdownInput(\n            name=\"keyspace\",\n            display_name=\"Keyspace\",\n            info=\"Optional keyspace within Astra DB to use for the collection.\",\n            advanced=True,\n            options=[],\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"collection_name\",\n            display_name=\"Collection\",\n            info=\"The name of the collection within Astra DB where the vectors will be stored.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewCollectionInput()),\n            combobox=True,\n            show=False,\n        ),\n        HandleInput(\n            name=\"embedding_model\",\n            display_name=\"Embedding Model\",\n            input_types=[\"Embeddings\"],\n            info=\"Specify the Embedding Model. Not required for Astra Vectorize collections.\",\n            required=False,\n            show=False,\n        ),\n        *LCVectorStoreComponent.inputs,\n        DropdownInput(\n            name=\"search_method\",\n            display_name=\"Search Method\",\n            info=(\n                \"Determine how your content is matched: Vector finds semantic similarity, \"\n                \"and Hybrid Search (suggested) combines both approaches \"\n                \"with a reranker.\"\n            ),\n            options=[\"Hybrid Search\", \"Vector Search\"],  # TODO: Restore Lexical Search?\n            options_metadata=[{\"icon\": \"SearchHybrid\"}, {\"icon\": \"SearchVector\"}],\n            value=\"Vector Search\",\n            advanced=True,\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"reranker\",\n            display_name=\"Reranker\",\n            info=\"Post-retrieval model that re-scores results for optimal relevance ranking.\",\n            show=False,\n            toggle=True,\n        ),\n        QueryInput(\n            name=\"lexical_terms\",\n            display_name=\"Lexical Terms\",\n            info=\"Add additional terms/keywords to augment search precision.\",\n            placeholder=\"Enter terms to search...\",\n            separator=\" \",\n            show=False,\n            value=\"\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"number_of_results\",\n            display_name=\"Number of Search Results\",\n            info=\"Number of search results to return.\",\n            advanced=True,\n            value=4,\n        ),\n        DropdownInput(\n            name=\"search_type\",\n            display_name=\"Search Type\",\n            info=\"Search type to use\",\n            options=[\"Similarity\", \"Similarity with score threshold\", \"MMR (Max Marginal Relevance)\"],\n            value=\"Similarity\",\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"search_score_threshold\",\n            display_name=\"Search Score Threshold\",\n            info=\"Minimum similarity score threshold for search results. \"\n            \"(when using 'Similarity with score threshold')\",\n            value=0,\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"advanced_search_filter\",\n            display_name=\"Search Metadata Filter\",\n            info=\"Optional dictionary of filters to apply to the search query.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"autodetect_collection\",\n            display_name=\"Autodetect Collection\",\n            info=\"Boolean flag to determine whether to autodetect the collection.\",\n            advanced=True,\n            value=True,\n        ),\n        StrInput(\n            name=\"content_field\",\n            display_name=\"Content Field\",\n            info=\"Field to use as the text content field for the vector store.\",\n            advanced=True,\n        ),\n        StrInput(\n            name=\"deletion_field\",\n            display_name=\"Deletion Based On Field\",\n            info=\"When this parameter is provided, documents in the target collection with \"\n            \"metadata field values matching the input metadata field value will be deleted \"\n            \"before new data is loaded.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"ignore_invalid_documents\",\n            display_name=\"Ignore Invalid Documents\",\n            info=\"Boolean flag to determine whether to ignore invalid documents at runtime.\",\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"astradb_vectorstore_kwargs\",\n            display_name=\"AstraDBVectorStore Parameters\",\n            info=\"Optional dictionary of additional parameters for the AstraDBVectorStore.\",\n            advanced=True,\n        ),\n    ]\n\n    @classmethod\n    def map_cloud_providers(cls):\n        # TODO: Programmatically fetch the regions for each cloud provider\n        return {\n            \"dev\": {\n                \"Amazon Web Services\": {\n                    \"id\": \"aws\",\n                    \"regions\": [\"us-west-2\"],\n                },\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\", \"europe-west4\"],\n                },\n            },\n            \"test\": {\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\"],\n                },\n            },\n            \"prod\": {\n                \"Amazon Web Services\": {\n                    \"id\": \"aws\",\n                    \"regions\": [\"us-east-2\", \"ap-south-1\", \"eu-west-1\"],\n                },\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-east1\"],\n                },\n                \"Microsoft Azure\": {\n                    \"id\": \"azure\",\n                    \"regions\": [\"westus3\"],\n                },\n            },\n        }\n\n    @classmethod\n    def get_vectorize_providers(cls, token: str, environment: str | None = None, api_endpoint: str | None = None):\n        try:\n            # Get the admin object\n            client = DataAPIClient(environment=environment)\n            admin_client = client.get_admin()\n            db_admin = admin_client.get_database_admin(api_endpoint, token=token)\n\n            # Get the list of embedding providers\n            embedding_providers = db_admin.find_embedding_providers()\n\n            vectorize_providers_mapping = {}\n            # Map the provider display name to the provider key and models\n            for provider_key, provider_data in embedding_providers.embedding_providers.items():\n                # Get the provider display name and models\n                display_name = provider_data.display_name\n                models = [model.name for model in provider_data.models]\n\n                # Build our mapping\n                vectorize_providers_mapping[display_name] = [provider_key, models]\n\n            # Sort the resulting dictionary\n            return defaultdict(list, dict(sorted(vectorize_providers_mapping.items())))\n        except Exception as _:  # noqa: BLE001\n            return {}\n\n    @classmethod\n    async def create_database_api(\n        cls,\n        new_database_name: str,\n        cloud_provider: str,\n        region: str,\n        token: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n    ):\n        client = DataAPIClient(environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the environment, set to prod if null like\n        my_env = environment or \"prod\"\n\n        # Raise a value error if name isn't provided\n        if not new_database_name:\n            msg = \"Database name is required to create a new database.\"\n            raise ValueError(msg)\n\n        # Call the create database function\n        return await admin_client.async_create_database(\n            name=new_database_name,\n            cloud_provider=cls.map_cloud_providers()[my_env][cloud_provider][\"id\"],\n            region=region,\n            keyspace=keyspace,\n            wait_until_active=False,\n        )\n\n    @classmethod\n    async def create_collection_api(\n        cls,\n        new_collection_name: str,\n        token: str,\n        api_endpoint: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n        dimension: int | None = None,\n        embedding_generation_provider: str | None = None,\n        embedding_generation_model: str | None = None,\n        reranker: str | None = None,\n    ):\n        # Build vectorize options, if needed\n        vectorize_options = None\n        if not dimension:\n            providers = cls.get_vectorize_providers(token=token, environment=environment, api_endpoint=api_endpoint)\n            vectorize_options = VectorServiceOptions(\n                provider=providers.get(embedding_generation_provider, [None, []])[0],\n                model_name=embedding_generation_model,\n            )\n\n        # Raise a value error if name isn't provided\n        if not new_collection_name:\n            msg = \"Collection name is required to create a new collection.\"\n            raise ValueError(msg)\n\n        # Define the base arguments being passed to the create collection function\n        base_args = {\n            \"collection_name\": new_collection_name,\n            \"token\": token,\n            \"api_endpoint\": api_endpoint,\n            \"keyspace\": keyspace,\n            \"environment\": environment,\n            \"embedding_dimension\": dimension,\n            \"collection_vector_service_options\": vectorize_options,\n        }\n\n        # Add optional arguments if the reranker is set\n        if reranker:\n            # Split the reranker field into a provider a model name\n            provider, _ = reranker.split(\"/\")\n            base_args[\"collection_rerank\"] = CollectionRerankOptions(\n                service=RerankServiceOptions(provider=provider, model_name=reranker),\n            )\n            base_args[\"collection_lexical\"] = CollectionLexicalOptions(analyzer=\"STANDARD\")\n\n        _AstraDBCollectionEnvironment(**base_args)\n\n    @classmethod\n    def get_database_list_static(cls, token: str, environment: str | None = None):\n        client = DataAPIClient(environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the list of databases\n        db_list = admin_client.list_databases()\n\n        # Generate the api endpoint for each database\n        db_info_dict = {}\n        for db in db_list:\n            try:\n                # Get the API endpoint for the database\n                api_endpoint = db.regions[0].api_endpoint\n\n                # Get the number of collections\n                try:\n                    # Get the number of collections in the database\n                    num_collections = len(\n                        client.get_database(\n                            api_endpoint,\n                            token=token,\n                        ).list_collection_names()\n                    )\n                except Exception:  # noqa: BLE001\n                    if db.status != \"PENDING\":\n                        continue\n                    num_collections = 0\n\n                # Add the database to the dictionary\n                db_info_dict[db.name] = {\n                    \"api_endpoint\": api_endpoint,\n                    \"keyspaces\": db.keyspaces,\n                    \"collections\": num_collections,\n                    \"status\": db.status if db.status != \"ACTIVE\" else None,\n                    \"org_id\": db.org_id if db.org_id else None,\n                }\n            except Exception:  # noqa: BLE001, S110\n                pass\n\n        return db_info_dict\n\n    def get_database_list(self):\n        return self.get_database_list_static(\n            token=self.token,\n            environment=self.environment,\n        )\n\n    @classmethod\n    def get_api_endpoint_static(\n        cls,\n        token: str,\n        environment: str | None = None,\n        api_endpoint: str | None = None,\n        database_name: str | None = None,\n    ):\n        # If the api_endpoint is set, return it\n        if api_endpoint:\n            return api_endpoint\n\n        # Check if the database_name is like a url\n        if database_name and database_name.startswith(\"https://\"):\n            return database_name\n\n        # If the database is not set, nothing we can do.\n        if not database_name:\n            return None\n\n        # Grab the database object\n        db = cls.get_database_list_static(token=token, environment=environment).get(database_name)\n        if not db:\n            return None\n\n        # Otherwise, get the URL from the database list\n        return db.get(\"api_endpoint\")\n\n    def get_api_endpoint(self):\n        return self.get_api_endpoint_static(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=self.api_endpoint,\n            database_name=self.database_name,\n        )\n\n    @classmethod\n    def get_database_id_static(cls, api_endpoint: str) -> str | None:\n        # Pattern matches standard UUID format: 8-4-4-4-12 hexadecimal characters\n        uuid_pattern = r\"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\"\n        match = re.search(uuid_pattern, api_endpoint)\n\n        return match.group(0) if match else None\n\n    def get_database_id(self):\n        return self.get_database_id_static(api_endpoint=self.get_api_endpoint())\n\n    def get_keyspace(self):\n        keyspace = self.keyspace\n\n        if keyspace:\n            return keyspace.strip()\n\n        return \"default_keyspace\"\n\n    def get_database_object(self, api_endpoint: str | None = None):\n        try:\n            client = DataAPIClient(environment=self.environment)\n\n            return client.get_database(\n                api_endpoint or self.get_api_endpoint(),\n                token=self.token,\n                keyspace=self.get_keyspace(),\n            )\n        except Exception as e:\n            msg = f\"Error fetching database object: {e}\"\n            raise ValueError(msg) from e\n\n    def collection_data(self, collection_name: str, database: Database | None = None):\n        try:\n            if not database:\n                client = DataAPIClient(environment=self.environment)\n\n                database = client.get_database(\n                    self.get_api_endpoint(),\n                    token=self.token,\n                    keyspace=self.get_keyspace(),\n                )\n\n            collection = database.get_collection(collection_name)\n\n            return collection.estimated_document_count()\n        except Exception as e:  # noqa: BLE001\n            self.log(f\"Error checking collection data: {e}\")\n\n            return None\n\n    def _initialize_database_options(self):\n        try:\n            return [\n                {\n                    \"name\": name,\n                    \"status\": info[\"status\"],\n                    \"collections\": info[\"collections\"],\n                    \"api_endpoint\": info[\"api_endpoint\"],\n                    \"keyspaces\": info[\"keyspaces\"],\n                    \"org_id\": info[\"org_id\"],\n                }\n                for name, info in self.get_database_list().items()\n            ]\n        except Exception as e:\n            msg = f\"Error fetching database options: {e}\"\n            raise ValueError(msg) from e\n\n    @classmethod\n    def get_provider_icon(cls, collection: CollectionDescriptor | None = None, provider_name: str | None = None) -> str:\n        # Get the provider name from the collection\n        provider_name = provider_name or (\n            collection.definition.vector.service.provider\n            if (\n                collection\n                and collection.definition\n                and collection.definition.vector\n                and collection.definition.vector.service\n            )\n            else None\n        )\n\n        # If there is no provider, use the vector store icon\n        if not provider_name or provider_name.lower() == \"bring your own\":\n            return \"vectorstores\"\n\n        # Map provider casings\n        case_map = {\n            \"nvidia\": \"NVIDIA\",\n            \"openai\": \"OpenAI\",\n            \"amazon bedrock\": \"AmazonBedrockEmbeddings\",\n            \"azure openai\": \"AzureOpenAiEmbeddings\",\n            \"cohere\": \"Cohere\",\n            \"jina ai\": \"JinaAI\",\n            \"mistral ai\": \"MistralAI\",\n            \"upstage\": \"Upstage\",\n            \"voyage ai\": \"VoyageAI\",\n        }\n\n        # Adjust the casing on some like nvidia\n        return case_map[provider_name.lower()] if provider_name.lower() in case_map else provider_name.title()\n\n    def _initialize_collection_options(self, api_endpoint: str | None = None):\n        # Nothing to generate if we don't have an API endpoint yet\n        api_endpoint = api_endpoint or self.get_api_endpoint()\n        if not api_endpoint:\n            return []\n\n        # Retrieve the database object\n        database = self.get_database_object(api_endpoint=api_endpoint)\n\n        # Get the list of collections\n        collection_list = database.list_collections(keyspace=self.get_keyspace())\n\n        # Return the list of collections and metadata associated\n        return [\n            {\n                \"name\": col.name,\n                \"records\": self.collection_data(collection_name=col.name, database=database),\n                \"provider\": (\n                    col.definition.vector.service.provider\n                    if col.definition.vector and col.definition.vector.service\n                    else None\n                ),\n                \"icon\": self.get_provider_icon(collection=col),\n                \"model\": (\n                    col.definition.vector.service.model_name\n                    if col.definition.vector and col.definition.vector.service\n                    else None\n                ),\n            }\n            for col in collection_list\n        ]\n\n    def reset_provider_options(self, build_config: dict) -> dict:\n        \"\"\"Reset provider options and related configurations in the build_config dictionary.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get vectorize providers\n        vectorize_providers_api = self.get_vectorize_providers(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n        )\n\n        # Create a new dictionary with \"Bring your own\" first\n        vectorize_providers: dict[str, list[list[str]]] = {\"Bring your own\": [[], []]}\n\n        # Add the remaining items (only Nvidia) from the original dictionary\n        vectorize_providers.update(\n            {\n                k: v\n                for k, v in vectorize_providers_api.items()\n                if k.lower() in [\"nvidia\"]  # TODO: Eventually support more\n            }\n        )\n\n        # Set provider options\n        provider_field = \"02_embedding_generation_provider\"\n        template[provider_field][\"options\"] = list(vectorize_providers.keys())\n\n        # Add metadata for each provider option\n        template[provider_field][\"options_metadata\"] = [\n            {\"icon\": self.get_provider_icon(provider_name=provider)} for provider in template[provider_field][\"options\"]\n        ]\n\n        # Get selected embedding provider\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure embedding model field\n        model_field = \"03_embedding_generation_model\"\n        template[model_field].update(\n            {\n                \"options\": vectorize_providers.get(embedding_provider, [[], []])[1],\n                \"placeholder\": \"Bring your own\" if is_bring_your_own else None,\n                \"readonly\": is_bring_your_own,\n                \"required\": not is_bring_your_own,\n                \"value\": None,\n            }\n        )\n\n        # If this is a bring your own, set dimensions to 0\n        return self.reset_dimension_field(build_config)\n\n    def reset_dimension_field(self, build_config: dict) -> dict:\n        \"\"\"Reset dimension field options based on provided configuration.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get selected embedding model\n        provider_field = \"02_embedding_generation_provider\"\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure dimension field\n        dimension_field = \"04_dimension\"\n        dimension_value = 1024 if not is_bring_your_own else None  # TODO: Dynamically figure this out\n        template[dimension_field].update(\n            {\n                \"placeholder\": dimension_value,\n                \"value\": dimension_value,\n                \"readonly\": not is_bring_your_own,\n                \"required\": is_bring_your_own,\n            }\n        )\n\n        return build_config\n\n    def reset_collection_list(self, build_config: dict) -> dict:\n        \"\"\"Reset collection list options based on provided configuration.\"\"\"\n        # Get collection options\n        collection_options = self._initialize_collection_options(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        # Update collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update(\n            {\n                \"options\": [col[\"name\"] for col in collection_options],\n                \"options_metadata\": [{k: v for k, v in col.items() if k != \"name\"} for col in collection_options],\n            }\n        )\n\n        # Reset selected collection if not in options\n        if collection_config[\"value\"] not in collection_config[\"options\"]:\n            collection_config[\"value\"] = \"\"\n\n        # Set advanced status based on database selection\n        collection_config[\"show\"] = bool(build_config[\"database_name\"][\"value\"])\n\n        return build_config\n\n    def reset_database_list(self, build_config: dict) -> dict:\n        \"\"\"Reset database list options and related configurations.\"\"\"\n        # Get database options\n        database_options = self._initialize_database_options()\n\n        # Update cloud provider options\n        env = self.environment\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_cloud_provider\"][\"options\"] = list(self.map_cloud_providers()[env].keys())\n\n        # Update database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update(\n            {\n                \"options\": [db[\"name\"] for db in database_options],\n                \"options_metadata\": [{k: v for k, v in db.items() if k != \"name\"} for db in database_options],\n            }\n        )\n\n        # Reset selections if value not in options\n        if database_config[\"value\"] not in database_config[\"options\"]:\n            database_config[\"value\"] = \"\"\n            build_config[\"api_endpoint\"][\"value\"] = \"\"\n            build_config[\"collection_name\"][\"show\"] = False\n\n        # Set advanced status based on token presence\n        database_config[\"show\"] = bool(build_config[\"token\"][\"value\"])\n\n        return build_config\n\n    def reset_build_config(self, build_config: dict) -> dict:\n        \"\"\"Reset all build configuration options to default empty state.\"\"\"\n        # Reset database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"show\": False})\n        build_config[\"api_endpoint\"][\"value\"] = \"\"\n\n        # Reset collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"show\": False})\n\n        return build_config\n\n    def _handle_hybrid_search_options(self, build_config: dict) -> dict:\n        \"\"\"Set hybrid search options in the build configuration.\"\"\"\n        # Detect what hybrid options are available\n        # Get the admin object\n        client = DataAPIClient(environment=self.environment)\n        admin_client = client.get_admin()\n        db_admin = admin_client.get_database_admin(self.get_api_endpoint(), token=self.token)\n\n        # We will try to get the reranking providers to see if its hybrid emabled\n        try:\n            providers = db_admin.find_reranking_providers()\n            build_config[\"reranker\"][\"options\"] = [\n                model.name for provider_data in providers.reranking_providers.values() for model in provider_data.models\n            ]\n            build_config[\"reranker\"][\"options_metadata\"] = [\n                {\"icon\": self.get_provider_icon(provider_name=model.name.split(\"/\")[0])}\n                for provider in providers.reranking_providers.values()\n                for model in provider.models\n            ]\n            build_config[\"reranker\"][\"value\"] = build_config[\"reranker\"][\"options\"][0]\n\n            # Set the default search field to hybrid search\n            build_config[\"search_method\"][\"show\"] = True\n            build_config[\"search_method\"][\"options\"] = [\"Hybrid Search\", \"Vector Search\"]\n            build_config[\"search_method\"][\"value\"] = \"Hybrid Search\"\n        except Exception as _:  # noqa: BLE001\n            build_config[\"reranker\"][\"options\"] = []\n            build_config[\"reranker\"][\"options_metadata\"] = []\n\n            # Set the default search field to vector search\n            build_config[\"search_method\"][\"show\"] = False\n            build_config[\"search_method\"][\"options\"] = [\"Vector Search\"]\n            build_config[\"search_method\"][\"value\"] = \"Vector Search\"\n\n        # Set reranker and lexical terms options based on search method\n        build_config[\"reranker\"][\"toggle_value\"] = True\n        build_config[\"reranker\"][\"show\"] = build_config[\"search_method\"][\"value\"] == \"Hybrid Search\"\n        build_config[\"reranker\"][\"toggle_disable\"] = build_config[\"search_method\"][\"value\"] == \"Hybrid Search\"\n        if build_config[\"reranker\"][\"show\"]:\n            build_config[\"search_type\"][\"value\"] = \"Similarity\"\n\n        return build_config\n\n    async def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:\n        \"\"\"Update build configuration based on field name and value.\"\"\"\n        # Early return if no token provided\n        if not self.token:\n            return self.reset_build_config(build_config)\n\n        # Database creation callback\n        if field_name == \"database_name\" and isinstance(field_value, dict):\n            if \"01_new_database_name\" in field_value:\n                await self._create_new_database(build_config, field_value)\n                return self.reset_collection_list(build_config)\n            return self._update_cloud_regions(build_config, field_value)\n\n        # Collection creation callback\n        if field_name == \"collection_name\" and isinstance(field_value, dict):\n            # Case 1: New collection creation\n            if \"01_new_collection_name\" in field_value:\n                await self._create_new_collection(build_config, field_value)\n                return build_config\n\n            # Case 2: Update embedding provider options\n            if \"02_embedding_generation_provider\" in field_value:\n                return self.reset_provider_options(build_config)\n\n            # Case 3: Update dimension field\n            if \"03_embedding_generation_model\" in field_value:\n                return self.reset_dimension_field(build_config)\n\n        # Initial execution or token/environment change\n        first_run = field_name == \"collection_name\" and not field_value and not build_config[\"database_name\"][\"options\"]\n        if first_run or field_name in {\"token\", \"environment\"}:\n            return self.reset_database_list(build_config)\n\n        # Database selection change\n        if field_name == \"database_name\" and not isinstance(field_value, dict):\n            return self._handle_database_selection(build_config, field_value)\n\n        # Keyspace selection change\n        if field_name == \"keyspace\":\n            return self.reset_collection_list(build_config)\n\n        # Collection selection change\n        if field_name == \"collection_name\" and not isinstance(field_value, dict):\n            return self._handle_collection_selection(build_config, field_value)\n\n        # Search method selection change\n        if field_name == \"search_method\":\n            is_vector_search = field_value == \"Vector Search\"\n            is_autodetect = build_config[\"autodetect_collection\"][\"value\"]\n\n            # Configure lexical terms (same for both cases)\n            build_config[\"lexical_terms\"][\"show\"] = not is_vector_search\n            build_config[\"lexical_terms\"][\"value\"] = \"\" if is_vector_search else build_config[\"lexical_terms\"][\"value\"]\n\n            # Disable reranker disabling if hybrid search is selected\n            build_config[\"reranker\"][\"toggle_disable\"] = not is_vector_search\n            build_config[\"reranker\"][\"toggle_value\"] = True\n            build_config[\"reranker\"][\"value\"] = build_config[\"reranker\"][\"options\"][0]\n\n            # Toggle search type and score threshold based on search method\n            build_config[\"search_type\"][\"show\"] = is_vector_search\n            build_config[\"search_score_threshold\"][\"show\"] = is_vector_search\n\n            # Make sure the search_type is set to \"Similarity\"\n            if not is_vector_search or is_autodetect:\n                build_config[\"search_type\"][\"value\"] = \"Similarity\"\n\n        return build_config\n\n    async def _create_new_database(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new database and update build config options.\"\"\"\n        try:\n            await self.create_database_api(\n                new_database_name=field_value[\"01_new_database_name\"],\n                token=self.token,\n                keyspace=self.get_keyspace(),\n                environment=self.environment,\n                cloud_provider=field_value[\"02_cloud_provider\"],\n                region=field_value[\"03_region\"],\n            )\n        except Exception as e:\n            msg = f\"Error creating database: {e}\"\n            raise ValueError(msg) from e\n\n        build_config[\"database_name\"][\"options\"].append(field_value[\"01_new_database_name\"])\n        build_config[\"database_name\"][\"options_metadata\"].append(\n            {\n                \"status\": \"PENDING\",\n                \"collections\": 0,\n                \"api_endpoint\": None,\n                \"keyspaces\": [self.get_keyspace()],\n                \"org_id\": None,\n            }\n        )\n\n    def _update_cloud_regions(self, build_config: dict, field_value: dict) -> dict:\n        \"\"\"Update cloud provider regions in build config.\"\"\"\n        env = self.environment\n        cloud_provider = field_value[\"02_cloud_provider\"]\n\n        # Update the region options based on the selected cloud provider\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"03_region\"][\"options\"] = self.map_cloud_providers()[env][cloud_provider][\"regions\"]\n\n        # Reset the the 03_region value if it's not in the new options\n        if template[\"03_region\"][\"value\"] not in template[\"03_region\"][\"options\"]:\n            template[\"03_region\"][\"value\"] = None\n\n        return build_config\n\n    async def _create_new_collection(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new collection and update build config options.\"\"\"\n        embedding_provider = field_value.get(\"02_embedding_generation_provider\")\n        try:\n            await self.create_collection_api(\n                new_collection_name=field_value[\"01_new_collection_name\"],\n                token=self.token,\n                api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n                environment=self.environment,\n                keyspace=self.get_keyspace(),\n                dimension=field_value.get(\"04_dimension\") if embedding_provider == \"Bring your own\" else None,\n                embedding_generation_provider=embedding_provider,\n                embedding_generation_model=field_value.get(\"03_embedding_generation_model\"),\n                reranker=self.reranker,\n            )\n        except Exception as e:\n            msg = f\"Error creating collection: {e}\"\n            raise ValueError(msg) from e\n\n        provider = embedding_provider.lower() if embedding_provider and embedding_provider != \"Bring your own\" else None\n        build_config[\"collection_name\"].update(\n            {\n                \"value\": field_value[\"01_new_collection_name\"],\n                \"options\": build_config[\"collection_name\"][\"options\"] + [field_value[\"01_new_collection_name\"]],\n            }\n        )\n        build_config[\"embedding_model\"][\"show\"] = not bool(provider)\n        build_config[\"embedding_model\"][\"required\"] = not bool(provider)\n        build_config[\"collection_name\"][\"options_metadata\"].append(\n            {\n                \"records\": 0,\n                \"provider\": provider,\n                \"icon\": self.get_provider_icon(provider_name=provider),\n                \"model\": field_value.get(\"03_embedding_generation_model\"),\n            }\n        )\n\n        # Make sure we always show the reranker options if the collection is hybrid enabled\n        # And right now they always are\n        build_config[\"lexical_terms\"][\"show\"] = True\n\n    def _handle_database_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle database selection and update related configurations.\"\"\"\n        build_config = self.reset_database_list(build_config)\n\n        # Reset collection list if database selection changes\n        if field_value not in build_config[\"database_name\"][\"options\"]:\n            build_config[\"database_name\"][\"value\"] = \"\"\n            return build_config\n\n        # Get the api endpoint for the selected database\n        index = build_config[\"database_name\"][\"options\"].index(field_value)\n        build_config[\"api_endpoint\"][\"value\"] = build_config[\"database_name\"][\"options_metadata\"][index][\"api_endpoint\"]\n\n        # Get the org_id for the selected database\n        org_id = build_config[\"database_name\"][\"options_metadata\"][index][\"org_id\"]\n        if not org_id:\n            return build_config\n\n        # Update the list of keyspaces based on the db info\n        build_config[\"keyspace\"][\"options\"] = build_config[\"database_name\"][\"options_metadata\"][index][\"keyspaces\"]\n        build_config[\"keyspace\"][\"value\"] = (\n            build_config[\"keyspace\"][\"options\"] and build_config[\"keyspace\"][\"options\"][0]\n            if build_config[\"keyspace\"][\"value\"] not in build_config[\"keyspace\"][\"options\"]\n            else build_config[\"keyspace\"][\"value\"]\n        )\n\n        # Get the database id for the selected database\n        db_id = self.get_database_id_static(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        keyspace = self.get_keyspace()\n\n        # Update the helper text for the embedding provider field\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_embedding_generation_provider\"][\"helper_text\"] = (\n            \"To create collections with more embedding provider options, go to \"\n            f'<a class=\"underline\" target=\"_blank\" rel=\"noopener noreferrer\" '\n            f'href=\"https://astra.datastax.com/org/{org_id}/database/{db_id}/data-explorer?createCollection=1&namespace={keyspace}\">'\n            \"your database in Astra DB</a>.\"\n        )\n\n        # Reset provider options\n        build_config = self.reset_provider_options(build_config)\n\n        # Handle hybrid search options\n        build_config = self._handle_hybrid_search_options(build_config)\n\n        return self.reset_collection_list(build_config)\n\n    def _handle_collection_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle collection selection and update embedding options.\"\"\"\n        build_config[\"autodetect_collection\"][\"value\"] = True\n        build_config = self.reset_collection_list(build_config)\n\n        # Reset embedding model if collection selection changes\n        if field_value and field_value not in build_config[\"collection_name\"][\"options\"]:\n            build_config[\"collection_name\"][\"options\"].append(field_value)\n            build_config[\"collection_name\"][\"options_metadata\"].append(\n                {\n                    \"records\": 0,\n                    \"provider\": None,\n                    \"icon\": \"vectorstores\",\n                    \"model\": None,\n                }\n            )\n            build_config[\"autodetect_collection\"][\"value\"] = False\n\n        if not field_value:\n            return build_config\n\n        # Get the selected collection index\n        index = build_config[\"collection_name\"][\"options\"].index(field_value)\n\n        # Set the provider of the selected collection\n        provider = build_config[\"collection_name\"][\"options_metadata\"][index][\"provider\"]\n        build_config[\"embedding_model\"][\"show\"] = not bool(provider)\n        build_config[\"embedding_model\"][\"required\"] = not bool(provider)\n\n        # Grab the collection object\n        database = self.get_database_object(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        collection = database.get_collection(\n            name=field_value,\n            keyspace=build_config[\"keyspace\"][\"value\"],\n        )\n\n        # Check if hybrid and lexical are enabled\n        col_options = collection.options()\n        hyb_enabled = col_options.rerank and col_options.rerank.enabled\n        lex_enabled = col_options.lexical and col_options.lexical.enabled\n        user_hyb_enabled = build_config[\"search_method\"][\"value\"] == \"Hybrid Search\"\n\n        # Show lexical terms if the collection is hybrid enabled\n        build_config[\"lexical_terms\"][\"show\"] = hyb_enabled and lex_enabled and user_hyb_enabled\n\n        return build_config\n\n    @check_cached_vector_store\n    def build_vector_store(self):\n        try:\n            from langchain_astradb import AstraDBVectorStore\n        except ImportError as e:\n            msg = (\n                \"Could not import langchain Astra DB integration package. \"\n                \"Please install it with `pip install langchain-astradb`.\"\n            )\n            raise ImportError(msg) from e\n\n        # Get the embedding model and additional params\n        embedding_params = {\"embedding\": self.embedding_model} if self.embedding_model else {}\n\n        # Get the additional parameters\n        additional_params = self.astradb_vectorstore_kwargs or {}\n\n        # Get Langflow version and platform information\n        __version__ = get_version_info()[\"version\"]\n        langflow_prefix = \"\"\n        # if os.getenv(\"AWS_EXECUTION_ENV\") == \"AWS_ECS_FARGATE\":  # TODO: More precise way of detecting\n        #     langflow_prefix = \"ds-\"\n\n        # Get the database object\n        database = self.get_database_object()\n        autodetect = self.collection_name in database.list_collection_names() and self.autodetect_collection\n\n        # Bundle up the auto-detect parameters\n        autodetect_params = {\n            \"autodetect_collection\": autodetect,\n            \"content_field\": (\n                self.content_field\n                if self.content_field and embedding_params\n                else (\n                    \"page_content\"\n                    if embedding_params\n                    and self.collection_data(collection_name=self.collection_name, database=database) == 0\n                    else None\n                )\n            ),\n            \"ignore_invalid_documents\": self.ignore_invalid_documents,\n        }\n\n        # Choose HybridSearchMode based on the selected param\n        hybrid_search_mode = HybridSearchMode.DEFAULT if self.search_method == \"Hybrid Search\" else HybridSearchMode.OFF\n\n        # Attempt to build the Vector Store object\n        try:\n            vector_store = AstraDBVectorStore(\n                # Astra DB Authentication Parameters\n                token=self.token,\n                api_endpoint=database.api_endpoint,\n                namespace=database.keyspace,\n                collection_name=self.collection_name,\n                environment=self.environment,\n                # Hybrid Search Parameters\n                hybrid_search=hybrid_search_mode,\n                # Astra DB Usage Tracking Parameters\n                ext_callers=[(f\"{langflow_prefix}langflow\", __version__)],\n                # Astra DB Vector Store Parameters\n                **autodetect_params,\n                **embedding_params,\n                **additional_params,\n            )\n        except Exception as e:\n            msg = f\"Error initializing AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        # Add documents to the vector store\n        self._add_documents_to_vector_store(vector_store)\n\n        return vector_store\n\n    def _add_documents_to_vector_store(self, vector_store) -> None:\n        self.ingest_data = self._prepare_ingest_data()\n\n        documents = []\n        for _input in self.ingest_data or []:\n            if isinstance(_input, Data):\n                documents.append(_input.to_lc_document())\n            else:\n                msg = \"Vector Store Inputs must be Data objects.\"\n                raise TypeError(msg)\n\n        documents = [\n            Document(page_content=doc.page_content, metadata=serialize(doc.metadata, to_str=True)) for doc in documents\n        ]\n\n        if documents and self.deletion_field:\n            self.log(f\"Deleting documents where {self.deletion_field}\")\n            try:\n                database = self.get_database_object()\n                collection = database.get_collection(self.collection_name, keyspace=database.keyspace)\n                delete_values = list({doc.metadata[self.deletion_field] for doc in documents})\n                self.log(f\"Deleting documents where {self.deletion_field} matches {delete_values}.\")\n                collection.delete_many({f\"metadata.{self.deletion_field}\": {\"$in\": delete_values}})\n            except Exception as e:\n                msg = f\"Error deleting documents from AstraDBVectorStore based on '{self.deletion_field}': {e}\"\n                raise ValueError(msg) from e\n\n        if documents:\n            self.log(f\"Adding {len(documents)} documents to the Vector Store.\")\n            try:\n                vector_store.add_documents(documents)\n            except Exception as e:\n                msg = f\"Error adding documents to AstraDBVectorStore: {e}\"\n                raise ValueError(msg) from e\n        else:\n            self.log(\"No documents to add to the Vector Store.\")\n\n    def _map_search_type(self) -> str:\n        search_type_mapping = {\n            \"Similarity with score threshold\": \"similarity_score_threshold\",\n            \"MMR (Max Marginal Relevance)\": \"mmr\",\n        }\n\n        return search_type_mapping.get(self.search_type, \"similarity\")\n\n    def _build_search_args(self):\n        # Clean up the search query\n        query = self.search_query if isinstance(self.search_query, str) and self.search_query.strip() else None\n        lexical_terms = self.lexical_terms or None\n\n        # Check if we have a search query, and if so set the args\n        if query:\n            args = {\n                \"query\": query,\n                \"search_type\": self._map_search_type(),\n                \"k\": self.number_of_results,\n                \"score_threshold\": self.search_score_threshold,\n                \"lexical_query\": lexical_terms,\n            }\n        elif self.advanced_search_filter:\n            args = {\n                \"n\": self.number_of_results,\n            }\n        else:\n            return {}\n\n        filter_arg = self.advanced_search_filter or {}\n        if filter_arg:\n            args[\"filter\"] = filter_arg\n\n        return args\n\n    def search_documents(self, vector_store=None) -> list[Data]:\n        vector_store = vector_store or self.build_vector_store()\n\n        self.log(f\"Search input: {self.search_query}\")\n        self.log(f\"Search type: {self.search_type}\")\n        self.log(f\"Number of results: {self.number_of_results}\")\n        self.log(f\"store.hybrid_search: {vector_store.hybrid_search}\")\n        self.log(f\"Lexical terms: {self.lexical_terms}\")\n        self.log(f\"Reranker: {self.reranker}\")\n\n        try:\n            search_args = self._build_search_args()\n        except Exception as e:\n            msg = f\"Error in AstraDBVectorStore._build_search_args: {e}\"\n            raise ValueError(msg) from e\n\n        if not search_args:\n            self.log(\"No search input or filters provided. Skipping search.\")\n            return []\n\n        docs = []\n        search_method = \"search\" if \"query\" in search_args else \"metadata_search\"\n\n        try:\n            self.log(f\"Calling vector_store.{search_method} with args: {search_args}\")\n            docs = getattr(vector_store, search_method)(**search_args)\n        except Exception as e:\n            msg = f\"Error performing {search_method} in AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        self.log(f\"Retrieved documents: {len(docs)}\")\n\n        data = docs_to_data(docs)\n        self.log(f\"Converted documents to data: {len(data)}\")\n        self.status = data\n\n        return data\n\n    def get_retriever_kwargs(self):\n        search_args = self._build_search_args()\n\n        return {\n            \"search_type\": self._map_search_type(),\n            \"search_kwargs\": search_args,\n        }\n"}, "collection_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several seconds for creation to complete.", "display_name": "Create new collection", "field_order": ["01_new_collection_name", "02_embedding_generation_provider", "03_embedding_generation_model", "04_dimension"], "name": "create_collection", "template": {"01_new_collection_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new collection to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_collection_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_embedding_generation_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding generation method", "dynamic": false, "helper_text": "To create collections with more embedding provider options, go to <a class=\"underline\" href=\"https://astra.datastax.com/\" target=\" _blank\" rel=\"noopener noreferrer\">your database in Astra DB</a>", "info": "Provider to use for generating embeddings.", "name": "embedding_generation_provider", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_embedding_generation_model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding model", "dynamic": false, "info": "Model to use for generating embeddings.", "name": "embedding_generation_model", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "04_dimension": {"_input_type": "IntInput", "advanced": false, "display_name": "Dimensions", "dynamic": false, "info": "Dimensions of the embeddings to generate.", "list": false, "list_add_label": "Add More", "name": "dimension", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int"}}}}}, "functionality": "create"}, "display_name": "Collection", "dynamic": false, "info": "The name of the collection within Astra DB where the vectors will be stored.", "name": "collection_name", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": false, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "content_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Content Field", "dynamic": false, "info": "Field to use as the text content field for the vector store.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "content_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "database_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several minutes for creation to complete.", "display_name": "Create new database", "field_order": ["01_new_database_name", "02_cloud_provider", "03_region"], "name": "create_database", "template": {"01_new_database_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new database to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_database_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_cloud_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Cloud provider", "dynamic": false, "info": "Cloud provider for the new database.", "name": "cloud_provider", "options": ["Amazon Web Services", "Google Cloud Platform", "Microsoft Azure"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_region": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Region", "dynamic": false, "info": "Region for the new database.", "name": "region", "options": [], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}}}}}, "functionality": "create"}, "display_name": "Database", "dynamic": false, "info": "The Database name for the Astra DB instance.", "name": "database_name", "options": [], "options_metadata": [{"api_endpoint": "https://5b8bb22c-4a38-4f0a-865c-a18ed7590bd1-us-east-2.apps.astra.datastax.com", "collections": 5, "keyspaces": ["default_keyspace", "samples_dataflow"], "org_id": "260f986d-e65c-4f05-94a3-7cebfcb867a3", "status": null}], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "deletion_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Deletion Based On Field", "dynamic": false, "info": "When this parameter is provided, documents in the target collection with metadata field values matching the input metadata field value will be deleted before new data is loaded.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "deletion_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "embedding_model": {"_input_type": "HandleInput", "advanced": false, "display_name": "Embedding Model", "dynamic": false, "info": "Specify the Embedding Model. Not required for Astra Vectorize collections.", "input_types": ["Embeddings"], "list": false, "list_add_label": "Add More", "name": "embedding_model", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "environment": {"_input_type": "DropdownInput", "advanced": true, "combobox": true, "dialog_inputs": {}, "display_name": "Environment", "dynamic": false, "info": "The environment for the Astra DB API Endpoint.", "name": "environment", "options": ["prod", "test", "dev"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "prod"}, "ignore_invalid_documents": {"_input_type": "BoolInput", "advanced": true, "display_name": "Ignore Invalid Documents", "dynamic": false, "info": "Boolean flag to determine whether to ignore invalid documents at runtime.", "list": false, "list_add_label": "Add More", "name": "ignore_invalid_documents", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "ingest_data": {"_input_type": "HandleInput", "advanced": false, "display_name": "Ingest Data", "dynamic": false, "info": "", "input_types": ["Data", "DataFrame"], "list": true, "list_add_label": "Add More", "name": "ingest_data", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "keyspace": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Keyspace", "dynamic": false, "info": "Optional keyspace within Astra DB to use for the collection.", "name": "keyspace", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "lexical_terms": {"_input_type": "QueryInput", "advanced": true, "display_name": "Lexical Terms", "dynamic": false, "info": "Add additional terms/keywords to augment search precision.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "lexical_terms", "placeholder": "Enter terms to search...", "required": false, "separator": " ", "show": false, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "query", "value": ""}, "number_of_results": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Search Results", "dynamic": false, "info": "Number of search results to return.", "list": false, "list_add_label": "Add More", "name": "number_of_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 4}, "reranker": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "<PERSON><PERSON><PERSON>", "dynamic": false, "info": "Post-retrieval model that re-scores results for optimal relevance ranking.", "name": "reranker", "options": [], "options_metadata": [], "placeholder": "", "required": false, "show": false, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "search_method": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Search Method", "dynamic": false, "info": "Determine how your content is matched: Vector finds semantic similarity, and Hybrid Search (suggested) combines both approaches with a reranker.", "name": "search_method", "options": ["Hybrid Search", "Vector Search"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Vector Search"}, "search_query": {"_input_type": "QueryInput", "advanced": false, "display_name": "Search Query", "dynamic": false, "info": "Enter a query to run a similarity search.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "search_query", "placeholder": "Enter a query...", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "query", "value": ""}, "search_score_threshold": {"_input_type": "FloatInput", "advanced": true, "display_name": "Search Score Threshold", "dynamic": false, "info": "Minimum similarity score threshold for search results. (when using 'Similarity with score threshold')", "list": false, "list_add_label": "Add More", "name": "search_score_threshold", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": 0}, "search_type": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Search Type", "dynamic": false, "info": "Search type to use", "name": "search_type", "options": ["Similarity", "Similarity with score threshold", "MMR (Max Marginal Relevance)"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Similarity"}, "should_cache_vector_store": {"_input_type": "BoolInput", "advanced": true, "display_name": "Cache Vector Store", "dynamic": false, "info": "If True, the vector store will be cached for the current build of the component. This is useful for components that have multiple output methods and want to share the same vector store.", "list": false, "list_add_label": "Add More", "name": "should_cache_vector_store", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "token": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Astra DB Application Token", "dynamic": false, "info": "Authentication token for accessing Astra DB.", "input_types": [], "load_from_db": true, "name": "token", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_APPLICATION_TOKEN"}}, "tool_mode": false}, "selected_output": "search_results", "showNode": true, "type": "AstraDB"}, "dragging": false, "id": "AstraDB-W6NB4", "measured": {"height": 502, "width": 320}, "position": {"x": 2060.799531746744, "y": 1507.872099528214}, "selected": false, "type": "genericNode"}, {"data": {"id": "File-vusZ2", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Loads content from one or more files as a DataFrame.", "display_name": "File", "documentation": "", "edited": false, "field_order": ["path", "file_path", "separator", "silent_errors", "delete_server_file_after_processing", "ignore_unsupported_extensions", "ignore_unspecified_files", "use_multithreading", "concurrency_multithreading"], "frozen": false, "icon": "file-text", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Raw Content", "group_outputs": false, "method": "load_files_message", "name": "message", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from copy import deepcopy\nfrom typing import Any\n\nfrom langflow.base.data.base_file import BaseFileComponent\nfrom langflow.base.data.utils import TEXT_FILE_TYPES, parallel_load_data, parse_text_file_to_data\nfrom langflow.io import BoolInput, FileInput, IntInput, Output\nfrom langflow.schema.data import Data\n\n\nclass FileComponent(BaseFileComponent):\n    \"\"\"Handles loading and processing of individual or zipped text files.\n\n    This component supports processing multiple valid files within a zip archive,\n    resolving paths, validating file types, and optionally using multithreading for processing.\n    \"\"\"\n\n    display_name = \"File\"\n    description = \"Loads content from one or more files.\"\n    documentation: str = \"https://docs.langflow.org/components-data#file\"\n    icon = \"file-text\"\n    name = \"File\"\n\n    VALID_EXTENSIONS = TEXT_FILE_TYPES\n\n    _base_inputs = deepcopy(BaseFileComponent._base_inputs)\n\n    for input_item in _base_inputs:\n        if isinstance(input_item, FileInput) and input_item.name == \"path\":\n            input_item.real_time_refresh = True\n            break\n\n    inputs = [\n        *_base_inputs,\n        BoolInput(\n            name=\"use_multithreading\",\n            display_name=\"[Deprecated] Use Multithreading\",\n            advanced=True,\n            value=True,\n            info=\"Set 'Processing Concurrency' greater than 1 to enable multithreading.\",\n        ),\n        IntInput(\n            name=\"concurrency_multithreading\",\n            display_name=\"Processing Concurrency\",\n            advanced=True,\n            info=\"When multiple files are being processed, the number of files to process concurrently.\",\n            value=1,\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Raw Content\", name=\"message\", method=\"load_files_message\"),\n    ]\n\n    def update_outputs(self, frontend_node: dict, field_name: str, field_value: Any) -> dict:\n        \"\"\"Dynamically show only the relevant output based on the number of files processed.\"\"\"\n        if field_name == \"path\":\n            # Add outputs based on the number of files in the path\n            if len(field_value) == 0:\n                return frontend_node\n\n            frontend_node[\"outputs\"] = []\n\n            if len(field_value) == 1:\n                # We need to check if the file is structured content\n                file_path = frontend_node[\"template\"][\"path\"][\"file_path\"][0]\n                if file_path.endswith((\".csv\", \".xlsx\", \".parquet\")):\n                    frontend_node[\"outputs\"].append(\n                        Output(display_name=\"Structured Content\", name=\"dataframe\", method=\"load_files_structured\"),\n                    )\n                elif file_path.endswith(\".json\"):\n                    frontend_node[\"outputs\"].append(\n                        Output(display_name=\"Structured Content\", name=\"json\", method=\"load_files_json\"),\n                    )\n\n                # All files get the raw content and path outputs\n                frontend_node[\"outputs\"].append(\n                    Output(display_name=\"Raw Content\", name=\"message\", method=\"load_files_message\"),\n                )\n                frontend_node[\"outputs\"].append(\n                    Output(display_name=\"File Path\", name=\"path\", method=\"load_files_path\"),\n                )\n            else:\n                # For multiple files, we only show the files output\n                frontend_node[\"outputs\"].append(\n                    Output(display_name=\"Files\", name=\"dataframe\", method=\"load_files\"),\n                )\n\n        return frontend_node\n\n    def process_files(self, file_list: list[BaseFileComponent.BaseFile]) -> list[BaseFileComponent.BaseFile]:\n        \"\"\"Processes files either sequentially or in parallel, depending on concurrency settings.\n\n        Args:\n            file_list (list[BaseFileComponent.BaseFile]): List of files to process.\n\n        Returns:\n            list[BaseFileComponent.BaseFile]: Updated list of files with merged data.\n        \"\"\"\n\n        def process_file(file_path: str, *, silent_errors: bool = False) -> Data | None:\n            \"\"\"Processes a single file and returns its Data object.\"\"\"\n            try:\n                return parse_text_file_to_data(file_path, silent_errors=silent_errors)\n            except FileNotFoundError as e:\n                msg = f\"File not found: {file_path}. Error: {e}\"\n                self.log(msg)\n                if not silent_errors:\n                    raise\n                return None\n            except Exception as e:\n                msg = f\"Unexpected error processing {file_path}: {e}\"\n                self.log(msg)\n                if not silent_errors:\n                    raise\n                return None\n\n        if not file_list:\n            msg = \"No files to process.\"\n            raise ValueError(msg)\n\n        concurrency = 1 if not self.use_multithreading else max(1, self.concurrency_multithreading)\n        file_count = len(file_list)\n\n        parallel_processing_threshold = 2\n        if concurrency < parallel_processing_threshold or file_count < parallel_processing_threshold:\n            if file_count > 1:\n                self.log(f\"Processing {file_count} files sequentially.\")\n            processed_data = [process_file(str(file.path), silent_errors=self.silent_errors) for file in file_list]\n        else:\n            self.log(f\"Starting parallel processing of {file_count} files with concurrency: {concurrency}.\")\n            file_paths = [str(file.path) for file in file_list]\n            processed_data = parallel_load_data(\n                file_paths,\n                silent_errors=self.silent_errors,\n                load_function=process_file,\n                max_concurrency=concurrency,\n            )\n\n        # Use rollup_basefile_data to merge processed data with BaseFile objects\n        return self.rollup_data(file_list, processed_data)\n"}, "concurrency_multithreading": {"_input_type": "IntInput", "advanced": true, "display_name": "Processing Concurrency", "dynamic": false, "info": "When multiple files are being processed, the number of files to process concurrently.", "list": false, "list_add_label": "Add More", "name": "concurrency_multithreading", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "delete_server_file_after_processing": {"_input_type": "BoolInput", "advanced": true, "display_name": "Delete Server File After Processing", "dynamic": false, "info": "If true, the Server File Path will be deleted after processing.", "list": false, "list_add_label": "Add More", "name": "delete_server_file_after_processing", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "file_path": {"_input_type": "HandleInput", "advanced": true, "display_name": "Server File Path", "dynamic": false, "info": "Data object with a 'file_path' property pointing to server file or a Message object with a path to the file. Supercedes 'Path' but supports same file types.", "input_types": ["Data", "Message"], "list": true, "list_add_label": "Add More", "name": "file_path", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "ignore_unspecified_files": {"_input_type": "BoolInput", "advanced": true, "display_name": "Ignore Unspecified Files", "dynamic": false, "info": "If true, Data with no 'file_path' property will be ignored.", "list": false, "list_add_label": "Add More", "name": "ignore_unspecified_files", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "ignore_unsupported_extensions": {"_input_type": "BoolInput", "advanced": true, "display_name": "Ignore Unsupported Extensions", "dynamic": false, "info": "If true, files with unsupported extensions will not be processed.", "list": false, "list_add_label": "Add More", "name": "ignore_unsupported_extensions", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "path": {"_input_type": "FileInput", "advanced": false, "display_name": "Files", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "zip", "tar", "tgz", "bz2", "gz"], "file_path": [], "info": "Supported file extensions: txt, md, mdx, csv, json, yaml, yml, xml, html, htm, pdf, docx, py, sh, sql, js, ts, tsx; optionally bundled in file extensions: zip, tar, tgz, bz2, gz", "list": true, "list_add_label": "Add More", "name": "path", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "temp_file": false, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "separator": {"_input_type": "StrInput", "advanced": true, "display_name": "Separator", "dynamic": false, "info": "Specify the separator to use between multiple outputs in Message format.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "separator", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n\n"}, "silent_errors": {"_input_type": "BoolInput", "advanced": true, "display_name": "Silent Errors", "dynamic": false, "info": "If true, errors will not raise an exception.", "list": false, "list_add_label": "Add More", "name": "silent_errors", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "use_multithreading": {"_input_type": "BoolInput", "advanced": true, "display_name": "[Deprecated] Use Multithreading", "dynamic": false, "info": "Set 'Processing Concurrency' greater than 1 to enable multithreading.", "list": false, "list_add_label": "Add More", "name": "use_multithreading", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}}, "tool_mode": false}, "showNode": true, "type": "File"}, "dragging": false, "id": "File-vusZ2", "measured": {"height": 230, "width": 320}, "position": {"x": 1330.7650978046952, "y": 1431.5905495627503}, "selected": false, "type": "genericNode"}, {"data": {"id": "LanguageModelComponent-1uhUK", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Runs a language model given a specified provider. ", "display_name": "Language Model", "documentation": "", "edited": false, "field_order": ["provider", "model_name", "api_key", "input_value", "system_message", "stream", "temperature"], "frozen": false, "icon": "brain-circuit", "legacy": false, "metadata": {"keywords": ["model", "llm", "language model", "large language model"]}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Model Response", "group_outputs": false, "method": "text_response", "name": "text_output", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Language Model", "group_outputs": false, "method": "build_model", "name": "model_output", "options": null, "required_inputs": null, "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "priority": 0, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "Model Provider API key", "input_types": [], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nfrom langchain_anthropic import Cha<PERSON><PERSON><PERSON><PERSON>\nfrom langchain_google_genai import ChatGoogleGenerativeAI\nfrom langchain_openai import ChatOpenAI\n\nfrom langflow.base.models.anthropic_constants import ANTHROPIC_MODELS\nfrom langflow.base.models.google_generative_ai_constants import GOOGLE_GENERATIVE_AI_MODELS\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_CHAT_MODEL_NAMES, OPENAI_REASONING_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs.inputs import BoolInput\nfrom langflow.io import DropdownInput, MessageInput, MultilineInput, SecretStrInput, SliderInput\nfrom langflow.schema.dotdict import dotdict\n\n\nclass LanguageModelComponent(LCModelComponent):\n    display_name = \"Language Model\"\n    description = \"Runs a language model given a specified provider.\"\n    documentation: str = \"https://docs.langflow.org/components-models\"\n    icon = \"brain-circuit\"\n    category = \"models\"\n    priority = 0  # Set priority to 0 to make it appear first\n\n    inputs = [\n        DropdownInput(\n            name=\"provider\",\n            display_name=\"Model Provider\",\n            options=[\"OpenAI\", \"Anthropic\", \"Google\"],\n            value=\"OpenAI\",\n            info=\"Select the model provider\",\n            real_time_refresh=True,\n            options_metadata=[{\"icon\": \"OpenAI\"}, {\"icon\": \"Anthropic\"}, {\"icon\": \"GoogleGenerativeAI\"}],\n        ),\n        DropdownInput(\n            name=\"model_name\",\n            display_name=\"Model Name\",\n            options=OPENAI_CHAT_MODEL_NAMES + OPENAI_REASONING_MODEL_NAMES,\n            value=OPENAI_CHAT_MODEL_NAMES[0],\n            info=\"Select the model to use\",\n            real_time_refresh=True,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API Key\",\n            info=\"Model Provider API key\",\n            required=False,\n            show=True,\n            real_time_refresh=True,\n        ),\n        MessageInput(\n            name=\"input_value\",\n            display_name=\"Input\",\n            info=\"The input text to send to the model\",\n        ),\n        MultilineInput(\n            name=\"system_message\",\n            display_name=\"System Message\",\n            info=\"A system message that helps set the behavior of the assistant\",\n            advanced=False,\n        ),\n        BoolInput(\n            name=\"stream\",\n            display_name=\"Stream\",\n            info=\"Whether to stream the response\",\n            value=False,\n            advanced=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"Temperature\",\n            value=0.1,\n            info=\"Controls randomness in responses\",\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:\n        provider = self.provider\n        model_name = self.model_name\n        temperature = self.temperature\n        stream = self.stream\n\n        if provider == \"OpenAI\":\n            if not self.api_key:\n                msg = \"OpenAI API key is required when using OpenAI provider\"\n                raise ValueError(msg)\n\n            if model_name in OPENAI_REASONING_MODEL_NAMES:\n                # reasoning models do not support temperature (yet)\n                temperature = None\n\n            return ChatOpenAI(\n                model_name=model_name,\n                temperature=temperature,\n                streaming=stream,\n                openai_api_key=self.api_key,\n            )\n        if provider == \"Anthropic\":\n            if not self.api_key:\n                msg = \"Anthropic API key is required when using Anthropic provider\"\n                raise ValueError(msg)\n            return ChatAnthropic(\n                model=model_name,\n                temperature=temperature,\n                streaming=stream,\n                anthropic_api_key=self.api_key,\n            )\n        if provider == \"Google\":\n            if not self.api_key:\n                msg = \"Google API key is required when using Google provider\"\n                raise ValueError(msg)\n            return ChatGoogleGenerativeAI(\n                model=model_name,\n                temperature=temperature,\n                streaming=stream,\n                google_api_key=self.api_key,\n            )\n        msg = f\"Unknown provider: {provider}\"\n        raise ValueError(msg)\n\n    def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None) -> dotdict:\n        if field_name == \"provider\":\n            if field_value == \"OpenAI\":\n                build_config[\"model_name\"][\"options\"] = OPENAI_CHAT_MODEL_NAMES + OPENAI_REASONING_MODEL_NAMES\n                build_config[\"model_name\"][\"value\"] = OPENAI_CHAT_MODEL_NAMES[0]\n                build_config[\"api_key\"][\"display_name\"] = \"OpenAI API Key\"\n            elif field_value == \"Anthropic\":\n                build_config[\"model_name\"][\"options\"] = ANTHROPIC_MODELS\n                build_config[\"model_name\"][\"value\"] = ANTHROPIC_MODELS[0]\n                build_config[\"api_key\"][\"display_name\"] = \"Anthropic API Key\"\n            elif field_value == \"Google\":\n                build_config[\"model_name\"][\"options\"] = GOOGLE_GENERATIVE_AI_MODELS\n                build_config[\"model_name\"][\"value\"] = GOOGLE_GENERATIVE_AI_MODELS[0]\n                build_config[\"api_key\"][\"display_name\"] = \"Google API Key\"\n        elif field_name == \"model_name\" and field_value.startswith(\"o1\") and self.provider == \"OpenAI\":\n            # Hide system_message for o1 models - currently unsupported\n            if \"system_message\" in build_config:\n                build_config[\"system_message\"][\"show\"] = False\n        elif field_name == \"model_name\" and not field_value.startswith(\"o1\") and \"system_message\" in build_config:\n            build_config[\"system_message\"][\"show\"] = True\n        return build_config\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "Input", "dynamic": false, "info": "The input text to send to the model", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "Select the model to use", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o-mini"}, "provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Provider", "dynamic": false, "info": "Select the model provider", "name": "provider", "options": ["OpenAI", "Anthropic", "Google"], "options_metadata": [{"icon": "OpenAI"}, {"icon": "Anthropic"}, {"icon": "GoogleGenerativeAI"}], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "OpenAI"}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "Stream", "dynamic": false, "info": "Whether to stream the response", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": true, "copy_field": false, "display_name": "System Message", "dynamic": false, "info": "A system message that helps set the behavior of the assistant", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "Temperature", "dynamic": false, "info": "Controls randomness in responses", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}}, "tool_mode": false}, "selected_output": "text_output", "showNode": true, "type": "LanguageModelComponent"}, "dragging": false, "id": "LanguageModelComponent-1uhUK", "measured": {"height": 451, "width": 320}, "position": {"x": 2354.7612483129965, "y": 633.8261067248878}, "selected": false, "type": "genericNode"}], "viewport": {"x": -22.84629031494228, "y": -151.44728538879235, "zoom": 0.45963552948592706}}, "description": "Load your data for chat context with Retrieval Augmented Generation.", "endpoint_name": null, "id": "b65cafc6-9f8e-4137-ad2b-4c663822f512", "is_component": false, "last_tested_version": "1.4.3", "name": "Vector Store RAG", "tags": ["openai", "astradb", "rag", "q-a"]}