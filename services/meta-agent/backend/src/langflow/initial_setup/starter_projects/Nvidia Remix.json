{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-8XmJU", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "Agent-9dON7", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-8XmJU{œdataTypeœ:œChatInputœ,œidœ:œChatInput-8XmJUœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-Agent-9dON7{œfieldNameœ:œinput_valueœ,œidœ:œAgent-9dON7œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-8XmJU", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-8XmJUœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "Agent-9dON7", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œAgent-9dON7œ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Agent", "id": "Agent-9dON7", "name": "response", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-o3obj", "inputTypes": ["Data", "DataFrame", "Message"], "type": "other"}}, "id": "reactflow__edge-Agent-9dON7{œdataTypeœ:œAgentœ,œidœ:œAgent-9dON7œ,œnameœ:œresponseœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-o3obj{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-o3objœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œotherœ}", "selected": false, "source": "Agent-9dON7", "sourceHandle": "{œdataTypeœ: œAgentœ, œidœ: œAgent-9dON7œ, œnameœ: œresponseœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-o3obj", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-o3objœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-PrjbV", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "system_prompt", "id": "Agent-9dON7", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-PrjbV{œdataTypeœ:œPromptœ,œidœ:œPrompt-PrjbVœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-Agent-9dON7{œfieldNameœ:œsystem_promptœ,œidœ:œAgent-9dON7œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-PrjbV", "sourceHandle": "{œdataTypeœ: œPromptœ, œidœ: œPrompt-PrjbVœ, œnameœ: œpromptœ, œoutput_typesœ: [œMessageœ]}", "target": "Agent-9dON7", "targetHandle": "{œfieldNameœ: œsystem_promptœ, œidœ: œAgent-9dON7œ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "RemixDocumentation", "id": "RemixDocumentation-DEIws", "name": "dataframe_output", "output_types": ["DataFrame"]}, "targetHandle": {"fieldName": "ingest_data", "id": "FAISS-Uz8O4", "inputTypes": ["Data", "DataFrame"], "type": "other"}}, "id": "reactflow__edge-RemixDocumentation-DEIws{œdataTypeœ:œRemixDocumentationœ,œidœ:œRemixDocumentation-DEIwsœ,œnameœ:œdataframe_outputœ,œoutput_typesœ:[œDataFrameœ]}-FAISS-Uz8O4{œfieldNameœ:œingest_dataœ,œidœ:œFAISS-Uz8O4œ,œinputTypesœ:[œDataœ,œDataFrameœ],œtypeœ:œotherœ}", "selected": false, "source": "RemixDocumentation-DEIws", "sourceHandle": "{œdataTypeœ: œRemixDocumentationœ, œidœ: œRemixDocumentation-DEIwsœ, œnameœ: œdataframe_outputœ, œoutput_typesœ: [œDataFrameœ]}", "target": "FAISS-Uz8O4", "targetHandle": "{œfieldNameœ: œingest_dataœ, œidœ: œFAISS-Uz8O4œ, œinputTypesœ: [œDataœ, œDataFrameœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "EmbeddingModel", "id": "EmbeddingModel-X6URU", "name": "embeddings", "output_types": ["Embeddings"]}, "targetHandle": {"fieldName": "embedding", "id": "FAISS-Uz8O4", "inputTypes": ["Embeddings"], "type": "other"}}, "id": "reactflow__edge-EmbeddingModel-X6URU{œdataTypeœ:œEmbeddingModelœ,œidœ:œEmbeddingModel-X6URUœ,œnameœ:œembeddingsœ,œoutput_typesœ:[œEmbeddingsœ]}-FAISS-Uz8O4{œfieldNameœ:œembeddingœ,œidœ:œFAISS-Uz8O4œ,œinputTypesœ:[œEmbeddingsœ],œtypeœ:œotherœ}", "selected": false, "source": "EmbeddingModel-X6URU", "sourceHandle": "{œdataTypeœ: œEmbeddingModelœ, œidœ: œEmbeddingModel-X6URUœ, œnameœ: œembeddingsœ, œoutput_typesœ: [œEmbeddingsœ]}", "target": "FAISS-Uz8O4", "targetHandle": "{œfieldNameœ: œembeddingœ, œidœ: œFAISS-Uz8O4œ, œinputTypesœ: [œEmbeddingsœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "FAISS", "id": "FAISS-Uz8O4", "name": "component_as_tool", "output_types": ["Tool"]}, "targetHandle": {"fieldName": "tools", "id": "Agent-9dON7", "inputTypes": ["Tool"], "type": "other"}}, "id": "reactflow__edge-FAISS-Uz8O4{œdataTypeœ:œFAISSœ,œidœ:œFAISS-Uz8O4œ,œnameœ:œcomponent_as_toolœ,œoutput_typesœ:[œToolœ]}-Agent-9dON7{œfieldNameœ:œtoolsœ,œidœ:œAgent-9dON7œ,œinputTypesœ:[œToolœ],œtypeœ:œotherœ}", "selected": false, "source": "FAISS-Uz8O4", "sourceHandle": "{œdataTypeœ: œFAISSœ, œidœ: œFAISS-Uz8O4œ, œnameœ: œcomponent_as_toolœ, œoutput_typesœ: [œToolœ]}", "target": "Agent-9dON7", "targetHandle": "{œfieldNameœ: œtoolsœ, œidœ: œAgent-9dON7œ, œinputTypesœ: [œToolœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "MCPTools", "id": "MCPTools-beHVJ", "name": "component_as_tool", "output_types": ["Tool"]}, "targetHandle": {"fieldName": "tools", "id": "Agent-9dON7", "inputTypes": ["Tool"], "type": "other"}}, "id": "reactflow__edge-MCPTools-beHVJ{œdataTypeœ:œMCPToolsœ,œidœ:œMCPTools-beHVJœ,œnameœ:œcomponent_as_toolœ,œoutput_typesœ:[œToolœ]}-Agent-9dON7{œfieldNameœ:œtoolsœ,œidœ:œAgent-9dON7œ,œinputTypesœ:[œToolœ],œtypeœ:œotherœ}", "selected": false, "source": "MCPTools-beHVJ", "sourceHandle": "{œdataTypeœ: œMCPToolsœ, œidœ: œMCPTools-beHVJœ, œnameœ: œcomponent_as_toolœ, œoutput_typesœ: [œToolœ]}", "target": "Agent-9dON7", "targetHandle": "{œfieldNameœ: œtoolsœ, œidœ: œAgent-9dON7œ, œinputTypesœ: [œToolœ], œtypeœ: œotherœ}"}], "nodes": [{"data": {"id": "ChatInput-8XmJU", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.4.2", "metadata": {"code_hash": "192913db3453", "module": "langflow.components.input_output.chat.ChatInput"}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Chat Message", "group_outputs": false, "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"Chat Input\"\n    description = \"Get chat inputs from the Playground.\"\n    documentation: str = \"https://docs.langflow.org/components-io#chat-input\"\n    icon = \"MessagesSquare\"\n    name = \"ChatInput\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Input Text\",\n            value=\"\",\n            info=\"Message to be passed as input.\",\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"Type of sender.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",\n            display_name=\"Files\",\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"Files to be sent with the message.\",\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Chat Message\", name=\"message\", method=\"message_response\"),\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"_input_type": "FileInput", "advanced": true, "display_name": "Files", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "Files to be sent with the message.", "list": true, "list_add_label": "Add More", "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "copy_field": false, "display_name": "Input Text", "dynamic": false, "info": "Message to be passed as input.", "input_types": [], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatInput"}, "dragging": false, "id": "ChatInput-8XmJU", "measured": {"height": 47, "width": 192}, "position": {"x": -210.69432682306316, "y": 315.16488151952666}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatOutput-o3obj", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color", "clean_data"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.4.2", "metadata": {"code_hash": "6f74e04e39d5", "module": "langflow.components.input_output.chat_output.ChatOutput"}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Output Message", "group_outputs": false, "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "Basic Clean Data", "dynamic": false, "info": "Whether to clean the data", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nimport orjson\nfrom fastapi.encoders import jsonable_encoder\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.helpers.data import safe_convert\nfrom langflow.inputs.inputs import BoolInput, DropdownInput, HandleInput, MessageTextInput\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.template.field.base import Output\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    documentation: str = \"https://docs.langflow.org/components-io#chat-output\"\n    icon = \"MessagesSquare\"\n    name = \"ChatOutput\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",\n            display_name=\"Inputs\",\n            info=\"Message to be passed as output.\",\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",\n            display_name=\"Basic Clean Data\",\n            value=True,\n            info=\"Whether to clean the data\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"Output Message\",\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _serialize_data(self, data: Data) -> str:\n        \"\"\"Serialize Data object to JSON string.\"\"\"\n        # Convert data.data to JSON-serializable format\n        serializable_data = jsonable_encoder(data.data)\n        # Serialize with orjson, enabling pretty printing with indentation\n        json_bytes = orjson.dumps(serializable_data, option=orjson.OPT_INDENT_2)\n        # Convert bytes to string and wrap in Markdown code blocks\n        return \"```json\\n\" + json_bytes.decode(\"utf-8\") + \"\\n```\"\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([safe_convert(item, clean_data=self.clean_data) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Data Template", "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "HandleInput", "advanced": false, "display_name": "Inputs", "dynamic": false, "info": "Message to be passed as output.", "input_types": ["Data", "DataFrame", "Message"], "list": false, "list_add_label": "Add More", "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatOutput"}, "dragging": false, "id": "ChatOutput-o3obj", "measured": {"height": 47, "width": 192}, "position": {"x": 431.32852283796484, "y": 385.1551996791115}, "selected": false, "type": "genericNode"}, {"data": {"id": "Agent-9dON7", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Define the agent's instructions, then enter a task to complete using tools.", "display_name": "Agent", "documentation": "", "edited": false, "field_order": ["agent_llm", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed", "max_retries", "timeout", "system_prompt", "tools", "input_value", "handle_parsing_errors", "verbose", "max_iterations", "agent_description", "memory", "sender", "sender_name", "n_messages", "session_id", "order", "template", "add_current_date_tool"], "frozen": false, "icon": "bot", "legacy": false, "lf_version": "1.4.2", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Response", "method": "message_response", "name": "response", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "add_current_date_tool": {"_input_type": "BoolInput", "advanced": true, "display_name": "Current Date", "dynamic": false, "info": "If true, will add a tool to the agent that returns the current date.", "list": false, "list_add_label": "Add More", "name": "add_current_date_tool", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "agent_description": {"_input_type": "MultilineInput", "advanced": true, "copy_field": false, "display_name": "Agent Description [Deprecated]", "dynamic": false, "info": "The description of the agent. This is only used when in Tool Mode. Defaults to 'A helpful assistant with access to the following tools:' and tools are added dynamically. This feature is deprecated and will be removed in future versions.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "agent_description", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "A helpful assistant with access to the following tools:"}, "agent_llm": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Provider", "dynamic": false, "info": "The provider of the language model that the agent will use to generate responses.", "input_types": [], "name": "agent_llm", "options": ["Amazon Bedrock", "Anthropic", "Azure OpenAI", "Google Generative AI", "Groq", "NVIDIA", "OpenAI", "<PERSON><PERSON><PERSON><PERSON>", "Custom"], "options_metadata": [{"icon": "Amazon"}, {"icon": "Anthropic"}, {"icon": "Azure"}, {"icon": "GoogleGenerativeAI"}, {"icon": "Groq"}, {"icon": "NVIDIA"}, {"icon": "OpenAI"}, {"icon": "<PERSON><PERSON><PERSON><PERSON>"}, {"icon": "brain"}], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "OpenAI"}, "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "The OpenAI API Key to use for the OpenAI model.", "input_types": [], "load_from_db": false, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import json\nimport re\n\nfrom langchain_core.tools import StructuredTool\n\nfrom langflow.base.agents.agent import LCToolsAgentComponent\nfrom langflow.base.agents.events import ExceptionWithMessageError\nfrom langflow.base.models.model_input_constants import (\n    ALL_PROVIDER_FIELDS,\n    MODEL_DYNAMIC_UPDATE_FIELDS,\n    MODEL_PROVIDERS,\n    MODEL_PROVIDERS_DICT,\n    MODELS_METADATA,\n)\nfrom langflow.base.models.model_utils import get_model_name\nfrom langflow.components.helpers.current_date import CurrentDateComponent\nfrom langflow.components.helpers.memory import MemoryComponent\nfrom langflow.components.langchain_utilities.tool_calling import ToolCallingAgentComponent\nfrom langflow.custom.custom_component.component import _get_component_toolkit\nfrom langflow.custom.utils import update_component_build_config\nfrom langflow.field_typing import Tool\nfrom langflow.io import BoolInput, DropdownInput, IntInput, MultilineInput, Output\nfrom langflow.logging import logger\nfrom langflow.schema.data import Data\nfrom langflow.schema.dotdict import dotdict\nfrom langflow.schema.message import Message\n\n\ndef set_advanced_true(component_input):\n    component_input.advanced = True\n    return component_input\n\n\nMODEL_PROVIDERS_LIST = [\"Anthropic\", \"Google Generative AI\", \"Groq\", \"OpenAI\"]\n\n\nclass AgentComponent(ToolCallingAgentComponent):\n    display_name: str = \"Agent\"\n    description: str = \"Define the agent's instructions, then enter a task to complete using tools.\"\n    documentation: str = \"https://docs.langflow.org/agents\"\n    icon = \"bot\"\n    beta = False\n    name = \"Agent\"\n\n    memory_inputs = [set_advanced_true(component_input) for component_input in MemoryComponent().inputs]\n\n    # Filter out json_mode from OpenAI inputs since we handle structured output differently\n    openai_inputs_filtered = [\n        input_field\n        for input_field in MODEL_PROVIDERS_DICT[\"OpenAI\"][\"inputs\"]\n        if not (hasattr(input_field, \"name\") and input_field.name == \"json_mode\")\n    ]\n\n    inputs = [\n        DropdownInput(\n            name=\"agent_llm\",\n            display_name=\"Model Provider\",\n            info=\"The provider of the language model that the agent will use to generate responses.\",\n            options=[*MODEL_PROVIDERS_LIST, \"Custom\"],\n            value=\"OpenAI\",\n            real_time_refresh=True,\n            input_types=[],\n            options_metadata=[MODELS_METADATA[key] for key in MODEL_PROVIDERS_LIST] + [{\"icon\": \"brain\"}],\n        ),\n        *openai_inputs_filtered,\n        MultilineInput(\n            name=\"system_prompt\",\n            display_name=\"Agent Instructions\",\n            info=\"System Prompt: Initial instructions and context provided to guide the agent's behavior.\",\n            value=\"You are a helpful assistant that can use tools to answer questions and perform tasks.\",\n            advanced=False,\n        ),\n        IntInput(\n            name=\"n_messages\",\n            display_name=\"Number of Chat History Messages\",\n            value=100,\n            info=\"Number of chat history messages to retrieve.\",\n            advanced=True,\n            show=True,\n        ),\n        *LCToolsAgentComponent._base_inputs,\n        # removed memory inputs from agent component\n        # *memory_inputs,\n        BoolInput(\n            name=\"add_current_date_tool\",\n            display_name=\"Current Date\",\n            advanced=True,\n            info=\"If true, will add a tool to the agent that returns the current date.\",\n            value=True,\n        ),\n    ]\n    outputs = [\n        Output(name=\"response\", display_name=\"Response\", method=\"message_response\"),\n        Output(name=\"structured_response\", display_name=\"Structured Response\", method=\"json_response\", tool_mode=False),\n    ]\n\n    async def message_response(self) -> Message:\n        try:\n            # Get LLM model and validate\n            llm_model, display_name = self.get_llm()\n            if llm_model is None:\n                msg = \"No language model selected. Please choose a model to proceed.\"\n                raise ValueError(msg)\n            self.model_name = get_model_name(llm_model, display_name=display_name)\n\n            # Get memory data\n            self.chat_history = await self.get_memory_data()\n            if isinstance(self.chat_history, Message):\n                self.chat_history = [self.chat_history]\n\n            # Add current date tool if enabled\n            if self.add_current_date_tool:\n                if not isinstance(self.tools, list):  # type: ignore[has-type]\n                    self.tools = []\n                current_date_tool = (await CurrentDateComponent(**self.get_base_args()).to_toolkit()).pop(0)\n                if not isinstance(current_date_tool, StructuredTool):\n                    msg = \"CurrentDateComponent must be converted to a StructuredTool\"\n                    raise TypeError(msg)\n                self.tools.append(current_date_tool)\n            # note the tools are not required to run the agent, hence the validation removed.\n\n            # Set up and run agent\n            self.set(\n                llm=llm_model,\n                tools=self.tools or [],\n                chat_history=self.chat_history,\n                input_value=self.input_value,\n                system_prompt=self.system_prompt,\n            )\n            agent = self.create_agent_runnable()\n            result = await self.run_agent(agent)\n\n            # Store result for potential JSON output\n            self._agent_result = result\n            # return result\n\n        except (ValueError, TypeError, KeyError) as e:\n            logger.error(f\"{type(e).__name__}: {e!s}\")\n            raise\n        except ExceptionWithMessageError as e:\n            logger.error(f\"ExceptionWithMessageError occurred: {e}\")\n            raise\n        except Exception as e:\n            logger.error(f\"Unexpected error: {e!s}\")\n            raise\n        else:\n            return result\n\n    async def json_response(self) -> Data:\n        \"\"\"Convert agent response to structured JSON Data output.\"\"\"\n        # Run the regular message response first to get the result\n        if not hasattr(self, \"_agent_result\"):\n            await self.message_response()\n\n        result = self._agent_result\n\n        # Extract content from result\n        if hasattr(result, \"content\"):\n            content = result.content\n        elif hasattr(result, \"text\"):\n            content = result.text\n        else:\n            content = str(result)\n\n        # Try to parse as JSON\n        try:\n            json_data = json.loads(content)\n            return Data(data=json_data)\n        except json.JSONDecodeError:\n            # If it's not valid JSON, try to extract JSON from the content\n            json_match = re.search(r\"\\{.*\\}\", content, re.DOTALL)\n            if json_match:\n                try:\n                    json_data = json.loads(json_match.group())\n                    return Data(data=json_data)\n                except json.JSONDecodeError:\n                    pass\n\n            # If we can't extract JSON, return the raw content as data\n            return Data(data={\"content\": content, \"error\": \"Could not parse as JSON\"})\n\n    async def get_memory_data(self):\n        # TODO: This is a temporary fix to avoid message duplication. We should develop a function for this.\n        messages = (\n            await MemoryComponent(**self.get_base_args())\n            .set(session_id=self.graph.session_id, order=\"Ascending\", n_messages=self.n_messages)\n            .retrieve_messages()\n        )\n        return [\n            message for message in messages if getattr(message, \"id\", None) != getattr(self.input_value, \"id\", None)\n        ]\n\n    def get_llm(self):\n        if not isinstance(self.agent_llm, str):\n            return self.agent_llm, None\n\n        try:\n            provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n            if not provider_info:\n                msg = f\"Invalid model provider: {self.agent_llm}\"\n                raise ValueError(msg)\n\n            component_class = provider_info.get(\"component_class\")\n            display_name = component_class.display_name\n            inputs = provider_info.get(\"inputs\")\n            prefix = provider_info.get(\"prefix\", \"\")\n\n            return self._build_llm_model(component_class, inputs, prefix), display_name\n\n        except Exception as e:\n            logger.error(f\"Error building {self.agent_llm} language model: {e!s}\")\n            msg = f\"Failed to initialize language model: {e!s}\"\n            raise ValueError(msg) from e\n\n    def _build_llm_model(self, component, inputs, prefix=\"\"):\n        model_kwargs = {}\n        for input_ in inputs:\n            if hasattr(self, f\"{prefix}{input_.name}\"):\n                model_kwargs[input_.name] = getattr(self, f\"{prefix}{input_.name}\")\n        return component.set(**model_kwargs).build_model()\n\n    def set_component_params(self, component):\n        provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n        if provider_info:\n            inputs = provider_info.get(\"inputs\")\n            prefix = provider_info.get(\"prefix\")\n            # Filter out json_mode and only use attributes that exist on this component\n            model_kwargs = {}\n            for input_ in inputs:\n                if hasattr(self, f\"{prefix}{input_.name}\"):\n                    model_kwargs[input_.name] = getattr(self, f\"{prefix}{input_.name}\")\n\n            return component.set(**model_kwargs)\n        return component\n\n    def delete_fields(self, build_config: dotdict, fields: dict | list[str]) -> None:\n        \"\"\"Delete specified fields from build_config.\"\"\"\n        for field in fields:\n            build_config.pop(field, None)\n\n    def update_input_types(self, build_config: dotdict) -> dotdict:\n        \"\"\"Update input types for all fields in build_config.\"\"\"\n        for key, value in build_config.items():\n            if isinstance(value, dict):\n                if value.get(\"input_types\") is None:\n                    build_config[key][\"input_types\"] = []\n            elif hasattr(value, \"input_types\") and value.input_types is None:\n                value.input_types = []\n        return build_config\n\n    async def update_build_config(\n        self, build_config: dotdict, field_value: str, field_name: str | None = None\n    ) -> dotdict:\n        # Iterate over all providers in the MODEL_PROVIDERS_DICT\n        # Existing logic for updating build_config\n        if field_name in (\"agent_llm\",):\n            build_config[\"agent_llm\"][\"value\"] = field_value\n            provider_info = MODEL_PROVIDERS_DICT.get(field_value)\n            if provider_info:\n                component_class = provider_info.get(\"component_class\")\n                if component_class and hasattr(component_class, \"update_build_config\"):\n                    # Call the component class's update_build_config method\n                    build_config = await update_component_build_config(\n                        component_class, build_config, field_value, \"model_name\"\n                    )\n\n            provider_configs: dict[str, tuple[dict, list[dict]]] = {\n                provider: (\n                    MODEL_PROVIDERS_DICT[provider][\"fields\"],\n                    [\n                        MODEL_PROVIDERS_DICT[other_provider][\"fields\"]\n                        for other_provider in MODEL_PROVIDERS_DICT\n                        if other_provider != provider\n                    ],\n                )\n                for provider in MODEL_PROVIDERS_DICT\n            }\n            if field_value in provider_configs:\n                fields_to_add, fields_to_delete = provider_configs[field_value]\n\n                # Delete fields from other providers\n                for fields in fields_to_delete:\n                    self.delete_fields(build_config, fields)\n\n                # Add provider-specific fields\n                if field_value == \"OpenAI\" and not any(field in build_config for field in fields_to_add):\n                    build_config.update(fields_to_add)\n                else:\n                    build_config.update(fields_to_add)\n                # Reset input types for agent_llm\n                build_config[\"agent_llm\"][\"input_types\"] = []\n            elif field_value == \"Custom\":\n                # Delete all provider fields\n                self.delete_fields(build_config, ALL_PROVIDER_FIELDS)\n                # Update with custom component\n                custom_component = DropdownInput(\n                    name=\"agent_llm\",\n                    display_name=\"Language Model\",\n                    options=[*sorted(MODEL_PROVIDERS), \"Custom\"],\n                    value=\"Custom\",\n                    real_time_refresh=True,\n                    input_types=[\"LanguageModel\"],\n                    options_metadata=[MODELS_METADATA[key] for key in sorted(MODELS_METADATA.keys())]\n                    + [{\"icon\": \"brain\"}],\n                )\n                build_config.update({\"agent_llm\": custom_component.to_dict()})\n            # Update input types for all fields\n            build_config = self.update_input_types(build_config)\n\n            # Validate required keys\n            default_keys = [\n                \"code\",\n                \"_type\",\n                \"agent_llm\",\n                \"tools\",\n                \"input_value\",\n                \"add_current_date_tool\",\n                \"system_prompt\",\n                \"agent_description\",\n                \"max_iterations\",\n                \"handle_parsing_errors\",\n                \"verbose\",\n            ]\n            missing_keys = [key for key in default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n        if (\n            isinstance(self.agent_llm, str)\n            and self.agent_llm in MODEL_PROVIDERS_DICT\n            and field_name in MODEL_DYNAMIC_UPDATE_FIELDS\n        ):\n            provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n            if provider_info:\n                component_class = provider_info.get(\"component_class\")\n                component_class = self.set_component_params(component_class)\n                prefix = provider_info.get(\"prefix\")\n                if component_class and hasattr(component_class, \"update_build_config\"):\n                    # Call each component class's update_build_config method\n                    # remove the prefix from the field_name\n                    if isinstance(field_name, str) and isinstance(prefix, str):\n                        field_name = field_name.replace(prefix, \"\")\n                    build_config = await update_component_build_config(\n                        component_class, build_config, field_value, \"model_name\"\n                    )\n        return dotdict({k: v.to_dict() if hasattr(v, \"to_dict\") else v for k, v in build_config.items()})\n\n    async def _get_tools(self) -> list[Tool]:\n        component_toolkit = _get_component_toolkit()\n        tools_names = self._build_tools_names()\n        agent_description = self.get_tool_description()\n        # TODO: Agent Description Depreciated Feature to be removed\n        description = f\"{agent_description}{tools_names}\"\n        tools = component_toolkit(component=self).get_tools(\n            tool_name=\"Call_Agent\", tool_description=description, callbacks=self.get_langchain_callbacks()\n        )\n        if hasattr(self, \"tools_metadata\"):\n            tools = component_toolkit(component=self, metadata=self.tools_metadata).update_tools_metadata(tools=tools)\n        return tools\n"}, "handle_parsing_errors": {"_input_type": "BoolInput", "advanced": true, "display_name": "<PERSON><PERSON> Parse Errors", "dynamic": false, "info": "Should the Agent fix errors when reading user input for better processing?", "list": false, "list_add_label": "Add More", "name": "handle_parsing_errors", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "input_value": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Input", "dynamic": false, "info": "The input provided by the user for the agent to process.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON Mode", "dynamic": false, "info": "If True, it will output JSON regardless of passing a schema.", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_iterations": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Iterations", "dynamic": false, "info": "The maximum number of attempts the agent can make to complete its task before it stops.", "list": false, "list_add_label": "Add More", "name": "max_iterations", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 15}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Retries", "dynamic": false, "info": "The maximum number of retries to make when generating.", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON>", "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "memory": {"_input_type": "HandleInput", "advanced": true, "display_name": "External Memory", "dynamic": false, "info": "Retrieve messages from an external memory. If empty, it will use the Langflow tables.", "input_types": ["Memory"], "list": false, "list_add_label": "Add More", "name": "memory", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "Model Kwargs", "dynamic": false, "info": "Additional keyword arguments to pass to the model.", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "To see the model names, first choose a provider. Then, enter your API key and click the refresh button next to the model name.", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo", "o1"], "options_metadata": [], "placeholder": "", "real_time_refresh": false, "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "n_messages": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Messages", "dynamic": false, "info": "Number of messages to retrieve.", "list": false, "list_add_label": "Add More", "name": "n_messages", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 100}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API Base", "dynamic": false, "info": "The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "order": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Order", "dynamic": false, "info": "Order of the messages.", "name": "order", "options": ["Ascending", "Descending"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": true, "trace_as_metadata": true, "type": "str", "value": "Ascending"}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "Seed", "dynamic": false, "info": "The seed controls the reproducibility of the job.", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Sender Type", "dynamic": false, "info": "Filter by sender type.", "name": "sender", "options": ["Machine", "User", "Machine and User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine and User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Filter by sender name.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "system_prompt": {"_input_type": "MultilineInput", "advanced": false, "copy_field": false, "display_name": "Agent Instructions", "dynamic": false, "info": "System Prompt: Initial instructions and context provided to guide the agent's behavior.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_prompt", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "You are a helpful assistant that must use tools to answer questions and perform tasks regarding RTX Remix.\n\nBefore "}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "Temperature", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "template": {"_input_type": "MultilineInput", "advanced": true, "copy_field": false, "display_name": "Template", "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {sender} or any other key in the message data.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{sender_name}: {text}"}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "Timeout", "dynamic": false, "info": "The timeout for requests to OpenAI completion API.", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}, "tools": {"_input_type": "HandleInput", "advanced": false, "display_name": "Tools", "dynamic": false, "info": "These are the tools that the agent can use to help with tasks.", "input_types": ["Tool"], "list": true, "list_add_label": "Add More", "name": "tools", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "verbose": {"_input_type": "BoolInput", "advanced": true, "display_name": "Verbose", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "verbose", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}}, "tool_mode": false}, "showNode": true, "type": "Agent"}, "dragging": false, "id": "Agent-9dON7", "measured": {"height": 591, "width": 320}, "position": {"x": 78.6911094428821, "y": -159.83908350054932}, "selected": false, "type": "genericNode"}, {"data": {"id": "Prompt-PrjbV", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": ["remix_documentation", "remix_mcp_server", "mcp_prompts"]}, "description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "documentation": "", "edited": false, "error": null, "field_order": ["template", "tool_placeholder"], "frozen": false, "full_path": null, "icon": "prompts", "is_composition": null, "is_input": null, "is_output": null, "legacy": false, "lf_version": "1.4.2", "metadata": {}, "minimized": false, "name": "", "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Prompt Message", "method": "build_prompt", "name": "prompt", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "priority": null, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"Prompt\"\n    description: str = \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"\n    trace_type = \"prompt\"\n    name = \"Prompt\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"Template\"),\n        MessageTextInput(\n            name=\"tool_placeholder\",\n            display_name=\"Tool Placeholder\",\n            tool_mode=True,\n            advanced=True,\n            info=\"A placeholder input for tool mode.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Prompt Message\", name=\"prompt\", method=\"build_prompt\"),\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "mcp_prompts": {"advanced": false, "display_name": "mcp_prompts", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "mcp_prompts", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "remix_documentation": {"advanced": false, "display_name": "remix_documentation", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "remix_documentation", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": "FAISS"}, "remix_mcp_server": {"advanced": false, "display_name": "remix_mcp_server", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "remix_mcp_server", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": "RTX Remix Toolkit Connection"}, "template": {"_input_type": "PromptInput", "advanced": false, "display_name": "Template", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "prompt", "value": "# Background\n\nYou are a helpful assistant that must use tools to answer questions and perform tasks regarding RTX Remix.\n\n# Procedure\n\n- You MUST ABSOLUTELY ALWAYS search through the documentation before responding to the user. Follow the instructions in the \"Documentation Instructions\" section for instruction on using the documentation.\n- When the user asks how to do something, always assume they are asking about the RTX Remix Toolkit or Runtime Graphic User Interfaces unless specified otherwise (DO NOT mention the tools available to you).\n- If the user requested you take an action or provide information about the current project, you may follow the instructions in the \"RTX Remix Toolkit Instructions\" section.\n\n# Documentation Instructions\n\n- DO NOT ATTEMPT, UNDER ANY CIRCUMSTANCES TO PROVIDE AN ANSWER BASED ON YOUR TRAINING DATA.\n- Use the {remix_documentation} tool to fetch the requested information from the RTX Remix documentation.\n- Always provide the source you used to get to the answer provided to the user.\n- If the user asks a question that can be ambiguous, request that they provide more information.\n- If the user asks a question that cannot be answered using the documentation, simply say so and don't attempt to answer the question.\n\n# RTX Remix Toolkit Instructions\n\n- Use the tools provided by the \"{remix_mcp_server}\" MCP Server to interact with the RTX Remix Toolkit.\n- Always verify if a recipe provided in the \"MCP Server Recipes\" sub-section matches the current user request. \n    - If a recipe matches, follow the instructions as closely as possible\n    - If NO recipe matches the user request, try to use any combination of tools to complete the user's request\n\n## MCP Server Recipes\n\n{mcp_prompts}"}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Tool Placeholder", "dynamic": false, "info": "A placeholder input for tool mode.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "Prompt"}, "dragging": false, "id": "Prompt-PrjbV", "measured": {"height": 529, "width": 320}, "position": {"x": -338.00443250004696, "y": -753.7815399447686}, "selected": false, "type": "genericNode"}, {"data": {"id": "RemixDocumentation-DEIws", "node": {"base_classes": ["Data", "DataFrame"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Fetch information from the NVIDIA RTX Remix documentation", "display_name": "RTX Remix Documentation", "documentation": "https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/index.html", "edited": true, "field_order": ["exclude_paths"], "frozen": false, "icon": "NVIDIA", "legacy": false, "lf_version": "1.4.2", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "DataFrame", "hidden": false, "method": "fetch_documentation_dataframe", "name": "dataframe_output", "options": null, "required_inputs": null, "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Data", "hidden": null, "method": "fetch_documentation_data", "name": "data_output", "options": null, "required_inputs": null, "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import httpx\r\nimport json\r\nimport re\r\nfrom langflow.custom import Component\r\nfrom langflow.io import Output, MessageTextInput\r\nfrom langflow.schema import DataFrame, Data\r\n\r\n\r\nclass RemixDocumentation(Component):\r\n    display_name = \"RTX Remix Documentation\"\r\n    description = \"Fetch information from the NVIDIA RTX Remix documentation\"\r\n    documentation: str = (\r\n        \"https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/index.html\"\r\n    )\r\n    icon = \"NVIDIA\"\r\n    name = \"RemixDocumentation\"\r\n\r\n    inputs = [\r\n        MessageTextInput(\r\n            name=\"exclude_paths\",\r\n            display_name=\"Exclude Paths\",\r\n            info=(\r\n                \"List of path prefixes to exclude from results. \"\r\n                \"Used to filter out sections of the documentation that are not typically relevant to user queries.\"\r\n            ),\r\n            is_list=True,\r\n            value=[\r\n                \"source\",\r\n                \"CHANGELOG.html\",\r\n                \"docs/changelog\",\r\n                \"docs/contributing/api.html\",\r\n            ],\r\n        ),\r\n    ]\r\n\r\n    outputs = [\r\n        Output(\r\n            display_name=\"DataFrame\",\r\n            name=\"dataframe_output\",\r\n            method=\"fetch_documentation_dataframe\",\r\n        ),\r\n        Output(\r\n            display_name=\"Data\", name=\"data_output\", method=\"fetch_documentation_data\"\r\n        ),\r\n    ]\r\n\r\n    _BASE_URL = \"https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/\"\r\n\r\n    def _fetch_all_documentation(self) -> list[Data]:\r\n        \"\"\"Fetch all documentation entries and return as list of Data objects.\"\"\"\r\n        # URL for the search index JavaScript file\r\n        search_index_url = self._BASE_URL + \"searchindex.js\"\r\n\r\n        # Fetch the search index file\r\n        response = httpx.get(search_index_url, follow_redirects=True)\r\n        response.raise_for_status()\r\n\r\n        # Extract the JSON data from the JavaScript file\r\n        # The file contains: const searchData = {...};\r\n        js_content = response.text\r\n\r\n        # Extract the JSON part using regex\r\n        match = re.search(r\"const searchData = ({.*});\", js_content, re.DOTALL)\r\n        if not match:\r\n            raise ValueError(\"Could not parse search index data\")\r\n\r\n        # Parse the JSON data\r\n        search_data = json.loads(match.group(1))\r\n\r\n        # Get the data array which contains all searchable items\r\n        data_items = search_data.get(\"data\", [])\r\n\r\n        results = []\r\n        for item in data_items:\r\n            filename = item.get(\"filename\", \"\")\r\n            text = item.get(\"content\", \"\")\r\n\r\n            if not text:\r\n                continue\r\n\r\n            # Check if filename should be excluded\r\n            should_exclude = False\r\n            for exclude_path in self.exclude_paths:\r\n                if filename.startswith(exclude_path):\r\n                    should_exclude = True\r\n                    break\r\n\r\n            if should_exclude:\r\n                continue\r\n\r\n            # Build URL with proper handling of empty anchors\r\n            anchor = item.get(\"anchor\", \"\")\r\n            if anchor:\r\n                url = f\"{self._BASE_URL}{filename}#{anchor}\"\r\n            else:\r\n                url = f\"{self._BASE_URL}{filename}\"\r\n\r\n            results.append(\r\n                Data(\r\n                    title=item.get(\"display_name\", \"\"),\r\n                    text=text,\r\n                    url=url,\r\n                )\r\n            )\r\n\r\n        # If no results found, create a single entry indicating no results\r\n        if not results:\r\n            data = Data(\r\n                title=\"No documentation found\",\r\n                text=\"No documentation entries were found in the search index\",\r\n                url=search_index_url,\r\n            )\r\n            results.append(data)\r\n\r\n        return results\r\n\r\n    def fetch_documentation_dataframe(self) -> DataFrame:\r\n        \"\"\"Fetch documentation and return as DataFrame.\"\"\"\r\n        results = self._fetch_all_documentation()\r\n        data_frame = DataFrame(results)\r\n        self.status = data_frame\r\n        return data_frame\r\n\r\n    def fetch_documentation_data(self) -> list[Data]:\r\n        \"\"\"Fetch documentation and return as list of Data objects.\"\"\"\r\n        results = self._fetch_all_documentation()\r\n        self.status = results\r\n        return results\r\n"}, "exclude_paths": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Exclude Paths", "dynamic": false, "info": "List of path prefixes to exclude from results. Used to filter out sections of the documentation that are not typically relevant to user queries.", "input_types": ["Message"], "list": true, "list_add_label": "Add More", "load_from_db": false, "name": "exclude_paths", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ["source", "docs/changelog", "CHANGELOG.html", "docs/contributing/api.html"]}}, "tool_mode": false}, "showNode": true, "type": "RemixDocumentation"}, "dragging": false, "id": "RemixDocumentation-DEIws", "measured": {"height": 373, "width": 320}, "position": {"x": -783.4316223978514, "y": -373.75276518828326}, "selected": false, "type": "genericNode"}, {"data": {"description": "Generate embeddings using a specified provider.", "display_name": "Embedding Model", "id": "EmbeddingModel-X6URU", "node": {"base_classes": ["Embeddings"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Generate embeddings using a specified provider.", "display_name": "Embedding Model", "documentation": "", "edited": false, "field_order": ["provider", "model", "api_key", "api_base", "dimensions", "chunk_size", "request_timeout", "max_retries", "show_progress_bar", "model_kwargs"], "frozen": false, "icon": "binary", "legacy": false, "lf_version": "1.4.2", "metadata": {"code_hash": "93faf11517da", "module": "langflow.components.models.embedding_model.EmbeddingModelComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Embedding Model", "group_outputs": false, "method": "build_embeddings", "name": "embeddings", "selected": "Embeddings", "tool_mode": true, "types": ["Embeddings"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "api_base": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "API Base URL", "dynamic": false, "info": "Base URL for the API. Leave empty for default.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "Model Provider API key", "input_types": [], "load_from_db": false, "name": "api_key", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": ""}, "chunk_size": {"_input_type": "IntInput", "advanced": true, "display_name": "Chunk Size", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nfrom langchain_openai import OpenAIEmbeddings\n\nfrom langflow.base.embeddings.model import LCEmbeddingsModel\nfrom langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES\nfrom langflow.field_typing import Embeddings\nfrom langflow.io import (\n    BoolInput,\n    DictInput,\n    DropdownInput,\n    FloatInput,\n    IntInput,\n    MessageTextInput,\n    SecretStrInput,\n)\nfrom langflow.schema.dotdict import dotdict\n\n\nclass EmbeddingModelComponent(LCEmbeddingsModel):\n    display_name = \"Embedding Model\"\n    description = \"Generate embeddings using a specified provider.\"\n    documentation: str = \"https://docs.langflow.org/components-embedding-models\"\n    icon = \"binary\"\n    name = \"EmbeddingModel\"\n    category = \"models\"\n\n    inputs = [\n        DropdownInput(\n            name=\"provider\",\n            display_name=\"Model Provider\",\n            options=[\"OpenAI\"],\n            value=\"OpenAI\",\n            info=\"Select the embedding model provider\",\n            real_time_refresh=True,\n            options_metadata=[{\"icon\": \"OpenAI\"}],\n        ),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"Model Name\",\n            options=OPENAI_EMBEDDING_MODEL_NAMES,\n            value=OPENAI_EMBEDDING_MODEL_NAMES[0],\n            info=\"Select the embedding model to use\",\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API Key\",\n            info=\"Model Provider API key\",\n            required=True,\n            show=True,\n            real_time_refresh=True,\n        ),\n        MessageTextInput(\n            name=\"api_base\",\n            display_name=\"API Base URL\",\n            info=\"Base URL for the API. Leave empty for default.\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"dimensions\",\n            display_name=\"Dimensions\",\n            info=\"The number of dimensions the resulting output embeddings should have. \"\n            \"Only supported by certain models.\",\n            advanced=True,\n        ),\n        IntInput(name=\"chunk_size\", display_name=\"Chunk Size\", advanced=True, value=1000),\n        FloatInput(name=\"request_timeout\", display_name=\"Request Timeout\", advanced=True),\n        IntInput(name=\"max_retries\", display_name=\"Max Retries\", advanced=True, value=3),\n        BoolInput(name=\"show_progress_bar\", display_name=\"Show Progress Bar\", advanced=True),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"Model Kwargs\",\n            advanced=True,\n            info=\"Additional keyword arguments to pass to the model.\",\n        ),\n    ]\n\n    def build_embeddings(self) -> Embeddings:\n        provider = self.provider\n        model = self.model\n        api_key = self.api_key\n        api_base = self.api_base\n        dimensions = self.dimensions\n        chunk_size = self.chunk_size\n        request_timeout = self.request_timeout\n        max_retries = self.max_retries\n        show_progress_bar = self.show_progress_bar\n        model_kwargs = self.model_kwargs or {}\n\n        if provider == \"OpenAI\":\n            if not api_key:\n                msg = \"OpenAI API key is required when using OpenAI provider\"\n                raise ValueError(msg)\n            return OpenAIEmbeddings(\n                model=model,\n                dimensions=dimensions or None,\n                base_url=api_base or None,\n                api_key=api_key,\n                chunk_size=chunk_size,\n                max_retries=max_retries,\n                timeout=request_timeout or None,\n                show_progress_bar=show_progress_bar,\n                model_kwargs=model_kwargs,\n            )\n        msg = f\"Unknown provider: {provider}\"\n        raise ValueError(msg)\n\n    def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None) -> dotdict:\n        if field_name == \"provider\" and field_value == \"OpenAI\":\n            build_config[\"model\"][\"options\"] = OPENAI_EMBEDDING_MODEL_NAMES\n            build_config[\"model\"][\"value\"] = OPENAI_EMBEDDING_MODEL_NAMES[0]\n            build_config[\"api_key\"][\"display_name\"] = \"OpenAI API Key\"\n            build_config[\"api_base\"][\"display_name\"] = \"OpenAI API Base URL\"\n        return build_config\n"}, "dimensions": {"_input_type": "IntInput", "advanced": true, "display_name": "Dimensions", "dynamic": false, "info": "The number of dimensions the resulting output embeddings should have. Only supported by certain models.", "list": false, "list_add_label": "Add More", "name": "dimensions", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Retries", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 3}, "model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "Select the embedding model to use", "name": "model", "options": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text-embedding-3-small"}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "Model Kwargs", "dynamic": false, "info": "Additional keyword arguments to pass to the model.", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Provider", "dynamic": false, "info": "Select the embedding model provider", "name": "provider", "options": ["OpenAI"], "options_metadata": [{"icon": "OpenAI"}], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "OpenAI"}, "request_timeout": {"_input_type": "FloatInput", "advanced": true, "display_name": "Request Timeout", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "request_timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": ""}, "show_progress_bar": {"_input_type": "BoolInput", "advanced": true, "display_name": "Show Progress Bar", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "show_progress_bar", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}}, "tool_mode": false}, "showNode": true, "type": "EmbeddingModel"}, "dragging": false, "id": "EmbeddingModel-X6URU", "measured": {"height": 375, "width": 320}, "position": {"x": -781.7797027471986, "y": 80.22604713786116}, "selected": false, "type": "genericNode"}, {"data": {"id": "FAISS-Uz8O4", "node": {"base_classes": ["Data", "DataFrame"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "FAISS Vector Store with search capabilities", "display_name": "FAISS", "documentation": "", "edited": false, "field_order": ["index_name", "persist_directory", "ingest_data", "search_query", "should_cache_vector_store", "allow_dangerous_deserialization", "embedding", "number_of_results"], "frozen": false, "icon": "FAISS", "legacy": false, "lf_version": "1.4.2", "metadata": {"code_hash": "ed38680af3a6", "module": "langflow.components.vectorstores.faiss.FaissVectorStoreComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Toolset", "hidden": false, "method": "to_toolkit", "name": "component_as_tool", "options": null, "required_inputs": null, "selected": "Tool", "tool_mode": true, "types": ["Tool"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "allow_dangerous_deserialization": {"_input_type": "BoolInput", "advanced": true, "display_name": "Allow Dangerous Deserialization", "dynamic": false, "info": "Set to True to allow loading pickle files from untrusted sources. Only enable this if you trust the source of the data.", "list": false, "list_add_label": "Add More", "name": "allow_dangerous_deserialization", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from pathlib import Path\n\nfrom langchain_community.vectorstores import FAISS\n\nfrom langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store\nfrom langflow.helpers.data import docs_to_data\nfrom langflow.io import BoolInput, HandleInput, IntInput, StrInput\nfrom langflow.schema.data import Data\n\n\nclass FaissVectorStoreComponent(LCVectorStoreComponent):\n    \"\"\"FAISS Vector Store with search capabilities.\"\"\"\n\n    display_name: str = \"FAISS\"\n    description: str = \"FAISS Vector Store with search capabilities\"\n    name = \"FAISS\"\n    icon = \"FAISS\"\n\n    inputs = [\n        StrInput(\n            name=\"index_name\",\n            display_name=\"Index Name\",\n            value=\"langflow_index\",\n        ),\n        StrInput(\n            name=\"persist_directory\",\n            display_name=\"Persist Directory\",\n            info=\"Path to save the FAISS index. It will be relative to where Langflow is running.\",\n        ),\n        *LCVectorStoreComponent.inputs,\n        BoolInput(\n            name=\"allow_dangerous_deserialization\",\n            display_name=\"Allow Dangerous Deserialization\",\n            info=\"Set to True to allow loading pickle files from untrusted sources. \"\n            \"Only enable this if you trust the source of the data.\",\n            advanced=True,\n            value=True,\n        ),\n        HandleInput(name=\"embedding\", display_name=\"Embedding\", input_types=[\"Embeddings\"]),\n        IntInput(\n            name=\"number_of_results\",\n            display_name=\"Number of Results\",\n            info=\"Number of results to return.\",\n            advanced=True,\n            value=4,\n        ),\n    ]\n\n    @staticmethod\n    def resolve_path(path: str) -> str:\n        \"\"\"Resolve the path relative to the Langflow root.\n\n        Args:\n            path: The path to resolve\n        Returns:\n            str: The resolved path as a string\n        \"\"\"\n        return str(Path(path).resolve())\n\n    def get_persist_directory(self) -> Path:\n        \"\"\"Returns the resolved persist directory path or the current directory if not set.\"\"\"\n        if self.persist_directory:\n            return Path(self.resolve_path(self.persist_directory))\n        return Path()\n\n    @check_cached_vector_store\n    def build_vector_store(self) -> FAISS:\n        \"\"\"Builds the FAISS object.\"\"\"\n        path = self.get_persist_directory()\n        path.mkdir(parents=True, exist_ok=True)\n\n        # Convert DataFrame to Data if needed using parent's method\n        self.ingest_data = self._prepare_ingest_data()\n\n        documents = []\n        for _input in self.ingest_data or []:\n            if isinstance(_input, Data):\n                documents.append(_input.to_lc_document())\n            else:\n                documents.append(_input)\n\n        faiss = FAISS.from_documents(documents=documents, embedding=self.embedding)\n        faiss.save_local(str(path), self.index_name)\n        return faiss\n\n    def search_documents(self) -> list[Data]:\n        \"\"\"Search for documents in the FAISS vector store.\"\"\"\n        path = self.get_persist_directory()\n        index_path = path / f\"{self.index_name}.faiss\"\n\n        if not index_path.exists():\n            vector_store = self.build_vector_store()\n        else:\n            vector_store = FAISS.load_local(\n                folder_path=str(path),\n                embeddings=self.embedding,\n                index_name=self.index_name,\n                allow_dangerous_deserialization=self.allow_dangerous_deserialization,\n            )\n\n        if not vector_store:\n            msg = \"Failed to load the FAISS index.\"\n            raise ValueError(msg)\n\n        if self.search_query and isinstance(self.search_query, str) and self.search_query.strip():\n            docs = vector_store.similarity_search(\n                query=self.search_query,\n                k=self.number_of_results,\n            )\n            return docs_to_data(docs)\n        return []\n"}, "embedding": {"_input_type": "HandleInput", "advanced": false, "display_name": "Embedding", "dynamic": false, "info": "", "input_types": ["Embeddings"], "list": false, "list_add_label": "Add More", "name": "embedding", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "index_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Index Name", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "index_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "langflow_index"}, "ingest_data": {"_input_type": "HandleInput", "advanced": false, "display_name": "Ingest Data", "dynamic": false, "info": "", "input_types": ["Data", "DataFrame"], "list": true, "list_add_label": "Add More", "name": "ingest_data", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "number_of_results": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Results", "dynamic": false, "info": "Number of results to return.", "list": false, "list_add_label": "Add More", "name": "number_of_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 4}, "persist_directory": {"_input_type": "StrInput", "advanced": false, "display_name": "Persist Directory", "dynamic": false, "info": "Path to save the FAISS index. It will be relative to where Langflow is running.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "persist_directory", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "search_query": {"_input_type": "QueryInput", "advanced": false, "display_name": "Search Query", "dynamic": false, "info": "Enter a query to run a similarity search.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "search_query", "placeholder": "Enter a query...", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "query", "value": ""}, "should_cache_vector_store": {"_input_type": "BoolInput", "advanced": true, "display_name": "Cache Vector Store", "dynamic": false, "info": "If True, the vector store will be cached for the current build of the component. This is useful for components that have multiple output methods and want to share the same vector store.", "list": false, "list_add_label": "Add More", "name": "should_cache_vector_store", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "tools_metadata": {"_input_type": "ToolsInput", "advanced": false, "display_name": "Actions", "dynamic": false, "info": "Modify tool names and descriptions to help agents understand when to use each tool.", "is_list": true, "list_add_label": "Add More", "name": "tools_metadata", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "tools", "value": [{"args": {"search_query": {"default": "", "description": "Enter a query to run a similarity search.", "title": "Search Query", "type": "string"}}, "description": "FAISS. search_documents - FAISS Vector Store with search capabilities", "display_description": "FAISS. search_documents - FAISS Vector Store with search capabilities", "display_name": "search_documents", "name": "search_documents", "readonly": false, "status": true, "tags": ["search_documents"]}, {"args": {"search_query": {"default": "", "description": "Enter a query to run a similarity search.", "title": "Search Query", "type": "string"}}, "description": "FAISS. as_dataframe - FAISS Vector Store with search capabilities", "display_description": "FAISS. as_dataframe - FAISS Vector Store with search capabilities", "display_name": "as_dataframe", "name": "as_dataframe", "readonly": false, "status": true, "tags": ["as_dataframe"]}]}}, "tool_mode": true}, "showNode": true, "type": "FAISS"}, "dragging": false, "id": "FAISS-Uz8O4", "measured": {"height": 503, "width": 320}, "position": {"x": -344.50128361428034, "y": -176.20680066416054}, "selected": false, "type": "genericNode"}, {"data": {"id": "note-HQL5D", "node": {"description": "# RTX Remix Langflow Integration\n\nThis Langflow project provides an integration for the NVIDIA RTX Remix Toolkit through its REST API.\n\n## Prerequisites\n\nBefore using this project, ensure you have completed the following steps:\n\n1. **Install RTX Remix Toolkit**\n   You must have the RTX Remix Toolkit installed on your system. Follow the installation guide here:\n   [Installing the RTX Remix Toolkit](https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/docs/installation/install-toolkit.html)\n\n2. **Run RTX Remix Toolkit**\n   Make sure the RTX Remix Toolkit application is running before using this Langflow project.\n\n3. **Create/Open a Project**\n   You must have an RTX Remix project opened within the Toolkit. Learn how to set up a project here:\n   [Setting Up a Project with the RTX Remix Toolkit](https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/docs/gettingstarted/learning-toolkitsetup.html)\n\n### Quick Start Tutorial\n\nTo quickly get started with RTX Remix, follow the [Building Your First Mod for the RTX Remix Sample](https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/docs/tutorials/tutorial-remixtool.html) tutorial.\n\nIt goes through the process of installing the various required parts, setting them up and getting a project up and running.\n\n## Getting Started\n\nOnce all prerequisites are met, the Langflow project should work without additional configuration.\n\n### Testing the Connection\n\nTo verify everything is working correctly:\n\n1. Open the Langflow project\n2. Locate the **RTX Remix MCP Connection** node\n3. Click the **refresh button** on the node\n4. Verify that the various REST API tools appear\n\nIf the REST API tools appear after refreshing, your connection to RTX Remix Toolkit is working properly and you can begin using the available tools.\n\n## Additional Resources\n\n- [RTX Remix Documentation](https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/)\n- [RTX Remix MCP Documentation](https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/docs/howto/learning-mcp.html)\n- [RTX Remix REST API Documentation](https://docs.omniverse.nvidia.com/kit/docs/rtx_remix/latest/docs/howto/learning-restapi.html)", "display_name": "", "documentation": "", "template": {"backgroundColor": "lime"}}, "type": "note"}, "dragging": false, "height": 1454, "id": "note-HQL5D", "measured": {"height": 1454, "width": 452}, "position": {"x": -1257.898918275863, "y": -634.0975399601093}, "resizing": false, "selected": false, "type": "noteNode", "width": 452}, {"data": {"id": "MCPTools-beHVJ", "node": {"base_classes": ["DataFrame"], "beta": false, "category": "data", "conditional_paths": [], "custom_fields": {}, "description": "Connect to an MCP server to use its tools.", "display_name": "MCP Connection", "documentation": "", "edited": false, "field_order": ["mode", "command", "env", "sse_url", "headers_input", "tool", "tool_placeholder"], "frozen": false, "icon": "Mcp", "key": "MCPTools", "legacy": false, "lf_version": "1.4.2", "metadata": {"code_hash": "d58eb6d2b3e7", "module": "langflow.components.agents.mcp_component.MCPToolsComponent"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Toolset", "hidden": false, "method": "to_toolkit", "name": "component_as_tool", "options": null, "required_inputs": null, "selected": "Tool", "tool_mode": true, "types": ["Tool"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.003932426697386162, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from __future__ import annotations\n\nimport asyncio\nimport uuid\nfrom typing import Any\n\nfrom langchain_core.tools import StructuredTool  # noqa: TC002\n\nfrom langflow.api.v2.mcp import get_server\nfrom langflow.base.agents.utils import maybe_unflatten_dict, safe_cache_get, safe_cache_set\nfrom langflow.base.mcp.util import (\n    MCPSseClient,\n    MCPStdioClient,\n    create_input_schema_from_json_schema,\n    update_tools,\n)\nfrom langflow.custom.custom_component.component_with_cache import ComponentWithCache\nfrom langflow.inputs.inputs import InputTypes  # noqa: TC001\nfrom langflow.io import DropdownInput, McpInput, MessageTextInput, Output\nfrom langflow.io.schema import flatten_schema, schema_to_langflow_inputs\nfrom langflow.logging import logger\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\nfrom langflow.services.auth.utils import create_user_longterm_token\n\n# Import get_server from the backend API\nfrom langflow.services.database.models.user.crud import get_user_by_id\nfrom langflow.services.deps import get_session, get_settings_service, get_storage_service\n\n\nclass MCPToolsComponent(ComponentWithCache):\n    schema_inputs: list = []\n    tools: list[StructuredTool] = []\n    _not_load_actions: bool = False\n    _tool_cache: dict = {}\n    _last_selected_server: str | None = None  # Cache for the last selected server\n\n    def __init__(self, **data) -> None:\n        super().__init__(**data)\n        # Initialize cache keys to avoid CacheMiss when accessing them\n        self._ensure_cache_structure()\n\n        # Initialize clients with access to the component cache\n        self.stdio_client: MCPStdioClient = MCPStdioClient(component_cache=self._shared_component_cache)\n        self.sse_client: MCPSseClient = MCPSseClient(component_cache=self._shared_component_cache)\n\n    def _ensure_cache_structure(self):\n        \"\"\"Ensure the cache has the required structure.\"\"\"\n        # Check if servers key exists and is not CacheMiss\n        servers_value = safe_cache_get(self._shared_component_cache, \"servers\")\n        if servers_value is None:\n            safe_cache_set(self._shared_component_cache, \"servers\", {})\n\n        # Check if last_selected_server key exists and is not CacheMiss\n        last_server_value = safe_cache_get(self._shared_component_cache, \"last_selected_server\")\n        if last_server_value is None:\n            safe_cache_set(self._shared_component_cache, \"last_selected_server\", \"\")\n\n    default_keys: list[str] = [\n        \"code\",\n        \"_type\",\n        \"tool_mode\",\n        \"tool_placeholder\",\n        \"mcp_server\",\n        \"tool\",\n    ]\n\n    display_name = \"MCP Tools\"\n    description = \"Connect to an MCP server to use its tools.\"\n    documentation: str = \"https://docs.langflow.org/mcp-client\"\n    icon = \"Mcp\"\n    name = \"MCPTools\"\n\n    inputs = [\n        McpInput(\n            name=\"mcp_server\",\n            display_name=\"MCP Server\",\n            info=\"Select the MCP Server that will be used by this component\",\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"tool\",\n            display_name=\"Tool\",\n            options=[],\n            value=\"\",\n            info=\"Select the tool to execute\",\n            show=False,\n            required=True,\n            real_time_refresh=True,\n        ),\n        MessageTextInput(\n            name=\"tool_placeholder\",\n            display_name=\"Tool Placeholder\",\n            info=\"Placeholder for the tool\",\n            value=\"\",\n            show=False,\n            tool_mode=False,\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Response\", name=\"response\", method=\"build_output\"),\n    ]\n\n    async def _validate_schema_inputs(self, tool_obj) -> list[InputTypes]:\n        \"\"\"Validate and process schema inputs for a tool.\"\"\"\n        try:\n            if not tool_obj or not hasattr(tool_obj, \"args_schema\"):\n                msg = \"Invalid tool object or missing input schema\"\n                raise ValueError(msg)\n\n            flat_schema = flatten_schema(tool_obj.args_schema.schema())\n            input_schema = create_input_schema_from_json_schema(flat_schema)\n            if not input_schema:\n                msg = f\"Empty input schema for tool '{tool_obj.name}'\"\n                raise ValueError(msg)\n\n            schema_inputs = schema_to_langflow_inputs(input_schema)\n            if not schema_inputs:\n                msg = f\"No input parameters defined for tool '{tool_obj.name}'\"\n                logger.warning(msg)\n                return []\n\n        except Exception as e:\n            msg = f\"Error validating schema inputs: {e!s}\"\n            logger.exception(msg)\n            raise ValueError(msg) from e\n        else:\n            return schema_inputs\n\n    async def update_tool_list(self, mcp_server_value=None):\n        # Accepts mcp_server_value as dict {name, config} or uses self.mcp_server\n        mcp_server = mcp_server_value if mcp_server_value is not None else getattr(self, \"mcp_server\", None)\n        server_name = None\n        server_config_from_value = None\n        if isinstance(mcp_server, dict):\n            server_name = mcp_server.get(\"name\")\n            server_config_from_value = mcp_server.get(\"config\")\n        else:\n            server_name = mcp_server\n        if not server_name:\n            self.tools = []\n            return [], {\"name\": server_name, \"config\": server_config_from_value}\n\n        # Use shared cache if available\n        servers_cache = safe_cache_get(self._shared_component_cache, \"servers\", {})\n        cached = servers_cache.get(server_name) if isinstance(servers_cache, dict) else None\n\n        if cached is not None:\n            self.tools = cached[\"tools\"]\n            self.tool_names = cached[\"tool_names\"]\n            self._tool_cache = cached[\"tool_cache\"]\n            server_config_from_value = cached[\"config\"]\n            return self.tools, {\"name\": server_name, \"config\": server_config_from_value}\n\n        try:\n            async for db in get_session():\n                user_id, _ = await create_user_longterm_token(db)\n                current_user = await get_user_by_id(db, user_id)\n\n                # Try to get server config from DB/API\n                server_config = await get_server(\n                    server_name,\n                    current_user,\n                    db,\n                    storage_service=get_storage_service(),\n                    settings_service=get_settings_service(),\n                )\n\n                # If get_server returns empty but we have a config, use it\n                if not server_config and server_config_from_value:\n                    server_config = server_config_from_value\n\n                if not server_config:\n                    self.tools = []\n                    return [], {\"name\": server_name, \"config\": server_config}\n\n                _, tool_list, tool_cache = await update_tools(\n                    server_name=server_name,\n                    server_config=server_config,\n                    mcp_stdio_client=self.stdio_client,\n                    mcp_sse_client=self.sse_client,\n                )\n\n                self.tool_names = [tool.name for tool in tool_list if hasattr(tool, \"name\")]\n                self._tool_cache = tool_cache\n                self.tools = tool_list\n                # Cache the result using shared cache\n                cache_data = {\n                    \"tools\": tool_list,\n                    \"tool_names\": self.tool_names,\n                    \"tool_cache\": tool_cache,\n                    \"config\": server_config,\n                }\n\n                # Safely update the servers cache\n                current_servers_cache = safe_cache_get(self._shared_component_cache, \"servers\", {})\n                if isinstance(current_servers_cache, dict):\n                    current_servers_cache[server_name] = cache_data\n                    safe_cache_set(self._shared_component_cache, \"servers\", current_servers_cache)\n\n                return tool_list, {\"name\": server_name, \"config\": server_config}\n        except (TimeoutError, asyncio.TimeoutError) as e:\n            msg = f\"Timeout updating tool list: {e!s}\"\n            logger.exception(msg)\n            raise TimeoutError(msg) from e\n        except Exception as e:\n            msg = f\"Error updating tool list: {e!s}\"\n            logger.exception(msg)\n            raise ValueError(msg) from e\n\n    async def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:\n        \"\"\"Toggle the visibility of connection-specific fields based on the selected mode.\"\"\"\n        try:\n            if field_name == \"tool\":\n                try:\n                    if len(self.tools) == 0:\n                        try:\n                            self.tools, build_config[\"mcp_server\"][\"value\"] = await self.update_tool_list()\n                            build_config[\"tool\"][\"options\"] = [tool.name for tool in self.tools]\n                            build_config[\"tool\"][\"placeholder\"] = \"Select a tool\"\n                        except (TimeoutError, asyncio.TimeoutError) as e:\n                            msg = f\"Timeout updating tool list: {e!s}\"\n                            logger.exception(msg)\n                            if not build_config[\"tools_metadata\"][\"show\"]:\n                                build_config[\"tool\"][\"show\"] = True\n                                build_config[\"tool\"][\"options\"] = []\n                                build_config[\"tool\"][\"value\"] = \"\"\n                                build_config[\"tool\"][\"placeholder\"] = \"Timeout on MCP server\"\n                            else:\n                                build_config[\"tool\"][\"show\"] = False\n                        except ValueError:\n                            if not build_config[\"tools_metadata\"][\"show\"]:\n                                build_config[\"tool\"][\"show\"] = True\n                                build_config[\"tool\"][\"options\"] = []\n                                build_config[\"tool\"][\"value\"] = \"\"\n                                build_config[\"tool\"][\"placeholder\"] = \"Error on MCP Server\"\n                            else:\n                                build_config[\"tool\"][\"show\"] = False\n\n                    if field_value == \"\":\n                        return build_config\n                    tool_obj = None\n                    for tool in self.tools:\n                        if tool.name == field_value:\n                            tool_obj = tool\n                            break\n                    if tool_obj is None:\n                        msg = f\"Tool {field_value} not found in available tools: {self.tools}\"\n                        logger.warning(msg)\n                        return build_config\n                    await self._update_tool_config(build_config, field_value)\n                except Exception as e:\n                    build_config[\"tool\"][\"options\"] = []\n                    msg = f\"Failed to update tools: {e!s}\"\n                    raise ValueError(msg) from e\n                else:\n                    return build_config\n            elif field_name == \"mcp_server\":\n                if not field_value:\n                    build_config[\"tool\"][\"show\"] = False\n                    build_config[\"tool\"][\"options\"] = []\n                    build_config[\"tool\"][\"value\"] = \"\"\n                    build_config[\"tool\"][\"placeholder\"] = \"\"\n                    build_config[\"tool_placeholder\"][\"tool_mode\"] = False\n                    self.remove_non_default_keys(build_config)\n                    return build_config\n\n                build_config[\"tool_placeholder\"][\"tool_mode\"] = True\n\n                current_server_name = field_value.get(\"name\") if isinstance(field_value, dict) else field_value\n                _last_selected_server = safe_cache_get(self._shared_component_cache, \"last_selected_server\", \"\")\n\n                # To avoid unnecessary updates, only proceed if the server has actually changed\n                if (_last_selected_server in (current_server_name, \"\")) and build_config[\"tool\"][\"show\"]:\n                    return build_config\n\n                # Determine if \"Tool Mode\" is active by checking if the tool dropdown is hidden.\n                is_in_tool_mode = build_config[\"tools_metadata\"][\"show\"]\n                safe_cache_set(self._shared_component_cache, \"last_selected_server\", current_server_name)\n\n                # Check if tools are already cached for this server before clearing\n                cached_tools = None\n                if current_server_name:\n                    servers_cache = safe_cache_get(self._shared_component_cache, \"servers\", {})\n                    if isinstance(servers_cache, dict):\n                        cached = servers_cache.get(current_server_name)\n                        if cached is not None:\n                            cached_tools = cached[\"tools\"]\n                            self.tools = cached_tools\n                            self.tool_names = cached[\"tool_names\"]\n                            self._tool_cache = cached[\"tool_cache\"]\n\n                # Only clear tools if we don't have cached tools for the current server\n                if not cached_tools:\n                    self.tools = []  # Clear previous tools only if no cache\n\n                self.remove_non_default_keys(build_config)  # Clear previous tool inputs\n\n                # Only show the tool dropdown if not in tool_mode\n                if not is_in_tool_mode:\n                    build_config[\"tool\"][\"show\"] = True\n                    if cached_tools:\n                        # Use cached tools to populate options immediately\n                        build_config[\"tool\"][\"options\"] = [tool.name for tool in cached_tools]\n                        build_config[\"tool\"][\"placeholder\"] = \"Select a tool\"\n                    else:\n                        # Show loading state only when we need to fetch tools\n                        build_config[\"tool\"][\"placeholder\"] = \"Loading tools...\"\n                        build_config[\"tool\"][\"options\"] = []\n                    build_config[\"tool\"][\"value\"] = uuid.uuid4()\n                else:\n                    # Keep the tool dropdown hidden if in tool_mode\n                    self._not_load_actions = True\n                    build_config[\"tool\"][\"show\"] = False\n\n            elif field_name == \"tool_mode\":\n                build_config[\"tool\"][\"placeholder\"] = \"\"\n                build_config[\"tool\"][\"show\"] = not bool(field_value) and bool(build_config[\"mcp_server\"])\n                self.remove_non_default_keys(build_config)\n                self.tool = build_config[\"tool\"][\"value\"]\n                if field_value:\n                    self._not_load_actions = True\n                else:\n                    build_config[\"tool\"][\"value\"] = uuid.uuid4()\n                    build_config[\"tool\"][\"options\"] = []\n                    build_config[\"tool\"][\"show\"] = True\n                    build_config[\"tool\"][\"placeholder\"] = \"Loading tools...\"\n            elif field_name == \"tools_metadata\":\n                self._not_load_actions = False\n\n        except Exception as e:\n            msg = f\"Error in update_build_config: {e!s}\"\n            logger.exception(msg)\n            raise ValueError(msg) from e\n        else:\n            return build_config\n\n    def get_inputs_for_all_tools(self, tools: list) -> dict:\n        \"\"\"Get input schemas for all tools.\"\"\"\n        inputs = {}\n        for tool in tools:\n            if not tool or not hasattr(tool, \"name\"):\n                continue\n            try:\n                flat_schema = flatten_schema(tool.args_schema.schema())\n                input_schema = create_input_schema_from_json_schema(flat_schema)\n                langflow_inputs = schema_to_langflow_inputs(input_schema)\n                inputs[tool.name] = langflow_inputs\n            except (AttributeError, ValueError, TypeError, KeyError) as e:\n                msg = f\"Error getting inputs for tool {getattr(tool, 'name', 'unknown')}: {e!s}\"\n                logger.exception(msg)\n                continue\n        return inputs\n\n    def remove_input_schema_from_build_config(\n        self, build_config: dict, tool_name: str, input_schema: dict[list[InputTypes], Any]\n    ):\n        \"\"\"Remove the input schema for the tool from the build config.\"\"\"\n        # Keep only schemas that don't belong to the current tool\n        input_schema = {k: v for k, v in input_schema.items() if k != tool_name}\n        # Remove all inputs from other tools\n        for value in input_schema.values():\n            for _input in value:\n                if _input.name in build_config:\n                    build_config.pop(_input.name)\n\n    def remove_non_default_keys(self, build_config: dict) -> None:\n        \"\"\"Remove non-default keys from the build config.\"\"\"\n        for key in list(build_config.keys()):\n            if key not in self.default_keys:\n                build_config.pop(key)\n\n    async def _update_tool_config(self, build_config: dict, tool_name: str) -> None:\n        \"\"\"Update tool configuration with proper error handling.\"\"\"\n        if not self.tools:\n            self.tools, build_config[\"mcp_server\"][\"value\"] = await self.update_tool_list()\n\n        if not tool_name:\n            return\n\n        tool_obj = next((tool for tool in self.tools if tool.name == tool_name), None)\n        if not tool_obj:\n            msg = f\"Tool {tool_name} not found in available tools: {self.tools}\"\n            self.remove_non_default_keys(build_config)\n            build_config[\"tool\"][\"value\"] = \"\"\n            logger.warning(msg)\n            return\n\n        try:\n            # Store current values before removing inputs\n            current_values = {}\n            for key, value in build_config.items():\n                if key not in self.default_keys and isinstance(value, dict) and \"value\" in value:\n                    current_values[key] = value[\"value\"]\n\n            # Get all tool inputs and remove old ones\n            input_schema_for_all_tools = self.get_inputs_for_all_tools(self.tools)\n            self.remove_input_schema_from_build_config(build_config, tool_name, input_schema_for_all_tools)\n\n            # Get and validate new inputs\n            self.schema_inputs = await self._validate_schema_inputs(tool_obj)\n            if not self.schema_inputs:\n                msg = f\"No input parameters to configure for tool '{tool_name}'\"\n                logger.info(msg)\n                return\n\n            # Add new inputs to build config\n            for schema_input in self.schema_inputs:\n                if not schema_input or not hasattr(schema_input, \"name\"):\n                    msg = \"Invalid schema input detected, skipping\"\n                    logger.warning(msg)\n                    continue\n\n                try:\n                    name = schema_input.name\n                    input_dict = schema_input.to_dict()\n                    input_dict.setdefault(\"value\", None)\n                    input_dict.setdefault(\"required\", True)\n\n                    build_config[name] = input_dict\n\n                    # Preserve existing value if the parameter name exists in current_values\n                    if name in current_values:\n                        build_config[name][\"value\"] = current_values[name]\n\n                except (AttributeError, KeyError, TypeError) as e:\n                    msg = f\"Error processing schema input {schema_input}: {e!s}\"\n                    logger.exception(msg)\n                    continue\n        except ValueError as e:\n            msg = f\"Schema validation error for tool {tool_name}: {e!s}\"\n            logger.exception(msg)\n            self.schema_inputs = []\n            return\n        except (AttributeError, KeyError, TypeError) as e:\n            msg = f\"Error updating tool config: {e!s}\"\n            logger.exception(msg)\n            raise ValueError(msg) from e\n\n    async def build_output(self) -> DataFrame:\n        \"\"\"Build output with improved error handling and validation.\"\"\"\n        try:\n            self.tools, _ = await self.update_tool_list()\n            if self.tool != \"\":\n                # Set session context for persistent MCP sessions using Langflow session ID\n                session_context = self._get_session_context()\n                if session_context:\n                    self.stdio_client.set_session_context(session_context)\n                    self.sse_client.set_session_context(session_context)\n\n                exec_tool = self._tool_cache[self.tool]\n                tool_args = self.get_inputs_for_all_tools(self.tools)[self.tool]\n                kwargs = {}\n                for arg in tool_args:\n                    value = getattr(self, arg.name, None)\n                    if value is not None:\n                        if isinstance(value, Message):\n                            kwargs[arg.name] = value.text\n                        else:\n                            kwargs[arg.name] = value\n\n                unflattened_kwargs = maybe_unflatten_dict(kwargs)\n\n                output = await exec_tool.coroutine(**unflattened_kwargs)\n\n                tool_content = []\n                for item in output.content:\n                    item_dict = item.model_dump()\n                    tool_content.append(item_dict)\n                return DataFrame(data=tool_content)\n            return DataFrame(data=[{\"error\": \"You must select a tool\"}])\n        except Exception as e:\n            msg = f\"Error in build_output: {e!s}\"\n            logger.exception(msg)\n            raise ValueError(msg) from e\n\n    def _get_session_context(self) -> str | None:\n        \"\"\"Get the Langflow session ID for MCP session caching.\"\"\"\n        # Try to get session ID from the component's execution context\n        if hasattr(self, \"graph\") and hasattr(self.graph, \"session_id\"):\n            session_id = self.graph.session_id\n            # Include server name to ensure different servers get different sessions\n            server_name = \"\"\n            mcp_server = getattr(self, \"mcp_server\", None)\n            if isinstance(mcp_server, dict):\n                server_name = mcp_server.get(\"name\", \"\")\n            elif mcp_server:\n                server_name = str(mcp_server)\n            return f\"{session_id}_{server_name}\" if session_id else None\n        return None\n\n    async def _get_tools(self):\n        \"\"\"Get cached tools or update if necessary.\"\"\"\n        mcp_server = getattr(self, \"mcp_server\", None)\n        if not self._not_load_actions:\n            tools, _ = await self.update_tool_list(mcp_server)\n            return tools\n        return []\n"}, "mcp_server": {"_input_type": "McpInput", "advanced": false, "display_name": "MCP Server", "dynamic": false, "info": "Select the MCP Server that will be used by this component", "name": "mcp_server", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "mcp", "value": {}}, "tool": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Tool", "dynamic": false, "info": "Select the tool to execute", "name": "tool", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": false, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "remix_lock_layer"}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Tool Placeholder", "dynamic": false, "info": "Placeholder for the tool", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": false, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "tools_metadata": {"_input_type": "ToolsInput", "advanced": false, "display_name": "Actions", "dynamic": false, "info": "Modify tool names and descriptions to help agents understand when to use each tool.", "is_list": true, "list_add_label": "Add More", "name": "tools_metadata", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "tools", "value": [{"args": {}, "description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_name": "remix__status_status_get", "name": "remix__status_status_get", "readonly": false, "status": false, "tags": ["remix__status_status_get"]}, {"args": {}, "description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_name": "remix__status_health_get", "name": "remix__status_health_get", "readonly": false, "status": false, "tags": ["remix__status_health_get"]}, {"args": {}, "description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_name": "remix__status_ready_get", "name": "remix__status_ready_get", "readonly": false, "status": false, "tags": ["remix__status_ready_get"]}, {"args": {}, "description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_description": "Returns the current status of the service.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`", "display_name": "remix__status_startup_get", "name": "remix__status_startup_get", "readonly": false, "status": false, "tags": ["remix__status_startup_get"]}, {"args": {"app_name": {"title": "App Name", "type": "string"}}, "description": " Async App Docs Endpoint\n\n\n**Query Parameters:**\n\n- **app_name** (Required): No description.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": " Async App Docs Endpoint\n\n\n**Query Parameters:**\n\n- **app_name** (Required): No description.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix__async_app_docs_endpoint_asyncapi_docs_get", "name": "remix__async_app_docs_endpoint_asyncapi_docs_g", "readonly": false, "status": false, "tags": ["remix__async_app_docs_endpoint_asyncapi_docs_get"]}, {"args": {"app_name": {"title": "App Name", "type": "string"}}, "description": " Async App Schema Endpoint\n\n\n**Query Parameters:**\n\n- **app_name** (Required): No description.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": " Async App Schema Endpoint\n\n\n**Query Parameters:**\n\n- **app_name** (Required): No description.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/json`\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix__async_app_schema_endpoint_asyncapi_schema_get", "name": "remix__async_app_schema_endpoint_asyncapi_sche", "readonly": false, "status": true, "tags": ["remix__async_app_schema_endpoint_asyncapi_schema_get"]}, {"args": {}, "description": "Get the currently loaded project if one is loaded.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layer_id**: The layer identifier (layer path for non-anonymous layers)\n\n  - **Example:**\n```json\n{\n  \"layer_id\": \"string\"\n}\n```", "display_description": "Get the currently loaded project if one is loaded.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layer_id**: The layer identifier (layer path for non-anonymous layers)\n\n  - **Example:**\n```json\n{\n  \"layer_id\": \"string\"\n}\n```", "display_name": "remix_get_loaded_project", "name": "remix_get_loaded_project", "readonly": false, "status": true, "tags": ["remix_get_loaded_project"]}, {"args": {"layer_id": {"description": "Project identifier for the project to open as project", "title": "Layer Id", "type": "string"}}, "description": "Open a project.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Project identifier for the project to open as project\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Open a project.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Project identifier for the project to open as project\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_open_project", "name": "remix_open_project", "readonly": false, "status": true, "tags": ["remix_open_project"]}, {"args": {"layer_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": -1, "description": "The number of layers to get per `layer_type`. If `layer_type` is not set this parameter will have no effect. Use -1 to get all the layers.", "title": "Layer Count"}, "layer_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "The type of layer to get. Filtering by layer type will ignore layer children.", "title": "Layer Types"}}, "description": "Get the layer tree in the current stage.\n\n\n**Query Parameters:**\n\n- **layer_types**: The type of layer to get. Filtering by layer type will ignore layer children.\n\n- **layer_count**: The number of layers to get per `layer_type`. If `layer_type` is not set this parameter will have no effect. Use -1 to get all the layers.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layers**: The list of layers in the layer stack\n\n  - **Example:**\n```json\n{\n  \"layers\": [\n    \"unknown_type\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get the layer tree in the current stage.\n\n\n**Query Parameters:**\n\n- **layer_types**: The type of layer to get. Filtering by layer type will ignore layer children.\n\n- **layer_count**: The number of layers to get per `layer_type`. If `layer_type` is not set this parameter will have no effect. Use -1 to get all the layers.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layers**: The list of layers in the layer stack\n\n  - **Example:**\n```json\n{\n  \"layers\": [\n    \"unknown_type\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_layers", "name": "remix_get_layers", "readonly": false, "status": true, "tags": ["remix_get_layers"]}, {"args": {"create_or_insert": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Whether to create a new layer or insert a sublayer", "title": "Create Or In<PERSON>t"}, "layer_path": {"description": "The path to the layer to create", "title": "Layer Path", "type": "string"}, "layer_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "If used, will set custom metadata for the layer type", "title": "Layer Type"}, "parent_layer_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Layer identifier (layer path for non-anonymous layers) for the layer to insert the sublayer into. If none, the root layer will be used", "title": "<PERSON><PERSON> Layer Id"}, "replace_existing": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Remove existing layers of type layer_type if set", "title": "Replace Existing"}, "set_edit_target": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Whether to set the layer as the edit target", "title": "Set Edit Target"}, "sublayer_position": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": -1, "description": "The position to insert the new layer at. Use -1 to insert at the end.", "title": "Sublayer Position"}}, "description": "Create a layer in the current stage.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Create a layer in the current stage.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_create_layer", "name": "remix_create_layer", "readonly": false, "status": true, "tags": ["remix_create_layer"]}, {"args": {"layer_id": {"description": "Layer identifier for the layer to get the sublayers from", "title": "Layer Id", "type": "string"}, "layer_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "The type of layer to get. Filtering by layer type will ignore layer children.", "title": "Layer Types"}}, "description": "Get the immediate sublayers of the given layer.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to get the sublayers from\n\n\n**Query Parameters:**\n\n- **layer_types**: The type of layer to get. Filtering by layer type will ignore layer children.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layers**: The list of layers in the layer stack\n\n  - **Example:**\n```json\n{\n  \"layers\": [\n    \"unknown_type\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get the immediate sublayers of the given layer.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to get the sublayers from\n\n\n**Query Parameters:**\n\n- **layer_types**: The type of layer to get. Filtering by layer type will ignore layer children.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layers**: The list of layers in the layer stack\n\n  - **Example:**\n```json\n{\n  \"layers\": [\n    \"unknown_type\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_sublayers", "name": "remix_get_sublayers", "readonly": false, "status": true, "tags": ["remix_get_sublayers"]}, {"args": {"layer_id": {"description": "Layer identifier for the layer to remove", "title": "Layer Id", "type": "string"}, "parent_layer_id": {"description": "Layer identifier (layer path for non-anonymous layers) for the parent layer of the layer to delete. If none, the root layer will be used", "title": "<PERSON><PERSON> Layer Id", "type": "string"}}, "description": "Remove a layer from the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to remove\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Remove a layer from the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to remove\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_remove_layer", "name": "remix_remove_layer", "readonly": false, "status": true, "tags": ["remix_remove_layer"]}, {"args": {"current_parent_layer_id": {"description": "Layer identifier (layer path for non-anonymous layers) for the layer to move", "title": "Current Parent Layer Id", "type": "string"}, "layer_id": {"description": "Layer identifier for the layer to move", "title": "Layer Id", "type": "string"}, "layer_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": -1, "description": "The position to insert the layer at. Use -1 to insert at the end.", "title": "Layer Index"}, "new_parent_layer_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Layer identifier (layer path for non-anonymous layers) for the new parent layer. If none, the layer will be moved to the root layer", "title": "New Parent Layer Id"}}, "description": "Move a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to move\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Move a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to move\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_move_layer", "name": "remix_move_layer", "readonly": false, "status": true, "tags": ["remix_move_layer"]}, {"args": {"layer_id": {"description": "Layer identifier for the layer to lock/unlock", "title": "Layer Id", "type": "string"}, "value": {"description": "Whether to lock the layer", "title": "Value", "type": "boolean"}}, "description": "Lock or unlock a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to lock/unlock\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Lock or unlock a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to lock/unlock\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_lock_layer", "name": "remix_lock_layer", "readonly": false, "status": true, "tags": ["remix_lock_layer"]}, {"args": {"layer_id": {"description": "Layer identifier for the layer to lock/unlock", "title": "Layer Id", "type": "string"}, "value": {"description": "Whether to mute the layer", "title": "Value", "type": "boolean"}}, "description": "Mute or unmute a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to lock/unlock\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Mute or unmute a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to lock/unlock\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_mute_layer", "name": "remix_mute_layer", "readonly": false, "status": true, "tags": ["remix_mute_layer"]}, {"args": {"layer_id": {"description": "Layer identifier for the layer to save", "title": "Layer Id", "type": "string"}}, "description": "Save a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to save\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Save a layer in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to save\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_save_layer", "name": "remix_save_layer", "readonly": false, "status": true, "tags": ["remix_save_layer"]}, {"args": {}, "description": "Get the active edit target in the current stage.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layer_id**: The layer identifier (layer path for non-anonymous layers)\n\n  - **Example:**\n```json\n{\n  \"layer_id\": \"string\"\n}\n```", "display_description": "Get the active edit target in the current stage.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layer_id**: The layer identifier (layer path for non-anonymous layers)\n\n  - **Example:**\n```json\n{\n  \"layer_id\": \"string\"\n}\n```", "display_name": "remix_get_edit_target_layer", "name": "remix_get_edit_target_layer", "readonly": false, "status": true, "tags": ["remix_get_edit_target_layer"]}, {"args": {"layer_id": {"description": "Layer identifier for the layer to set as edit target", "title": "Layer Id", "type": "string"}}, "description": "Set the active edit target in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to set as edit target\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Set the active edit target in the current stage.\n\n\n**Path Parameters:**\n\n- **layer_id** (Required): Layer identifier for the layer to set as edit target\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_set_edit_target_layer", "name": "remix_set_edit_target_layer", "readonly": false, "status": true, "tags": ["remix_set_edit_target_layer"]}, {"args": {}, "description": "Get the available layer types.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layer_types**: The types of layers available\n\n  - **Example:**\n```json\n{\n  \"layer_types\": [\n    \"string\"\n  ]\n}\n```", "display_description": "Get the available layer types.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **layer_types**: The types of layers available\n\n  - **Example:**\n```json\n{\n  \"layer_types\": [\n    \"string\"\n  ]\n}\n```", "display_name": "remix_get_layer_types", "name": "remix_get_layer_types", "readonly": false, "status": true, "tags": ["remix_get_layer_types"]}, {"args": {}, "description": "Get the list of available non-ingested model assets. This will use the default output directory for non-ingested model assets to list the available assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **file_paths**: List of paths pointing to files\n\n  - **Example:**\n```json\n{\n  \"file_paths\": [\n    \"string\"\n  ]\n}\n```", "display_description": "Get the list of available non-ingested model assets. This will use the default output directory for non-ingested model assets to list the available assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **file_paths**: List of paths pointing to files\n\n  - **Example:**\n```json\n{\n  \"file_paths\": [\n    \"string\"\n  ]\n}\n```", "display_name": "remix_get_available_model_assets", "name": "remix_get_available_model_assets", "readonly": false, "status": true, "tags": ["remix_get_available_model_assets"]}, {"args": {}, "description": "Get the list of available non-ingested texture assets. This will use the default output directory for non-ingested texture assets to list the available assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **file_paths**: List of paths pointing to files\n\n  - **Example:**\n```json\n{\n  \"file_paths\": [\n    \"string\"\n  ]\n}\n```", "display_description": "Get the list of available non-ingested texture assets. This will use the default output directory for non-ingested texture assets to list the available assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **file_paths**: List of paths pointing to files\n\n  - **Example:**\n```json\n{\n  \"file_paths\": [\n    \"string\"\n  ]\n}\n```", "display_name": "remix_get_available_texture_assets", "name": "remix_get_available_texture_assets", "readonly": false, "status": true, "tags": ["remix_get_available_texture_assets"]}, {"args": {"asset_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "A type of asset to filter the results by ('textures' or 'models')", "title": "Asset Type"}}, "description": "Get the list of available ingested assets. This will use the default output directory for ingested assets to list the available assets.\n\n\n**Query Parameters:**\n\n- **asset_type**: A type of asset to filter the results by ('textures' or 'models')\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **file_paths**: List of paths pointing to files\n\n  - **Example:**\n```json\n{\n  \"file_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get the list of available ingested assets. This will use the default output directory for ingested assets to list the available assets.\n\n\n**Query Parameters:**\n\n- **asset_type**: A type of asset to filter the results by ('textures' or 'models')\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **file_paths**: List of paths pointing to files\n\n  - **Example:**\n```json\n{\n  \"file_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_available_ingested_assets", "name": "remix_get_available_ingested_assets", "readonly": false, "status": true, "tags": ["remix_get_available_ingested_assets"]}, {"args": {}, "description": "Get the default output directory for non-ingested model assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **directory_path**: Path pointing to a directory\n\n  - **Example:**\n```json\n{\n  \"directory_path\": \"string\"\n}\n```", "display_description": "Get the default output directory for non-ingested model assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **directory_path**: Path pointing to a directory\n\n  - **Example:**\n```json\n{\n  \"directory_path\": \"string\"\n}\n```", "display_name": "remix_get_default_model_asset_directory", "name": "remix_get_default_model_asset_directory", "readonly": false, "status": true, "tags": ["remix_get_default_model_asset_directory"]}, {"args": {}, "description": "Get the default output directory for non-ingested texture assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **directory_path**: Path pointing to a directory\n\n  - **Example:**\n```json\n{\n  \"directory_path\": \"string\"\n}\n```", "display_description": "Get the default output directory for non-ingested texture assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **directory_path**: Path pointing to a directory\n\n  - **Example:**\n```json\n{\n  \"directory_path\": \"string\"\n}\n```", "display_name": "remix_get_default_texture_asset_directory", "name": "remix_get_default_texture_asset_directory", "readonly": false, "status": true, "tags": ["remix_get_default_texture_asset_directory"]}, {"args": {}, "description": "Get the default output directory for ingested assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **directory_path**: Path pointing to a directory\n\n  - **Example:**\n```json\n{\n  \"directory_path\": \"string\"\n}\n```", "display_description": "Get the default output directory for ingested assets.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **directory_path**: Path pointing to a directory\n\n  - **Example:**\n```json\n{\n  \"directory_path\": \"string\"\n}\n```", "display_name": "remix_get_default_ingested_asset_directory", "name": "remix_get_default_ingested_asset_directory", "readonly": false, "status": true, "tags": ["remix_get_default_ingested_asset_directory"]}, {"args": {"exists": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Filter an prim if it exists or not on a given layer. Use in conjunction with `layer_identifier` to filter on a given layer, otherwise this parameter will be ignored.", "title": "Exists"}, "filter_session_prims": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Filter out the prims that exist on the session layer or not", "title": "Filter Session Prims"}, "layer_identifier": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Look for prims that exists or not on a given layer. Use the `exists` query parameter to set whether existing or non-existing prims should be returned.", "title": "Layer Identifier"}, "prim_hashes": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": null, "description": "Filter prim paths to keep specific hashes", "title": "<PERSON><PERSON>"}, "prim_types": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": null, "description": "Filter prim paths to keep specific types of prims", "title": "Prim Types"}, "selection": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Select all prims (False) or the prims currently selected in the viewport (True)", "title": "Selection"}}, "description": "Get the the prim paths in the current stage.\n\n\n**Query Parameters:**\n\n- **prim_hashes**: Filter prim paths to keep specific hashes\n\n- **prim_types**: Filter prim paths to keep specific types of prims\n\n- **selection**: Select all prims (False) or the prims currently selected in the viewport (True)\n\n- **filter_session_prims**: Filter out the prims that exist on the session layer or not\n\n- **layer_identifier**: Look for prims that exists or not on a given layer. Use the `exists` query parameter to set whether existing or non-existing prims should be returned.\n\n- **exists**: Filter an prim if it exists or not on a given layer. Use in conjunction with `layer_identifier` to filter on a given layer, otherwise this parameter will be ignored.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get the the prim paths in the current stage.\n\n\n**Query Parameters:**\n\n- **prim_hashes**: Filter prim paths to keep specific hashes\n\n- **prim_types**: Filter prim paths to keep specific types of prims\n\n- **selection**: Select all prims (False) or the prims currently selected in the viewport (True)\n\n- **filter_session_prims**: Filter out the prims that exist on the session layer or not\n\n- **layer_identifier**: Look for prims that exists or not on a given layer. Use the `exists` query parameter to set whether existing or non-existing prims should be returned.\n\n- **exists**: Filter an prim if it exists or not on a given layer. Use in conjunction with `layer_identifier` to filter on a given layer, otherwise this parameter will be ignored.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_prim_paths", "name": "remix_get_prim_paths", "readonly": false, "status": true, "tags": ["remix_get_prim_paths"]}, {"args": {"prim_path": {"description": "The prim path to the asset that will be inspected for instances", "title": "Prim Path", "type": "string"}}, "description": "Get a given model's instances. The prim must be a model.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to the asset that will be inspected for instances\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get a given model's instances. The prim must be a model.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to the asset that will be inspected for instances\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_model_instances", "name": "remix_get_model_instances", "readonly": false, "status": true, "tags": ["remix_get_model_instances"]}, {"args": {"prim_path": {"description": "The prim path to the asset that will be inspected for textures", "title": "Prim Path", "type": "string"}, "texture_types": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": null, "description": "The type of textures to look for in the given material.", "title": "Texture Types"}}, "description": "Get a given material's textures. The prim must be a material.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to the asset that will be inspected for textures\n\n\n**Query Parameters:**\n\n- **texture_types**: The type of textures to look for in the given material.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **textures**: A list of prim paths (shader input paths) and their corresponding texture paths\n\n  - **Example:**\n```json\n{\n  \"textures\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get a given material's textures. The prim must be a material.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to the asset that will be inspected for textures\n\n\n**Query Parameters:**\n\n- **texture_types**: The type of textures to look for in the given material.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **textures**: A list of prim paths (shader input paths) and their corresponding texture paths\n\n  - **Example:**\n```json\n{\n  \"textures\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_material_textures", "name": "remix_get_material_textures", "readonly": false, "status": true, "tags": ["remix_get_material_textures"]}, {"args": {"prim_path": {"description": "The prim path to the asset that will be inspected for file paths", "title": "Prim Path", "type": "string"}}, "description": "Get a given prim's reference file paths.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to the asset that will be inspected for file paths\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **reference_paths**: A list of prim paths and their corresponding reference relative paths and layer identifiers. Combine the reference path's relative path with the layer identifier to get the absolute reference path.\n\n  - **Example:**\n```json\n{\n  \"reference_paths\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get a given prim's reference file paths.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to the asset that will be inspected for file paths\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **reference_paths**: A list of prim paths and their corresponding reference relative paths and layer identifiers. Combine the reference path's relative path with the layer identifier to get the absolute reference path.\n\n  - **Example:**\n```json\n{\n  \"reference_paths\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_prim_reference_file_paths", "name": "remix_get_prim_reference_file_paths", "readonly": false, "status": true, "tags": ["remix_get_prim_reference_file_paths"]}, {"args": {"asset_file_path": {"description": "The path to the asset to use as a reference", "title": "Asset File Path", "type": "string"}, "existing_asset_file_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "The relative path of the asset reference to replace", "title": "Existing Asset File Path"}, "existing_asset_layer_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "The layer identifier where the existing reference is located", "title": "Existing <PERSON><PERSON> Layer Id"}, "force": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Whether to force use the reference or validate the ingestion status", "title": "Force"}, "prim_path": {"description": "The prim path for which to replace a reference", "title": "Prim Path", "type": "string"}}, "description": "Replace a prim's reference file path. If no existing file path is provided, the first reference will be replaced.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path for which to replace a reference\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **reference_paths**: A list of prim paths and their corresponding reference relative paths and layer identifiers. Combine the reference path's relative path with the layer identifier to get the absolute reference path.\n\n  - **Example:**\n```json\n{\n  \"reference_paths\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Replace a prim's reference file path. If no existing file path is provided, the first reference will be replaced.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path for which to replace a reference\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **reference_paths**: A list of prim paths and their corresponding reference relative paths and layer identifiers. Combine the reference path's relative path with the layer identifier to get the absolute reference path.\n\n  - **Example:**\n```json\n{\n  \"reference_paths\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_replace_prim_reference_file_path", "name": "remix_replace_prim_reference_file_path", "readonly": false, "status": true, "tags": ["remix_replace_prim_reference_file_path"]}, {"args": {"asset_file_path": {"description": "The path to the asset to use as a reference", "title": "Asset File Path", "type": "string"}, "force": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Whether to force use the reference or validate the ingestion status", "title": "Force"}, "prim_path": {"description": "The prim path to append a reference to", "title": "Prim Path", "type": "string"}}, "description": "Append a new reference to a given prim.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to append a reference to\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **reference_paths**: A list of prim paths and their corresponding reference relative paths and layer identifiers. Combine the reference path's relative path with the layer identifier to get the absolute reference path.\n\n  - **Example:**\n```json\n{\n  \"reference_paths\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Append a new reference to a given prim.\n\n\n**Path Parameters:**\n\n- **prim_path** (Required): The prim path to append a reference to\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **reference_paths**: A list of prim paths and their corresponding reference relative paths and layer identifiers. Combine the reference path's relative path with the layer identifier to get the absolute reference path.\n\n  - **Example:**\n```json\n{\n  \"reference_paths\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_append_prim_reference_file_path", "name": "remix_append_prim_reference_file_path", "readonly": false, "status": true, "tags": ["remix_append_prim_reference_file_path"]}, {"args": {"prim_paths": {"description": "Comma-separated list of prim paths to select", "title": "Prim Paths", "type": "string"}}, "description": "Set the selection in the current stage.\n\n\n**Path Parameters:**\n\n- **prim_paths** (Required): Comma-separated list of prim paths to select\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Set the selection in the current stage.\n\n\n**Path Parameters:**\n\n- **prim_paths** (Required): Comma-separated list of prim paths to select\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_set_selection", "name": "remix_set_selection", "readonly": false, "status": true, "tags": ["remix_set_selection"]}, {"args": {"exists": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Filter an texture if it exists or not on a given layer. Use in conjunction with `layer_identifier` to filter on a given layer, otherwise this parameter will be ignored.", "title": "Exists"}, "filter_session_prims": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Filter out prims that exist on the session layer or not", "title": "Filter Session Prims"}, "layer_identifier": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Look for textures that exist or not on a given layer. Use the `exists` query parameter to set whether existing or non-existing textures should be returned.", "title": "Layer Identifier"}, "prim_hashes": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": null, "description": "Filter textures to keep textures from specific material hashes", "title": "<PERSON><PERSON>"}, "selection": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Select all prims (False) or the stage selection (True)", "title": "Selection"}, "texture_types": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": null, "description": "The types of textures to look for", "title": "Texture Types"}}, "description": "Get the texture properties and associated asset paths in the current stage.\n\n\n**Query Parameters:**\n\n- **prim_hashes**: Filter textures to keep textures from specific material hashes\n\n- **texture_types**: The types of textures to look for\n\n- **selection**: Select all prims (False) or the stage selection (True)\n\n- **filter_session_prims**: Filter out prims that exist on the session layer or not\n\n- **layer_identifier**: Look for textures that exist or not on a given layer. Use the `exists` query parameter to set whether existing or non-existing textures should be returned.\n\n- **exists**: Filter an texture if it exists or not on a given layer. Use in conjunction with `layer_identifier` to filter on a given layer, otherwise this parameter will be ignored.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **textures**: A list of prim paths (shader input paths) and their corresponding texture paths\n\n  - **Example:**\n```json\n{\n  \"textures\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get the texture properties and associated asset paths in the current stage.\n\n\n**Query Parameters:**\n\n- **prim_hashes**: Filter textures to keep textures from specific material hashes\n\n- **texture_types**: The types of textures to look for\n\n- **selection**: Select all prims (False) or the stage selection (True)\n\n- **filter_session_prims**: Filter out prims that exist on the session layer or not\n\n- **layer_identifier**: Look for textures that exist or not on a given layer. Use the `exists` query parameter to set whether existing or non-existing textures should be returned.\n\n- **exists**: Filter an texture if it exists or not on a given layer. Use in conjunction with `layer_identifier` to filter on a given layer, otherwise this parameter will be ignored.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **textures**: A list of prim paths (shader input paths) and their corresponding texture paths\n\n  - **Example:**\n```json\n{\n  \"textures\": [\n    [\n      \"example_item\"\n    ]\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_textures", "name": "remix_get_textures", "readonly": false, "status": true, "tags": ["remix_get_textures"]}, {"args": {"force": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "description": "Whether to replace a non-ingested asset or fail the validation instead", "title": "Force"}, "textures": {"description": "A list of prim paths (shader input paths) and their corresponding texture paths", "items": {"items": {}, "type": "array"}, "title": "Textures", "type": "array"}}, "description": "Override the given textures on the current edit target in the current stage.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Override the given textures on the current edit target in the current stage.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_override_textures", "name": "remix_override_textures", "readonly": false, "status": true, "tags": ["remix_override_textures"]}, {"args": {}, "description": "Get a list of the available texture types.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **texture_types**: A list of texture types\n\n  - **Example:**\n```json\n{\n  \"texture_types\": [\n    \"string\"\n  ]\n}\n```", "display_description": "Get a list of the available texture types.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **texture_types**: A list of texture types\n\n  - **Example:**\n```json\n{\n  \"texture_types\": [\n    \"string\"\n  ]\n}\n```", "display_name": "remix_get_valid_texture_types", "name": "remix_get_valid_texture_types", "readonly": false, "status": true, "tags": ["remix_get_valid_texture_types"]}, {"args": {"texture_prim_path": {"description": "The prim path of a given texture", "title": "Texture Prim Path", "type": "string"}}, "description": "Get the parent material for a given texture prim path.\n\n\n**Path Parameters:**\n\n- **texture_prim_path** (Required): The prim path of a given texture\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get the parent material for a given texture prim path.\n\n\n**Path Parameters:**\n\n- **texture_prim_path** (Required): The prim path of a given texture\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_texture_material", "name": "remix_get_texture_material", "readonly": false, "status": true, "tags": ["remix_get_texture_material"]}, {"args": {"texture_prim_path": {"description": "The prim path of a given texture", "title": "Texture Prim Path", "type": "string"}, "texture_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Get the expected input for a given texture type or based on an ingested texture's file name.", "title": "Texture Type"}}, "description": "Get the parent material inputs for a given texture prim path.\n\n\n**Path Parameters:**\n\n- **texture_prim_path** (Required): The prim path of a given texture\n\n\n**Query Parameters:**\n\n- **texture_type**: Get the expected input for a given texture type or based on an ingested texture's file name.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Get the parent material inputs for a given texture prim path.\n\n\n**Path Parameters:**\n\n- **texture_prim_path** (Required): The prim path of a given texture\n\n\n**Query Parameters:**\n\n- **texture_type**: Get the expected input for a given texture type or based on an ingested texture's file name.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n    - **prim_paths**: A list of prim paths\n\n  - **Example:**\n```json\n{\n  \"prim_paths\": [\n    \"string\"\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_get_texture_material_inputs", "name": "remix_get_texture_material_inputs", "readonly": false, "status": true, "tags": ["remix_get_texture_material_inputs"]}, {"args": {"queue_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "ID to describe which queue should be updated", "title": "Queue Id"}}, "description": "Update the mass validation schema. Can be used to update the validation progress from an external process.\n\n\n**Query Parameters:**\n\n- **queue_id**: ID to describe which queue should be updated\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Update the mass validation schema. Can be used to update the validation progress from an external process.\n\n\n**Query Parameters:**\n\n- **queue_id**: ID to describe which queue should be updated\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Example:**\n```json\n\"string\"\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_update_ingestion_schema", "name": "remix_update_ingestion_schema", "readonly": false, "status": false, "tags": ["remix_update_ingestion_schema"]}, {"args": {"check_plugins": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": [{"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true}, "name": "ClearUnassignedMaterial", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "include_geom_subset": true, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllMeshes"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "context_name": "", "cook_mass_template": false, "default_material_mdl_name": "OmniPBR", "default_material_mdl_url": "OmniPBR.mdl", "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true}, "name": "DefaultMaterial", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "include_geom_subset": false, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllMeshes"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "ignore_not_convertable_shaders": false, "progress": [0, "Initializing", true], "save_on_fix_failure": true, "shader_subidentifiers": {"AperturePBR_Opacity": ".*", "AperturePBR_Translucent": "translucent|glass|trans"}}, "name": "MaterialShaders", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllMaterials"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"attributes": {"inputs:emissive_intensity": [{"input_value": 10000, "operator": "=", "output_value": 1}]}, "channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true}, "name": "ValueMapping", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllShaders"}]}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "conversion_args": {"inputs:normalmap_texture": {"encoding_attr": "inputs:encoding", "replace_suffix": "_Normal", "suffix": "_OTH_Normal"}}, "cook_mass_template": false, "data_flows": [{"channel": "cleanup_files", "name": "InOutData", "push_input_data": true, "push_output_data": true}], "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "replace_udim_textures_by_empty": true, "save_on_fix_failure": true}, "name": "ConvertToOctahedral", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllShaders"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "conversion_args": {"inputs:diffuse_texture": {"args": ["--format", "bc7", "--mip-gamma-correct"]}, "inputs:emissive_mask_texture": {"args": ["--format", "bc7", "--mip-gamma-correct"]}, "inputs:height_texture": {"args": ["--format", "bc4", "--no-mip-gamma-correct", "--mip-filter", "max"]}, "inputs:metallic_texture": {"args": ["--format", "bc4", "--no-mip-gamma-correct"]}, "inputs:normalmap_texture": {"args": ["--format", "bc5", "--no-mip-gamma-correct"]}, "inputs:reflectionroughness_texture": {"args": ["--format", "bc4", "--no-mip-gamma-correct"]}, "inputs:transmittance_texture": {"args": ["--format", "bc7", "--mip-gamma-correct"]}}, "cook_mass_template": false, "data_flows": [{"channel": "cleanup_files", "name": "InOutData", "push_input_data": true, "push_output_data": true}, {"channel": "write_metadata", "name": "InOutData", "push_input_data": false, "push_output_data": true}], "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "replace_udim_textures_by_empty": true, "save_on_fix_failure": true, "suffix": ".rtex.dds"}, "name": "ConvertToDDS", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllShaders"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true}, "name": "RelativeAssetPaths", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllPrims"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_dependency_between_round": true, "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_all_layers_on_exit": true}, "name": "DependencyIterator"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true}, "name": "RelativeReferences", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllPrims"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_on_exit": true}, "name": "CurrentStage"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true, "set_default_prim": true, "wrap_prim_name": "XForms"}, "name": "WrapRootPrims", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "Nothing"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_on_exit": true}, "name": "CurrentStage"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": true, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true, "scale_target": 1}, "name": "ApplyUnitScale", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false, "select_session_layer_prims": false}, "name": "RootPrims"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_on_exit": true}, "name": "CurrentStage"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true, "set_default_prim": true, "wrap_prim_name": "ReferenceTarget"}, "name": "WrapRootPrims", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "Nothing"}], "stop_if_fix_failed": true}], "title": "Check Plugins"}, "context_plugin": {"anyOf": [{"$ref": "#/$defs/AnonModel0"}, {"type": "null"}], "default": {"data": {"allow_empty_input_files_list": true, "bake_material": false, "baking_scales": false, "channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": true, "context_name": "ingestcraft", "convert_fbx_to_y_up": false, "convert_fbx_to_z_up": false, "convert_stage_up_y": false, "convert_stage_up_z": false, "cook_mass_template": true, "create_context_if_not_exist": true, "create_output_directory_if_missing": true, "create_world_as_default_root_prim": true, "data_flows": [{"channel": "write_metadata", "name": "InOutData", "push_input_data": true, "push_output_data": true}, {"channel": "ingestion_output", "name": "InOutData", "push_input_data": false, "push_output_data": true}], "default_output_endpoint": "/stagecraft/assets/default-directory", "disabling_instancing": false, "embed_mdl_in_usd": true, "embed_textures": true, "export_hidden_props": false, "export_mdl_gltf_extension": false, "export_preview_surface": false, "export_separate_gltf": false, "expose_mass_queue_action_ui": true, "expose_mass_ui": true, "full_path_keep": false, "global_progress_value": 0, "hide_context_ui": true, "ignore_animations": false, "ignore_camera": false, "ignore_flip_rotations": false, "ignore_light": false, "ignore_materials": false, "ignore_pivots": false, "ignore_unbound_bones": false, "input_files": [], "keep_all_materials": false, "merge_all_meshes": false, "output_directory": "", "output_usd_extension": "usd", "progress": [0, "Initializing", true], "single_mesh": false, "smooth_normals": true, "support_point_instancer": false, "use_double_precision_to_usd_transform_op": false, "use_meter_as_world_unit": false}, "name": "AssetImporter"}}, "executor": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": 1, "title": "Executor"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "Model(s)", "title": "Name"}, "resultor_plugins": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": [{"data": {"channel": "cleanup_files", "cleanup_input": true, "cleanup_output": false, "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true]}, "name": "FileCleanup"}, {"data": {"channel": "write_metadata", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true]}, "name": "FileMetadataWritter"}], "title": "Resultor Plugins"}}, "description": "Add an item to the mass validation queue.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"completed_schemas\": [\n    {\n      \"key\": \"value\"\n    }\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Add an item to the mass validation queue.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"completed_schemas\": [\n    {\n      \"key\": \"value\"\n    }\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_ingest_model_asset", "name": "remix_ingest_model_asset", "readonly": false, "status": true, "tags": ["remix_ingest_model_asset"]}, {"args": {"check_plugins": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": [{"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_on_exit": false}, "name": "CurrentStage"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "ignore_not_convertable_shaders": false, "progress": [0, "Initializing", true], "save_on_fix_failure": true, "shader_subidentifiers": {"AperturePBR_Opacity": ".*"}}, "name": "MaterialShaders", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllMaterials"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_on_exit": false}, "name": "CurrentStage"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "conversion_args": {"inputs:normalmap_texture": {"encoding_attr": "inputs:encoding", "replace_suffix": "_Normal", "suffix": "_OTH_Normal"}}, "cook_mass_template": false, "data_flows": [{"channel": "cleanup_files_normal", "name": "InOutData", "push_input_data": true, "push_output_data": true}], "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "replace_udim_textures_by_empty": false, "save_on_fix_failure": true}, "name": "ConvertToOctahedral", "resultor_plugins": [{"data": {"channel": "cleanup_files_normal", "cleanup_input": true, "cleanup_output": false, "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true]}, "name": "FileCleanup"}], "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllShaders"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_on_exit": false}, "name": "CurrentStage"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "conversion_args": {"inputs:diffuse_texture": {"args": ["--format", "bc7", "--mip-gamma-correct"]}, "inputs:emissive_mask_texture": {"args": ["--format", "bc7", "--mip-gamma-correct"]}, "inputs:height_texture": {"args": ["--format", "bc4", "--no-mip-gamma-correct", "--mip-filter", "max"]}, "inputs:metallic_texture": {"args": ["--format", "bc4", "--no-mip-gamma-correct"]}, "inputs:normalmap_texture": {"args": ["--format", "bc5", "--no-mip-gamma-correct"]}, "inputs:reflectionroughness_texture": {"args": ["--format", "bc4", "--no-mip-gamma-correct"]}, "inputs:transmittance_texture": {"args": ["--format", "bc7", "--mip-gamma-correct"]}}, "cook_mass_template": false, "data_flows": [{"channel": "cleanup_files", "name": "InOutData", "push_input_data": true, "push_output_data": true}, {"channel": "write_metadata", "name": "InOutData", "push_input_data": false, "push_output_data": true}, {"channel": "ingestion_output", "name": "InOutData", "push_input_data": false, "push_output_data": true}], "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "replace_udim_textures_by_empty": false, "save_on_fix_failure": true, "suffix": ".rtex.dds"}, "name": "ConvertToDDS", "resultor_plugins": [{"data": {"channel": "cleanup_files", "cleanup_input": true, "cleanup_output": false, "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true]}, "name": "FileCleanup"}], "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "AllShaders"}], "stop_if_fix_failed": true}, {"context_plugin": {"data": {"channel": "<PERSON><PERSON><PERSON>", "close_stage_on_exit": false, "cook_mass_template": false, "create_context_if_not_exist": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "hide_context_ui": false, "progress": [0, "Initializing", true], "save_on_exit": false}, "name": "CurrentStage"}, "data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": true, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "save_on_fix_failure": true}, "name": "MassTexturePreview", "selector_plugins": [{"data": {"channel": "<PERSON><PERSON><PERSON>", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true], "select_from_root_layer_only": false}, "name": "Nothing"}], "stop_if_fix_failed": true}], "title": "Check Plugins"}, "context_plugin": {"anyOf": [{"$ref": "#/$defs/AnonModel0"}, {"type": "null"}], "default": {"data": {"allow_empty_input_files_list": true, "channel": "<PERSON><PERSON><PERSON>", "context_name": "ingestcraft", "cook_mass_template": true, "create_context_if_not_exist": true, "create_output_directory_if_missing": true, "data_flows": [{"channel": "<PERSON><PERSON><PERSON>", "name": "InOutData", "push_input_data": true, "push_output_data": false}], "default_output_endpoint": "/stagecraft/assets/default-directory", "expose_mass_queue_action_ui": false, "expose_mass_ui": true, "global_progress_value": 0, "hide_context_ui": true, "input_files": [], "output_directory": "", "progress": [0, "Initializing", true]}, "name": "TextureImporter"}}, "executor": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": 1, "title": "Executor"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "Material(s)", "title": "Name"}, "resultor_plugins": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": [{"data": {"channel": "write_metadata", "cook_mass_template": false, "expose_mass_queue_action_ui": false, "expose_mass_ui": false, "global_progress_value": 0, "progress": [0, "Initializing", true]}, "name": "FileMetadataWritter"}], "title": "Resultor Plugins"}}, "description": "Add an item to the mass validation queue.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"completed_schemas\": [\n    {\n      \"key\": \"value\"\n    }\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_description": "Add an item to the mass validation queue.\n\n\n**Responses:**\n\n- **200** (Success): Successful Response\n  - Content-Type: `application/lightspeed.remix.service+json; version=1.0`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"completed_schemas\": [\n    {\n      \"key\": \"value\"\n    }\n  ]\n}\n```\n\n- **422**: Validation Error\n  - Content-Type: `application/json`\n\n  - **Response Properties:**\n\n  - **Example:**\n```json\n{\n  \"detail\": [\n    \"unknown_type\"\n  ]\n}\n```", "display_name": "remix_ingest_material_asset", "name": "remix_ingest_material_asset", "readonly": false, "status": true, "tags": ["remix_ingest_material_asset"]}]}}, "tool_mode": true}, "showNode": true, "type": "MCPTools"}, "dragging": false, "id": "MCPTools-beHVJ", "measured": {"height": 455, "width": 320}, "position": {"x": -350.67957241638305, "y": 418.18424520697226}, "selected": true, "type": "genericNode"}], "viewport": {"x": 1262.8270031092698, "y": 533.860647282749, "zoom": 0.639257691715369}}, "description": "Agent to integrate the NVIDIA RTX Remix Toolkit REST API and RTX Remix Documentation", "endpoint_name": null, "id": "faac3377-a15b-459b-a6bd-c47c318dcc78", "is_component": false, "last_tested_version": "1.4.2", "name": "NVIDIA RTX Remix", "tags": ["agents"]}