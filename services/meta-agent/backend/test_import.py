import sys
print("Testing backend startup...")

try:
    print("1. Loading environment...")
    from dotenv import load_dotenv
    load_dotenv()
    print("   ✓ Environment loaded")
except Exception as e:
    print(f"   ✗ Failed: {e}")
    sys.exit(1)

try:
    print("2. Importing settings...")
    from src.config.settings import settings
    print("   ✓ Settings loaded")
    print(f"   Port: {settings.port}")
    print(f"   Host: {settings.host}")
except Exception as e:
    print(f"   ✗ Failed: {e}")
    sys.exit(1)

try:
    print("3. Testing database connection string...")
    print(f"   Database URL configured: {'DATABASE_URL' in os.environ if 'os' in locals() else 'Unknown'}")
except Exception as e:
    print(f"   ✗ Failed: {e}")

try:
    print("4. Importing main app...")
    from src.main import app
    print("   ✓ FastAPI app created")
except Exception as e:
    print(f"   ✗ Failed: {e}")
    import traceback
    traceback.print_exc()
